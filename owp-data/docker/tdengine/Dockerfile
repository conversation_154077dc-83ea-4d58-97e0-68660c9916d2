# 构建镜像，执行命令：【docker build -t td-client .】
FROM openjdk:8u342
MAINTAINER Liyohe

# 时区问题
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' >/etc/timezone

# 安装 taosc 根据自己的CPU架构(arm64/x64)选择软件包
ENV TD_VERSION 3.3.5.8
ENV ARCH arm64

WORKDIR /root
RUN apt-get update && apt-get install -y wget && apt-get clean && rm -rf /var/lib/apt/lists/*  \
    && wget https://www.taosdata.com/assets-download/3.0/TDengine-client-${TD_VERSION}-Linux-${ARCH}.tar.gz && tar -xzf TDengine-client-${TD_VERSION}-Linux-${ARCH}.tar.gz \
    && cd TDengine-client-${TD_VERSION} && ./install_client.sh && rm -rf /root/TDengine-*
