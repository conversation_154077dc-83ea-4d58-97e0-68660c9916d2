spring:
  application:
    name: owp-data
  cloud:
    nacos:
      discovery:
        server-addr: 172.28.40.68:8848
        username: nacos
        password: nacos
        group: OWP_GROUP
      config:
        server-addr: 172.28.40.68:8848
        file-extension: yml
        username: nacos
        password: nacos
        group: OWP_GROUP
        shared-configs[0]:
          data-id: commom-redis.yml
          group: DEFAULT_GROUP
        shared-configs[1]:
          data-id: common-mysql.yml
          group: DEFAULT_GROUP
        shared-configs[2]:
          data-id: common-mybatis.yml
          group: DEFAULT_GROUP
        shared-configs[3]:
          data-id: common-config.yml
          group: DEFAULT_GROUP
