/** 生成正常图片 */
const echarts = require('echarts')
const { createCanvas } = require('canvas')
const { run, get_extreme_value, auto_compute_y } = require('./X')
const internal = require('stream')

function main() {
    /** echart版本必须得4.9.0,不然有时间显示bug */
    let start_time = Date.now()
    const handle_error = msg => {
        throw new Error(msg)
    }
    let path

    const args = process.argv
    const ind = args.findIndex(item => item == '--input')
    if (ind != -1 && args.length > ind + 1) {
        path = args.at(ind + 1)
    } else {
        handle_error('参数配置不正确')
        return
    }

    /** @读取到option */
    const option = JSON.parse(require('fs').readFileSync(require('path').resolve(path), 'utf-8'))

    let y_line_left = null
    let y_line_right = null

    /** @判断Y轴的形式 */
    if (Array.isArray(option.y)) {
        if (option.y.length == 2) {
            y_line_left = option.y.at(0)
            y_line_right = option.y.at(1)
        } else if (option.y.length == 1) {
            y_line_left = option.y.at(0)
        } else {
            handle_error('Y轴的配置不正确')
        }
    } else {
        handle_error('Y轴的配置不正确')
    }

    /** @判断是不是两条数据 依据:series是否含有yAxisIndex:1 */

    let option_line_left = {} //最终Y轴左边的配置
    let option_line_right = {} //最终Y轴右边的配置

    let double_length = option.series?.some(item => item.yAxisIndex == 1) ? true : false

    if (double_length && y_line_left && y_line_right) {
        /** @case 两条轴的情况 */
        if (y_line_left.auto) {
            let { min, max } = get_extreme_value(
                option.series.filter(item => item.yAxisIndex === 0),
                true
            )
            if (min == 'null' || max == 'null') {
                option_line_left = { splitNumber: y_line_left.splitnumber }
            } else {
                const { comp_min, comp_max, comp_interval } = auto_compute_y(
                    max,
                    min,
                    y_line_left.splitnumber
                )
                option_line_left = { min: comp_min, max: comp_max, interval: comp_interval }
            }
        } else {
            option_line_left = {
                min: y_line_left.min,
                max: y_line_left.max,
                interval: y_line_left.interval,
            }
        }
        if (y_line_right.auto) {
            let { min, max } = get_extreme_value(
                option.series.filter(item => item.yAxisIndex === 1),
                true
            )
            if (min == 'null' || max == 'null') {
                option_line_right = { splitNumber: y_line_right.splitnumber }
            } else {
                const { comp_min, comp_max, comp_interval } = auto_compute_y(
                    max,
                    min,
                    y_line_right.splitnumber
                )
                option_line_right = { min: comp_min, max: comp_max, interval: comp_interval }
            }
        } else {
            option_line_right = {
                min: y_line_right.min,
                max: y_line_right.max,
                interval: y_line_right.interval,
            }
        }
    } else if (!double_length && (y_line_left || y_line_right)) {
        /** @case 只显示第一条轴 */
        if (y_line_left.auto) {
            /** @是auto */
            let { min, max } = get_extreme_value(option.series, true)
            if (min == 'null' || max == 'null') {
                option_line_left = { splitNumber: y_line_left.splitnumber }
            } else {
                const { comp_min, comp_max, comp_interval } = auto_compute_y(
                    max,
                    min,
                    y_line_left.splitnumber
                )
                option_line_left = { min: comp_min, max: comp_max, interval: comp_interval }
            }
        } else {
            /** @不是auto */
            option_line_left = {
                min: y_line_left.min,
                max: y_line_left.max,
                interval: y_line_left.interval,
            }
        }
    } else if (double_length && (y_line_left || y_line_right)) {
        handle_error('Y轴的配置不正确')
    }

    /** 更改颜色序列 */
    const colors = []
    new Set([
        ...option.series.map(item => item?.lineStyle?.color ?? null).filter(item => item),
        '#3070b7',
        '#56bbaa',
        '#d48265',
        '#91c7ae',
        '#c23531',
        '#61a0a8',
        '#ca8622',
        '#749f83',
        '#bda29a',
        '#6e7074',
        '#546570',
        '#c4ccd3',
    ]).forEach(item => {
        colors.push(item)
    })

    /** @配置 dotline 信息 */
    let chart_series = option.series
    if (option.dotline && Array.isArray(option.dotline) && option.dotline.length > 0)
        chart_series.forEach(item => {
            item['markLine'] = {
                symbol: 'none',
                data: option.dotline.map(it => {
                    return {
                        yAxis: String(it.position), // 设置分隔线的位置
                        lineStyle: {
                            color: 'red',
                            type: 'dotted',
                        },
                        label: {
                            show: true,
                            color: '#000',
                            position: 'end',
                            formatter: it.label,
                            fontFamily: 'Times New Roman',
                        },
                    }
                }),
            }
        })

    /** 最终的chart配置  */
    const chart_option = {
        backgroundColor: '#ffffff',
        color: colors,
        /** X,Y轴的标题 */
        title: [
            ...option.y.map((i, index) => {
                item = i.name
                return {
                    text: item,
                    textStyle: {
                        color: 'black',
                        fontWeight: 'bold',
                        fontFamily: 'Times New Roman',
                        fontSize: option.fontsize * 1.25,
                    },
                    top: '4%',
                    ...(index === 0 ? { left: '2.5%' } : { right: '2.5%' }),
                }
            }),
            {
                text: option.x.name, //
                textStyle: {
                    color: 'black',
                    fontWeight: 'bold',
                    fontFamily: 'Times New Roman',
                    fontSize: option.fontsize * 1.25,
                },
                right: '2%',
                bottom: '2%',
            },
        ],

        /** 整体布局 */
        grid: {
            show: true,
            left: option.width > 926 ? '7%' : '8%',
            right:
                option.width > 926
                    ? option.y.length > 1
                        ? '7%'
                        : '5%'
                    : option.y.length > 1
                        ? '8%'
                        : '5%',
            borderColor: '#333',
        },

        /** 图例 */
        legend: {
            top: '4%',
            ...(option.linelegend ? { icon: 'line' } : {}), //是否显示直线-优先级高
            itemWidth: 50,
            itemHeight: 10,
            textStyle: {
                fontFamily: 'Times New Roman',
                fontWeight: 'bold',
                fontSize: option.fontsize * 1.25,
                color: '#333',
            },
        },

        xAxis: [
            /**  下X轴(主X轴) */
            {
                position: 'bottom',
                type: 'category',
                boundaryGap: false,

                data: option.x.data,
                /** 刻度线 */
                axisTick: {
                    length: 8,
                    show: true,
                    inside: true,
                    alignWithLabel: true
                },

                /** 轴线 */
                axisLine: {
                    show: true,
                    onZero: false,
                    lineStyle: {
                        color: '#333',
                    },
                },

                /** 轴标签 */
                axisLabel: {
                    showMaxLabel: true, //显示尾轴
                    showMinLabel: true, //显示头轴
                    textStyle: {
                        fontFamily: 'Times New Roman',
                        fontSize: option.fontsize,
                        color: 'black',
                    },
                },

                /** 网格线 */
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#ccc',
                    },
                },
            },
            /**  上X轴(副X轴,不用于显示) */
            {
                position: 'top',
                type: 'category',
                boundaryGap: false,
                /** @note 此处数据要和上面保持一致才能显示刻度线 */
                axisTick: {
                    length: 8,
                    show: true,
                    inside: true,
                    alignWithLabel: true
                },
                axisLine: {
                    show: true,
                    onZero: false,
                    lineStyle: {
                        color: '#333',
                    },
                },
                axisLabel: {
                    show: false,
                    position: 'top',
                    showMaxLabel: true, //显示尾轴
                    showMinLabel: true, //显示头轴
                    textStyle: {
                        fontFamily: 'Times New Roman',
                        fontSize: 16,
                        color: '#333',
                    },
                },
            },
        ],

        yAxis: [
            /** 左Y轴 */
            {
                ...option_line_left,
                type: 'value',
                /** 刻度线 */
                axisTick: {
                    length: 8,
                    show: true,
                    inside: true,
                },
                /** 轴线 */
                axisLine: {
                    lineStyle: {
                        color: '#333',
                    },
                },
                /** 轴标签 */
                axisLabel: {
                    textStyle: {
                        margin: 4,
                        fontFamily: 'Times New Roman',
                        fontSize: 16,
                        color: 'black',
                    },
                },
                /** 网格线 */
                splitLine: {
                    lineStyle: {
                        color: '#ccc',
                    },
                },
            },
            /** 右Y轴 */
            {
                /** @note 此处数据要和上面保持一致才能显示刻度线 */
                alignTicks: true, //自动对齐
                ...option_line_right,
                type: 'value',

                axisTick: {
                    length: 8,
                    show: true,
                    inside: true,
                },
                axisLine: {
                    lineStyle: {
                        color: '#333',
                    },
                },
                axisLabel: {
                    margin: 4,
                    show: option.y.length > 1,
                    textStyle: {
                        fontFamily: 'Times New Roman',
                        fontSize: option.fontsize,
                        color: 'black',
                    },
                },
                splitLine: {
                    lineStyle: {
                        color: '#ccc',
                    },
                },
            },
        ],
        /** 数据显示列 */
        series: [...chart_series],
    }

    /** 改写Canvas创建函数 */
    echarts.setCanvasCreator(() => {
        return createCanvas(option.width, option.height)
    })

    /** 渲染函数 */
    function render_chart() {
        const canvas = createCanvas(Number(option.width), Number(option.height))

        const PixelRatio = 4 //图片压缩比,决定清晰度
        const BorderWidth = 2 //边框宽度
        const BorderColor = '#333' //边框颜色

        const chart = echarts.init(
            canvas,
            {},
            {
                width: option.width,
                height: option.height,
                devicePixelRatio: PixelRatio,
            }
        )
        chart.setOption(chart_option)

        const ctx = canvas.getContext('2d')
        ctx.strokeStyle = BorderColor
        ctx.lineWidth = BorderWidth
        ctx.strokeRect(
            0,
            0,
            option.width * PixelRatio - BorderWidth * 2,
            option.height * PixelRatio - BorderWidth * 2
        )

        return canvas
    }

    /** 将canvas存储为图片 */
    function save_image(canvas) {
        const fs = require('fs')
        const out = fs.createWriteStream(require('path').resolve(option.output || './'))
        const stream = canvas.createPNGStream({
            compressionLevel: 5,
            resolution: 0,
        })
        stream.pipe(out)
        out.on('finish', () => {
            console.log(
                `${Date.now() - start_time}ms⌛️`,
                '保存成功🔥 :',
                require('path').resolve(option.output)
            )
            process.exit()
        })
    }

    /** 运行 */
    save_image(render_chart())
}

main()
