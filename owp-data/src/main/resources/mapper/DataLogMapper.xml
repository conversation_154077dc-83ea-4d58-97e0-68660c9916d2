<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.DataLogMapper">

    <select id="list" resultType="com.hdec.data.domain.DataLog">
        SELECT
            *
        FROM
            owp_data_log
        WHERE
            field_num = #{fieldNum}
        AND `type` = #{qo.type}
        <if test="qo.startTime != null and qo.startTime != ''">
            AND `time` BETWEEN #{qo.startTime} AND CONCAT(#{qo.endTime}, ' 23:59:59')
        </if>
        <if test="qo.usernameKw != null and qo.usernameKw != ''">
            AND `username` LIKE CONCAT('%', #{qo.usernameKw}, '%')
        </if>
        <if test="qo.pointNoKw != null and qo.pointNoKw != ''">
            AND `point_no` LIKE CONCAT('%', #{qo.pointNoKw}, '%')
        </if>
        ORDER BY `operate_time` DESC
    </select>

    <insert id="save" parameterType="com.hdec.data.domain.DataLog">
        INSERT INTO owp_data_log (
            rate, point_id, point_no, direction, attr_id, attr_name, operate_time, `time`,
            before_val, after_val, user_id, username, `type`, del_reason, del_data_json, del_data, field_num
        )
        VALUES
            (#{log.rate}, #{log.pointId}, #{log.pointNo}, #{log.direction}, #{log.attrId}, #{log.attrName}, NOW(), #{log.time},
            #{log.beforeVal}, #{log.afterVal}, #{log.userId}, #{log.username}, #{log.type}, #{log.delReason}, #{log.delDataJson}, #{log.delData}, #{log.fieldNum})
    </insert>

    <delete id="delById">
        DELETE
        FROM
            owp_data_log
        WHERE
            id = #{id}
    </delete>

    <select id="getById" resultType="com.hdec.data.domain.DataLog">
        SELECT
            *
        FROM
            owp_data_log
        WHERE
            id = #{id}
    </select>


</mapper>