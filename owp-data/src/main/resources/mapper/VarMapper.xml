<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.VarMapper">

    <select id="list" resultType="com.hdec.data.domain.template.ReportVar">
        SELECT
            id, `name`, `type`
        FROM
            owp_report_var
        WHERE
            `type` IN ('base', 'multi', 'chartNum')
    </select>

    <select id="select" resultType="com.hdec.data.domain.template.ReportVar">
        SELECT
            *
        FROM
            owp_report_var
        WHERE
            `name` = #{var.name}
        AND
            field_num = #{var.fieldNum}
    </select>

    <select id="save" parameterType="com.hdec.data.domain.template.ReportVar">
        INSERT INTO owp_report_var (
            `name`, `type`, inst_id, param_json, field_num
        )
        VALUES
            (#{var.name}, #{var.type}, #{var.instId}, #{var.paramJson}, #{var.fieldNum})
    </select>

    <delete id="delete">
        DELETE
        FROM
            owp_report_var
        WHERE
            id IN
            <foreach collection="ids" item="id" separator=", " open="(" close=")">
                #{id}
            </foreach>
    </delete>

    <select id="getCustomVars" resultType="com.hdec.data.domain.template.ReportVar">
        SELECT
            *
        FROM
            owp_report_var
        WHERE
            field_num = #{fieldNum} AND `type` NOT IN ('base', 'multi', 'chartNum')
    </select>

    <select id="getPointIdsByVarName" resultType="com.hdec.data.domain.template.ReportVar">
        SELECT
            *
        FROM
            owp_report_var
        WHERE
            `name` = #{varName}
        AND `field_num` = #{fieldNum}
        AND `type` NOT IN ('base', 'multi', 'chartNum')
        limit 1
    </select>

    <update id="update" parameterType="com.hdec.data.domain.template.ReportVar">
        UPDATE
            owp_report_var
        <set>
            <if test="var.name != null">`name` = #{var.name},</if>
            <if test="var.type != null">`type` = #{var.type},</if>
            <if test="var.type != null">`inst_id` = #{var.instId},</if>
            <if test="var.paramJson != null">`param_json` = #{var.paramJson},</if>
        </set>
        WHERE
            id = #{var.id}
    </update>

</mapper>