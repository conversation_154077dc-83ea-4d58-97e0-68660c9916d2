<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.ReportMouldMapper">

    <select id="list" resultType="com.hdec.data.domain.template.Mould">
        SELECT
            *
        FROM
            owp_report_mould
        <where>
            field_num = #{fieldNum}
            <if test="name != null and name != ''">
                AND `name` LIKE CONCAT('%', #{name} ,'%')
            </if>
            <if test="type != null and type != ''">
                AND `type` = #{type}
            </if>
            <if test="label != null and label != ''">
                AND `label` LIKE CONCAT('%', #{label} ,'%')
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <select id="listByField" resultType="com.hdec.data.domain.template.Mould">
        SELECT
            `id`, `name`, `type`, `update_time`, `username`, `label`, `field_num`
        FROM
            owp_report_mould
        WHERE
            field_num = #{fieldNum}
        ORDER BY id DESC
    </select>

    <select id="selectByName" resultType="com.hdec.data.domain.template.Mould">
        SELECT
            id
        FROM
            owp_report_mould
        WHERE
            field_num = #{fieldNum} AND `name` = #{name}
        <if test="id != null"> AND id != #{id}</if>
        LIMIT 1
    </select>

    <select id="getById" resultType="com.hdec.data.domain.template.Mould">
        SELECT
            *
        FROM
            owp_report_mould
        WHERE
            id = #{id}
    </select>

    <select id="getOutlineIdByMouldId" resultType="int">
        SELECT
            outline_id
        FROM
            owp_report_nav_mould
        WHERE
            mould_id = #{mouldId}
    </select>

    <select id="getOutlineByMouldIds" resultType="com.hdec.data.domain.Outline">
        SELECT
            a.`name` AS navName,
            b.mould_id,
            c.`name`
        FROM
            owp_report_outline_nav a
        INNER JOIN owp_report_nav_mould b ON a.id = b.nav_id
        LEFT JOIN owp_report_outline c ON a.outline_id = c.id
        WHERE
            b.mould_id IN
        <foreach collection="mouldIds" item="mouldId" separator=", " open="(" close=")">
            #{mouldId}
        </foreach>
    </select>

    <select id="getByIds" resultType="com.hdec.data.domain.template.Mould">
        SELECT
            *
        FROM
            owp_report_mould
        WHERE
        id IN
        <foreach collection="mouldIds" item="mouldId" separator=", " open="(" close=")">
            #{mouldId}
        </foreach>
    </select>

    <delete id="delete">
        DELETE
        FROM
            owp_report_mould
        WHERE
            id IN
            <foreach collection="ids" item="id" separator=", " open="(" close=")">
                #{id}
            </foreach>
    </delete>

    <update id="update" parameterType="com.hdec.data.domain.template.Mould">
        UPDATE owp_report_mould
        <set>
            `update_time` = NOW(),
            <if test="mould.name != null">`name` = #{mould.name},</if>
            <if test="mould.type != null">`type` = #{mould.type},</if>
            <if test="mould.userId != null">`user_id` = #{mould.userId},</if>
            <if test="mould.username != null">`username` = #{mould.username},</if>
            <if test="mould.label != null">`label` = #{mould.label},</if>
            <if test="mould.content != null">`content` = #{mould.content},</if>
        </set>
        WHERE
            id = #{mould.id}
    </update>

    <select id="getAllMouldLabels" resultType="string">
        SELECT
            label
        FROM
            owp_report_mould
        WHERE
            field_num = #{fieldNum}
            AND label IS NOT NULL AND label != ''
    </select>

    <insert id="add" parameterType="com.hdec.data.domain.template.Mould" useGeneratedKeys="true" keyProperty="id" >
        INSERT INTO owp_report_mould (
            `name`,
            `type`,
            create_time,
            update_time,
            user_id,
            username,
            label,
            content,
            field_num
        )
        VALUES (
            #{mould.name},
            #{mould.type},
            NOW(),
            NOW(),
            #{mould.userId},
            #{mould.username},
            #{mould.label},
            #{mould.content},
            #{mould.fieldNum}
        )
    </insert>

    <insert id="adds" parameterType="com.hdec.data.domain.template.Mould">
        INSERT INTO owp_report_mould (
            `name`,
            `type`,
            create_time,
            update_time,
            user_id,
            username,
            label,
            content,
            field_num
        ) VALUES
        <foreach collection="moulds" separator="," item="mould">
            (
                #{mould.name},
                #{mould.type},
                NOW(),
                #{mould.updateTime},
                #{mould.userId},
                #{mould.username},
                #{mould.label},
                #{mould.content},
                #{mould.fieldNum}
            )
        </foreach>
    </insert>

</mapper>