<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.TaskMapper">

    <select id="list" resultType="com.hdec.data.domain.Task">
        SELECT
            `id`, `module`, `action`, `type`, `level`, `rate`, `user_id`, `username`, `status`, `err_msg`, `process`, `create_time`, `re_start_time`, `re_end_time`, `start_time`, `finish_time`, `field_num`
        FROM
            owp_task
        WHERE
            field_num IN
            <foreach item="fieldNum" collection="fieldNums" open="(" separator="," close=")">
                #{fieldNum}
            </foreach>
        ORDER BY `create_time` DESC
    </select>

    <select id="selectTopN" resultType="com.hdec.data.domain.Task">
        SELECT
            *
        FROM
            owp_task
        WHERE
            status = 0
        ORDER BY create_time
        LIMIT #{N}
    </select>

    <update id="resetTaskStatus">
        UPDATE owp_task
        SET
            `status` = 0
        WHERE
            `status` = 1
    </update>

    <insert id="saveTask" parameterType="com.hdec.data.domain.Task" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO owp_task (
            `type`, `level`, params, rate, user_id, username, status, process, create_time, start_time, finish_time, field_num, re_start_time, re_end_time
        )
        VALUES
            (#{task.type}, #{task.level}, #{task.params}, #{task.rate}, #{task.userId}, #{task.username}, #{task.status}, #{task.process}, #{task.createTime}, #{task.startTime}, #{task.finishTime}, #{task.fieldNum}, #{task.reStartTime}, #{task.reEndTime})
    </insert>

    <insert id="saveTasks" parameterType="com.hdec.data.domain.Task" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO owp_task (
            `type`, `level`, params, rate, user_id, username, status, process, create_time, start_time, finish_time, field_num
        )
        VALUES
        <foreach collection="points" item="point" separator="," >
            (#{task.type}, #{task.level}, #{task.params}, #{task.rate}, #{task.userId}, #{task.username}, #{task.status}, #{task.process}, #{task.createTime}, #{task.startTime}, #{task.finishTime}, #{task.fieldNum})
        </foreach>
    </insert>

    <update id="update">
        UPDATE
            owp_task
        <set>
            <if test="task.status != null">`status` = #{task.status},</if>
            <if test="task.process != null">`process` = #{task.process},</if>
            <if test="task.startTime != null">`start_time` = #{task.startTime},</if>
            <if test="task.finishTime != null">`finish_time` = #{task.finishTime},</if>
            <if test="task.errMsg != null">`err_msg` = #{task.errMsg},</if>
        </set>
        WHERE
            id = #{task.id}
    </update>

    <update id="updateWithNull">
        UPDATE
        owp_task
        <set>
            `status` = #{task.status},
            `process` = #{task.process},
            `start_time` = #{task.startTime},
            `finish_time` = #{task.finishTime},
        </set>
        WHERE
        id = #{task.id}
    </update>

    <update id="updateList" parameterType="com.hdec.data.domain.Redo">
        <foreach collection="tasks" item="task" separator=";">
            UPDATE
                owp_task
            <set>
                <if test="task.status != null">`status` = #{task.status},</if>
                <if test="task.process != null">`process` = #{task.process},</if>
                <if test="task.startTime != null">`start_time` = #{task.startTime},</if>
                <if test="task.finishTime != null">`finish_time` = #{task.finishTime},</if>
            </set>
            WHERE
                id = #{task.id}
        </foreach>
    </update>

    <delete id="delete">
        DELETE
        FROM
            owp_task
        WHERE
            id = #{id}
    </delete>

    <delete id="deletes">
        DELETE
        FROM
            owp_task
        WHERE
            id IN
            <foreach collection="ids" item="id" separator=", " open="(" close=")">
                #{id}
            </foreach>
    </delete>

    <select id="selectFirstTaskByLevel" resultType="com.hdec.data.domain.Task">
        SELECT
            *
        FROM
            owp_task
        WHERE
            status = 0 AND `level` = #{level}
        ORDER BY create_time
        LIMIT 1
    </select>

    <select id="selectNotFinishedParamByField" resultType="string">
        SELECT
            params
        FROM
            owp_task
        WHERE
            status != 2
        AND field_num = #{fieldNum}
        AND `level` IN ( 'slow', 'fast' )
    </select>

</mapper>