<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.FrequencyMapper">

    <select id="queryAcc" resultType="com.hdec.data.vo.AccVo">
        SELECT
            `time`,
            attr_val AS val
        FROM
            data_${fieldNum}_${qo.pointId}
        WHERE
            attr_id = 1 AND `time` BETWEEN #{qo.startTime} AND #{qo.endTime}
        ORDER BY `time` DESC
    </select>

</mapper>