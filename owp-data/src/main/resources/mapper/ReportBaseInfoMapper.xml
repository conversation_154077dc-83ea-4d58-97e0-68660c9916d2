<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.ReportBaseInfoMapper">

    <select id="getBaseInfo" resultType="com.hdec.data.domain.ReportBaseInfo">
        SELECT
            *
        FROM
            owp_report_base_info
        WHERE
            field_num = #{fieldNum}
        LIMIT 1
    </select>

    <update id="updateBaseInfo" parameterType="com.hdec.data.domain.ReportBaseInfo">
        UPDATE owp_report_base_info
        <set>
            <if test="baseInfo.projectOverview != null">project_overview = #{baseInfo.projectOverview},</if>
            <if test="baseInfo.monitorOverview != null">monitor_overview = #{baseInfo.monitorOverview},</if>
        </set>
        WHERE
            field_num = #{baseInfo.fieldNum}
    </update>

    <insert id="saveBaseInfo" parameterType="com.hdec.data.domain.ReportBaseInfo">
        INSERT INTO owp_report_base_info (
            project_overview,
            monitor_overview,
            field_num
        )
        VALUES (
            #{baseInfo.projectOverview},
            #{baseInfo.monitorOverview},
            #{baseInfo.fieldNum}
        )
    </insert>

    <select id="getPersonConf" resultType="com.hdec.data.domain.ReportPerson">
        SELECT
            *
        FROM
            owp_report_person
        WHERE
            field_num = #{fieldNum}
        LIMIT 1
    </select>

    <update id="updatePersonConf" parameterType="com.hdec.data.domain.ReportPerson">
        UPDATE owp_report_person
        SET
            editor = #{person.editor},
            checker = #{person.checker},
            reviewer = #{person.reviewer},
            company = #{person.company},
            dept = #{person.dept}
        WHERE
            field_num = #{person.fieldNum}
    </update>

    <insert id="savePersonConf" parameterType="com.hdec.data.domain.ReportPerson">
        INSERT INTO owp_report_person (
            editor,
            checker,
            reviewer,
            company,
            dept,
            field_num
        )
        VALUES (
            #{person.editor},
            #{person.checker},
            #{person.reviewer},
            #{person.company},
            #{person.dept},
            #{person.fieldNum}
        )
    </insert>

</mapper>