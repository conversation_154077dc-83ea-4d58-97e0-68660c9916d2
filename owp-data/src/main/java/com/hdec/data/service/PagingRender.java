package com.hdec.data.service;

import org.apache.poi.xwpf.usermodel.BreakType;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.ddr.poi.html.ElementRenderer;
import org.ddr.poi.html.HtmlRenderContext;
import org.jsoup.nodes.Element;

/**
 * 分页符标记
 */
public class PagingRender implements ElementRenderer {
    private static final String[] TAGS = new String[]{"pagebreak"};

    @Override
    public boolean renderStart(Element element, HtmlRenderContext context) {
        XWPFParagraph closestParagraph = context.getClosestParagraph();
        closestParagraph.createRun().addBreak(BreakType.PAGE);
        return false;
    }

    @Override
    public String[] supportedTags() {
        return TAGS;
    }

    @Override
    public boolean renderAsBlock() {
        return true;
    }
}
