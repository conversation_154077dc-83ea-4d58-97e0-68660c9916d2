package com.hdec.data.service;

import com.hdec.data.domain.ReportTemplate;
import com.hdec.data.mapper.ReportTemplateMapper;
import com.hdec.data.qo.ReportTemplateQo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 报告模板业务
 *
 * <AUTHOR>
 */
@Service
public class ReportTemplateService {

    @Autowired
    private ReportTemplateMapper templateMapper;

    /**
     * 获取所有报告模板
     */
    public List<ReportTemplate> list(ReportTemplateQo qo) {
        return templateMapper.list(qo);
    }
}
