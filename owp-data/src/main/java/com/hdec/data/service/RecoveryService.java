package com.hdec.data.service;

import com.alibaba.fastjson.JSON;
import com.hdec.common.domain.R;
import com.hdec.common.domain.ResourceCommon;
import com.hdec.common.util.TimeUtil;
import com.hdec.common.vo.InstDirectAttr;
import com.hdec.data.domain.Redo;
import com.hdec.data.domain.Task;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.mapper.DataLogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/19
 */
@Service
public class RecoveryService {

    @Autowired
    private TdService tdService;

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private DataLogMapper logMapper;

    @Autowired
    private TaskService taskService;

    @Lazy
    @Autowired
    private ExcelImportService excelImportService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 恢复删除数据
     */
    public void recoveryDel(String sessionId, String rate, Integer pointId, Integer direct, String time, String delDataJson, String fieldNum) throws ParseException {
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        if (resource == null) {
            return;
        }

        InstDirectAttr info = monitorService.getInstDirectAttr(pointId);

        /* 查询目标数据并更新 */
        Map<String, Object> record = JSON.parseObject(delDataJson, Map.class);
        String tableName = tdService.convertRate(rate) + "_" + info.getInstId() + "_" + pointId;
        if (direct == -1) {
            for (int i = 0; i <= 3; i++) {
                record.put("status_" + i, 0);
            }
        } else {
            record.put("status_" + direct, 0);
        }
        record.put("del", false);

        record.remove("point");

        /* 插入数据 */
        tdService.insertRecord2(tableName, record, rate);

        List<Redo> redoList = new ArrayList<>();
        redoList.add(new Redo(TimeUtil.parse2Day(time), pointId, rate, fieldNum));
        /* 重做统计 */
        excelImportService.redoStat(redoList, "恢复", "fast", resource.getUserId(), resource.getUsername(), fieldNum);
    }

}
