package com.hdec.data.service;

import com.hdec.common.constant.Constant;
import com.hdec.common.util.CommonUtil;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.domain.HighTask;
import com.hdec.data.domain.RedoParam;
import com.hdec.data.mapper.HighTaskMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.Set;

@Service
public class HighTaskService {

    @Autowired
    private HighTaskMapper highTaskMapper;

    @Value("${deploy.fieldNum}")
    private String fieldNum;

    @Value("${highTask.slowLoop.sleepMinutes:15}")
    private Integer slowLoopSleepMinutes;

    @Autowired
    private TaskService taskService;

    @Lazy
    @Autowired
    private ExcelImportService importService;

    /**
     * 批量插入
     */
    public void addBatch(Set<HighTask> highTasks) {
        if (ObjectUtils.isEmpty(highTasks)) {
            return;
        }

        Iterator<HighTask> it = highTasks.iterator();
        while (it.hasNext()) {
            HighTask task = it.next();
            if (isTaskExist(task)) {
                it.remove();
            }
        }

        if (!ObjectUtils.isEmpty(highTasks)) {
            highTaskMapper.addBatch(highTasks);
        }
    }

    /**
     * 判断任务是否存在
     */
    private boolean isTaskExist(HighTask task) {
        HighTask existTask = highTaskMapper.select(task);
        return existTask != null;
    }

    /**
     * 重置任务状态
     */
    public void resetTaskStatus() {
        highTaskMapper.resetTaskStatus();
    }
    /**
     * 循环执行快速队列的Redo任务
     */
    public void fastLoop() {
        int sleetMinutes = 3;
        while (true) {
            HighTask task = selectFirstTaskByLevel("fast");
            if (task == null) {
                CommonUtil.sleepNMinutes(sleetMinutes);
            } else {
                handleTask(task);
                CommonUtil.sleepNMinutes(sleetMinutes);
            }
        }
    }

    /**
     * 循环执行慢速队列的Redo任务
     */
    public void slowLoop() {
        while (true) {
            /* 取最早提交的一条任务 */
            HighTask task = selectFirstTaskByLevel("slow");
            if (task == null) {
                CommonUtil.sleepNMinutes(slowLoopSleepMinutes);
            } else {
                handleTask(task);
                CommonUtil.sleepNMinutes(slowLoopSleepMinutes);

                highTaskMapper.delOldData(3);
            }
        }
    }

    /**
     * 成功结束
     */
    public void finishBySuccess(HighTask task) {
        task.setStatus(2);
        task.setFinishTime(new Date());
        highTaskMapper.update(task);
    }

    /**
     * 取最早提交的一条任务
     */
    public synchronized HighTask selectFirstTaskByLevel(String level) {
        HighTask task = highTaskMapper.selectFirstTaskByLevel(level);
        if (task == null) {
            return null;
        }

        /* 更新标志位 */
        task.setStatus(1);
        task.setStartTime(new Date());
        highTaskMapper.update(task);
        return task;
    }

    /**
     * 执行任务
     */
    private void handleTask(HighTask task) {
        if (task.getRate() == null) {
            return;
        }

        /* 执行任务，执行完毕后更新任务状态 */
        try {
            taskService.doTaskByDays(fieldNum, task.getRate(), Arrays.asList(new RedoParam(task.getPoint(), task.getDay())), "auto_high");
            if (Constant.RATE_HIGH.equals(task.getRate())) {
                importService.handleHighSample2(fieldNum, task.getPoint(), TimeUtil.format2Day(task.getDay()));
            }

            finishBySuccess(task);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
