package com.hdec.data.service;

//import org.jsoup.nodes.Element;

import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.ddr.poi.html.ElementRenderer;
import org.ddr.poi.html.HtmlRenderContext;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTFonts;

/**
 * 将H1 - H6 标签 render为 标题1 - 标题6
 */
public class TopicRender implements ElementRenderer {

    private static final String[] TAGS = new String[]{"h1", "h2", "h3", "h4", "h5", "h6"};

    //开始render - return true == 渲染text里面的内容 | return false == 不渲染内容
    public boolean renderStart(org.jsoup.nodes.Element element, HtmlRenderContext context) {
        int parseInt = Integer.parseInt(element.normalName().substring(1));
        XWPFParagraph closestParagraph = context.getClosestParagraph();
        closestParagraph.setStyle(String.valueOf(parseInt));
        XWPFRun closestParagraphRun = closestParagraph.createRun();
        CTFonts ctFonts = closestParagraphRun.getCTR().addNewRPr().addNewRFonts();

        ctFonts.setEastAsia("SimHei");
        ctFonts.setAscii("Times New Roman");

        closestParagraphRun.setText(element.text());
        closestParagraphRun.setBold(false);

//            //获取 | 设置
//            XWPFDocument xwpfDocument = new XWPFDocument(new FileInputStream("/home/<USER>"));
//            CTStyles ctStyles = xwpfDocument.getStyle();
//            XWPFDocument doc = context.getXWPFDocument();
//            XWPFStyles docStyles = doc.createStyles();
//            docStyles.setStyles(ctStyles);
//
//            docStyles.setEastAsia("SimHei");
//            docStyles.fo
//
//            //此时调用doc.getStyle()已经能获取到模板的样式了
//
//            //获取段落
//
//            XWPFParagraph closestParagraph = context.getClosestParagraph();
//            closestParagraph.setStyle(String.valueOf(parseInt));
//            XWPFRun closestParagraphRun = closestParagraph.createRun();
//            closestParagraphRun.setText(element.text());
//
////            doc.enforceUpdateFields();

        return false;
    }


    //用于获取到tag尾的内容
    public void renderEnd(org.jsoup.nodes.Element element, HtmlRenderContext context) {
        context.unmarkDedupe();
    }

    //能进入renderStart/End 的标签
    @Override
    public String[] supportedTags() {
        return TAGS;
    }

    //是否块状染渲 <div> or <span>
    @Override
    public boolean renderAsBlock() {
        return true;
    }
}
