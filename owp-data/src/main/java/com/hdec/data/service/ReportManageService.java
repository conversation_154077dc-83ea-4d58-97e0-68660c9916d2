package com.hdec.data.service;

import com.hdec.common.domain.ProResourceCommon;
import com.hdec.common.domain.R;
import com.hdec.data.domain.Report;
import com.hdec.data.feign.WindService;
import com.hdec.data.mapper.ReportMapper;
import com.hdec.data.qo.FinalReportQo;
import com.hdec.data.qo.ReportQo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 报告管理业务类
 *
 * <AUTHOR>
 */
@Service
public class ReportManageService {

    @Autowired
    private ReportMapper reportMapper;

    @Autowired
    private WindService windService;

    /**
     * 项目级报告列表
     */
    public List<Report> proList(String fieldNum, ReportQo qo) {
        return reportMapper.proList(fieldNum, qo);
    }

    /**
     * 企业级报告列表
     */
    public List<Report> entList(ReportQo qo, List<String> fieldNums) {
        if (ObjectUtils.isEmpty(fieldNums)) {
            return Collections.emptyList();
        }

        Map<String, String> fieldNameMap = getFieldNames(fieldNums);
        List<Report> reports = reportMapper.entList(qo, fieldNums);
        if (ObjectUtils.isEmpty(reports)) {
            return reports;
        }

        /* 补充风场名称 */
        Iterator<Report> it = reports.iterator();
        while (it.hasNext()) {
            Report report = it.next();
            String fieldName = fieldNameMap.get(report.getFieldNum());
            if (ObjectUtils.isEmpty(fieldName)) {
                it.remove();
            } {
                report.setFieldName(fieldName);
            }
        }

        /* 按风场名称进行筛选 */
        if (!ObjectUtils.isEmpty(qo.getProjectName()) && !ObjectUtils.isEmpty(reports)) {
            reports = reports.stream().filter(e -> !ObjectUtils.isEmpty(e.getFieldName()) && e.getFieldName().indexOf(qo.getProjectName()) != -1).collect(Collectors.toList());
        }
        return reports;
    }

    /**
     * 按风场筛选
     */
    private List<String> filterByFieldName(Map<String, String> fieldNameMap, String fieldName) {
        List<String> res = new ArrayList<>();
        for (Map.Entry<String, String> en : fieldNameMap.entrySet()) {
            if (!ObjectUtils.isEmpty(en.getValue()) && en.getValue().indexOf(fieldName) != -1) {
                res.add(en.getKey());
            }
        }
        return res;
    }

    private Map<String, String> getFieldNames(List<String> fieldNums) {
        Map<String, String> map = new HashMap<>();
        if (ObjectUtils.isEmpty(fieldNums)) {
            return map;
        }

        List<ProResourceCommon> fieldNames = windService.getFieldNameByFieldNums(fieldNums);
        if (!ObjectUtils.isEmpty(fieldNames)) {
            for (ProResourceCommon fieldName : fieldNames) {
                map.put(fieldName.getFieldNum(), fieldName.getName());
            }
        }
        return map;
    }

    /**
     * 保存报告
     */
    public void save(Report report) {
        reportMapper.save(report);
    }

    /**
     * 更新报告状态
     */
    public void updateStatus(Integer reportId, String status) {
        reportMapper.updateStatus(reportId, status);
    }

    /**
     * 更新报告状态
     */
    public void updatePath(Integer reportId, String status, String filepath, String pdfPath) {
        reportMapper.updatePath(reportId, status, filepath, pdfPath);
    }

    /**
     * 上传最终稿月报
     */
    public R finalReport(FinalReportQo qo) {
        if (ObjectUtils.isEmpty(qo.getFilepath())) {
            return R.error("请选择文件");
        }
        String filePath = qo.getFilepath().toLowerCase();
        if (!filePath.endsWith(".doc") && !filePath.endsWith(".docx") && !filePath.endsWith(".pdf")) {
            return R.error("文件只允许DOC、DOCX、PDF格式");
        }
        reportMapper.finalReport(qo.getReportId(), qo.getFilepath());
        return R.success("操作成功");
    }

    /**
     * 批量删除报告
     */
    public void delete(Integer[] ids) {
        reportMapper.delete(ids);
    }

    /**
     * 获取所有期数
     */
    public List<String> entAllIssueNums(List<String> fieldNums) {
        return reportMapper.entAllIssueNums(fieldNums);
    }

    public Integer proIssueNumByYear(String fieldNum, Integer issueYear) {
        return reportMapper.proIssueNumByYear(fieldNum, issueYear);
    }

}
