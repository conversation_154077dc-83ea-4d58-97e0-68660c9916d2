package com.hdec.data.service;

import com.hdec.common.domain.AttrCommon;
import com.hdec.common.domain.PointCommon;
import com.hdec.data.domain.StatVar;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.qo.ParseVarQo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 复用变量业务逻辑类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MultiVarService extends VarServiceBase {

    @Autowired
    private TdService tdService;

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private MultiVarParseService multiVarParse;

    /**
     * 复用变量解析
     */
    public String multiVarParse(String fieldNum, ParseVarQo qo) {
        StatVar varObj;
        try {
            varObj = multiVarParse.parseVar2Bean(fieldNum, qo.getVar());
        } catch (Exception e) {
            log.error(e.getMessage());
            return NO_RES_STR;
        }
        System.out.println("varObj:" + varObj);

        /* 获取该风场下所有测点、分量 */
        List<PointCommon> fieldPoints = monitorService.getPointsByFieldNum(fieldNum);
        List<AttrCommon> fieldAttrs = monitorService.allAttrByField(fieldNum);
        return handleMultiVar(varObj, fieldPoints, fieldAttrs, qo.getStartTime(), qo.getEndTime());
    }

    /**
     * 处理复用变量
     */
    private String handleMultiVar(StatVar varObj, List<PointCommon> fieldPoints, List<AttrCommon> fieldAttrs, String startTime, String endTime) {
        /* 查询涉及到的所有测点并按仪器分组 */
        List<PointCommon> points = getRelatedPointIds(varObj, fieldPoints);
        Map<Integer, List<PointCommon>> instPoints = points.stream().collect(Collectors.groupingBy(PointCommon::getInstId));

        /* 查询涉及到的所有数据 */
        String unit = "";
        List<Map<String, Object>> totalLines = new ArrayList<>();
        for (Map.Entry<Integer, List<PointCommon>> entry : instPoints.entrySet()) {
            Integer instId = entry.getKey();
            List<PointCommon> ps = entry.getValue();
            // 获取分量ID
            AttrCommon attr = findAttrIdByName(fieldAttrs, varObj.getAttrName(), instId);
            if (attr == null) {
                continue;
            }
            // 更新单位
            if (!ObjectUtils.isEmpty(attr.getUnit())) {
                unit = attr.getUnit();
            }

//            List<Map<String, Object>> lines = tdService.getTogetherData(instId, ps, attr.getId(), varObj.getDirect(), startTime, endTime, varObj.getStatItem());
//            totalLines.addAll(lines);
        }
        if (totalLines.size() == 0) {
            return NO_RES_STR;
        }

        if ("最小值".equals(varObj.getStatItem())) {
            return totalLines.get(0).get("min") + unit;
        } else if ("最大值".equals(varObj.getStatItem())) {
            return totalLines.get(0).get("max") + unit;
        } else if ("平均值".equals(varObj.getStatItem())) {
            return totalLines.get(0).get("avg") + unit;
        } else if ("最小值~最大值".equals(varObj.getStatItem())) {
            Object min = totalLines.get(0).get("min");
            Object max = totalLines.get(0).get("max");
            if (min != null && max != null) {
                return min + unit + "~" + max + unit;
            }
        } else if (varObj.getStatItem().contains("最小值的")) {
            Float min = totalLines.stream().map(x -> Float.parseFloat(String.valueOf(x.get("min")))).min(Comparator.comparing(x -> x)).orElse(null);
            Float max = totalLines.stream().map(x -> Float.parseFloat(String.valueOf(x.get("min")))).max(Comparator.comparing(x -> x)).orElse(null);
            if (min != null && max != null) {
                return min + unit + "~" + max + unit;
            }
        } else if (varObj.getStatItem().contains("最大值的")) {
            Float min = totalLines.stream().map(x -> Float.parseFloat(String.valueOf(x.get("max")))).min(Comparator.comparing(x -> x)).orElse(null);
            Float max = totalLines.stream().map(x -> Float.parseFloat(String.valueOf(x.get("max")))).max(Comparator.comparing(x -> x)).orElse(null);
            if (min != null && max != null) {
                return min + unit + "~" + max + unit;
            }
        } else if (varObj.getStatItem().contains("平均值的")) {
            Float min = totalLines.stream().map(x -> Float.parseFloat(String.valueOf(x.get("avg")))).min(Comparator.comparing(x -> x)).orElse(null);
            Float max = totalLines.stream().map(x -> Float.parseFloat(String.valueOf(x.get("avg")))).max(Comparator.comparing(x -> x)).orElse(null);
            if (min != null && max != null) {
                return min + unit + "~" + max + unit;
            }
        }
        return NO_RES_STR;
    }

    /**
     * 由分量名查找分量ID
     */
    private AttrCommon findAttrIdByName(List<AttrCommon> fieldAttrs, String attrName, Integer instId) {
        for (AttrCommon attr : fieldAttrs) {
            if (instId.equals(attr.getInstId()) && attrName.equals(attr.getName())) {
                return attr;
            }
        }
        return null;
    }

}
