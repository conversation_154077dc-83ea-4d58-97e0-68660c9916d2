package com.hdec.data.service;

import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.ddr.poi.html.ElementRenderer;
import org.ddr.poi.html.HtmlRenderContext;
import org.jsoup.nodes.Element;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPageNumber;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSectPr;

import java.math.BigInteger;

/** 使用<pagestart/>作为内容开始标记,用于创建一个新的分页 */
public class StartPageRender implements ElementRenderer {
    private  static  final  String[]  TAGS =new  String[]{"pagestart"};
    @Override
    public boolean renderStart(Element element, HtmlRenderContext context) {
        XWPFParagraph closestParagraph = context.getClosestParagraph();
        CTSectPr ctSectPr = closestParagraph.getCTP().getPPr().addNewSectPr();
        CTPageNumber ctPageNumber = ctSectPr.addNewPgNumType();
        ctPageNumber.setStart(BigInteger.valueOf(1));
        return false;
    }

    @Override
    public String[] supportedTags() {
        return TAGS;
    }

    @Override
    public boolean renderAsBlock() {
        return true;
    }
}

