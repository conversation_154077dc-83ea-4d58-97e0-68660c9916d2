package com.hdec.data.service;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.hdec.common.constant.Constant;
import com.hdec.common.domain.*;
import com.hdec.common.util.CommonUtil;
import com.hdec.common.util.FileUtil;
import com.hdec.common.util.ReportUtil;
import com.hdec.common.vo.PreTabVo;
import com.hdec.common.vo.TabVo;
import com.hdec.data.domain.ChartNum;
import com.hdec.data.domain.Outline;
import com.hdec.data.domain.ReportOutlineNav;
import com.hdec.data.domain.template.Mould;
import com.hdec.data.domain.template.ReportCover;
import com.hdec.data.domain.template.ReportVar;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.feign.UserService;
import com.hdec.data.feign.WindService;
import com.hdec.data.mapper.NavMouldMapper;
import com.hdec.data.mapper.ReportMouldMapper;
import com.hdec.data.qo.*;
import com.hdec.data.util.EasyWord;
import com.hdec.data.util.JsoupUtil;
import com.hdec.data.util.WordToPdfUtil;
import com.hdec.data.vo.ChartNumVo;
import com.hdec.data.vo.FieldVo;
import com.hdec.data.vo.ParamVo;
import com.hdec.data.vo.Td;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 模板业务类
 *
 * <AUTHOR>
 */
@Service
public class ReportMouldService {

    /** 文件本地路径 - Linux */
    @Value("${file.linuxPath}")
    private String linuxPath;

    @Autowired
    private ReportMouldMapper mouldMapper;

    @Autowired
    private NavMouldMapper navMouldMapper;
    @Autowired
    private WindService windService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ReportOutlineService outlineService;

    @Autowired
    private UserService userService;

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private ReportTabService tabService;

    @Autowired
    private ReportVarService reportVarService;

    /**
     * 核心线程数
     */
    private final static int corePoolSize = 2;

    /**
     * 最大线程数
     */
    private final static int maxPoolSize = corePoolSize * 2;

    /**
     * 队列容量
     */
    private final static int queueSize = corePoolSize * 10;

    /**
     * 线程池
     */
    private final static ThreadPoolExecutor threadPool = new ThreadPoolExecutor(corePoolSize, maxPoolSize, 3, TimeUnit.MINUTES, new ArrayBlockingQueue<>(queueSize));

    static {
        // 允许核心线程在空闲时销毁
        threadPool.allowCoreThreadTimeOut(true);
    }

    /**
     * 模板列表
     */
    public List<Mould> list(String fieldNum, MouldQo qo) {
        List<Mould> moulds = mouldMapper.list(fieldNum, qo.getName(), qo.getType(), qo.getLabel());

        for (Mould mould : moulds) {
            if (!ObjectUtils.isEmpty(mould.getLabel())) {
                mould.setLabels(mould.getLabel().split(Constant.SPLIT_STR));
            }
        }

        /* 设置该模板绑定的大纲 */
        Map<Integer, List<Outline>> map = getMouldOutlineMap(moulds);
        for (Mould mould : moulds) {
            mould.setOutlines(map.get(mould.getId()));
        }
        return moulds;
    }

    /**
     * 风场下所有模板
     */
    public List<Mould> listByField(String fieldNum) {
        return mouldMapper.listByField(fieldNum);
    }

    /**
     * 获取模板大纲映射
     */
    private Map<Integer, List<Outline>> getMouldOutlineMap(List<Mould> moulds) {
        if (ObjectUtils.isEmpty(moulds)) {
            return Collections.emptyMap();
        }

        Set<Integer> mouldIds = moulds.stream().map(Mould::getId).collect(Collectors.toSet());
        List<Outline> outlines = mouldMapper.getOutlineByMouldIds(mouldIds);
        if (ObjectUtils.isEmpty(outlines)) {
            return Collections.emptyMap();
        }

        return outlines.stream().collect(Collectors.groupingBy(Outline::getMouldId));
    }

    /**
     * 更新模板
     */
    public Map<String, String> update(Mould mould) {
        if (!ObjectUtils.isEmpty(mould.getLabels())) {
            mould.setLabel(Joiner.on(Constant.SPLIT_STR).join(mould.getLabels()));
        }
        mouldMapper.update(mould);

        /* 更新引用编号内容 */
        threadPool.submit(() -> {
            try {
                updateRefContent(mould);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        // 返回图表引用情况
        return getChartNumFefInfo(mould.getContent());
    }

    /**
     * 更新引用编号内容
     */
    private void updateRefContent(Mould mould) {
        List<ChartNum> chartNums = listChartNumsByMould(mould.getId());
        Map<String, String> chartNumMap = getChartNumMap(chartNums);

        // 获取所有引用了当前模板编号的
        List<MouldVal> mouldValList = navMouldMapper.getNavMouldByRefId(mould.getId());
        for (MouldVal mouldVal : mouldValList) {
            List<ParamVo> paramVos = JSON.parseArray(mouldVal.getRefVal(), ParamVo.class);
            Iterator<ParamVo> it = paramVos.iterator();
            while (it.hasNext()) {
                ParamVo paramVo = it.next();
                if (!paramVo.getRefId().startsWith("u-" + mould.getId() + "-")) {
                    continue;
                }

                String content = chartNumMap.get(paramVo.getRefId());
                if (ObjectUtils.isEmpty(content)) {
                    it.remove();
                } else {
                    paramVo.setRefContent(content);
                }
            }
            mouldVal.setRefVal(JSON.toJSONString(paramVos));
            navMouldMapper.setMouldValue(mouldVal);
        }

        List<MouldVal> list = navMouldMapper.getNavMouldValueByMouldId(mould.getId());
        for (MouldVal mouldVal : list) {
            if (mould.getContent().contains("@{风机编号}") || mould.getContent().contains("@{升压站}")) {
                if (ObjectUtils.isEmpty(mouldVal.getFanVal())) {
                    mouldVal.setFanVal("");
                }
            } else {
                if ("".equals(mouldVal.getFanVal())) {
                    mouldVal.setFanVal(null);
                }
            }
            navMouldMapper.setMouldValue(mouldVal);
        }
    }

    private Map<String, String> getChartNumMap(List<ChartNum> chartNums) {
        Map<String, String> map = new HashMap<>();
        for (ChartNum chartNum : chartNums) {
            map.put(chartNum.getId(), chartNum.getContent());
        }
        return map;
    }

    /**
     * 获取图表编号引用情况
     */
    private Map<String, String> getChartNumFefInfo(String content) {
        if (ObjectUtils.isEmpty(content)) {
            return Collections.emptyMap();
        }

        Map<String, String> map = new HashMap<>();

        /* 获取所有引用标签、图表标签 */
        Document document = Jsoup.parse(content);
        List<Element> refNumElements = JsoupUtil.getElementsByAttr(document, "data-type", "refNum");
        List<Element> chartNumElements = JsoupUtil.getElementsByAttr(document, "data-type", "chartNum");

        Map<String, String> chartNumMap = getChartNumElementsMap(chartNumElements);
        for (Element refNumElement : refNumElements) {
            String id = refNumElement.attr("id");
            String dataRef = refNumElement.attr("data-ref");
            if (dataRef != null && chartNumMap.get(dataRef) != null) {
                map.put(id, chartNumMap.get(dataRef));
            } else {
                map.put(id, null);
            }
        }
        return map;
    }

    /**
     * 获取所有图表编号Map
     */
    private Map<String, String> getChartNumElementsMap(List<Element> chartNumElements) {
        if (ObjectUtils.isEmpty(chartNumElements)) {
            return Collections.emptyMap();
        }

        Map<String, String> map = new HashMap<>();
        for (Element element : chartNumElements) {
            String id = element.id();
            if (ObjectUtils.isEmpty(id)) {
                continue;
            }
            map.put(id, loopUpPText(element));
        }
        return map;
    }

    /**
     * 新增模板
     */
    public Integer add(Mould mould, String sessionId) {
        Mould existMould = mouldMapper.selectByName(null, mould.getFieldNum(), mould.getName());
        if (existMould != null) {
            return -1;
        }

        /* 完善模板信息 */
        perfectMouldInfo(mould, sessionId);
        mouldMapper.add(mould);
        return mould.getId();
    }

    /**
     * 按名称查询模板
     */
    public Mould selectByName(Integer id, String fieldNum, String name) {
        return mouldMapper.selectByName(id, fieldNum, name);
    }

    /**
     * 批量新增模板
     */
    public void adds(List<Mould> moulds, String sessionId) {
        /* 设置创建人信息 */
        for (Mould mould : moulds) {
            perfectMouldInfo(mould, sessionId);
        }
        mouldMapper.adds(moulds);
    }

    /**
     * 完善模板信息
     */
    private void perfectMouldInfo(Mould mould, String sessionId) {
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        if (resource != null) {
            mould.setUserId(resource.getUserId());
            mould.setUsername(resource.getUsername());
        }

        mould.setType("普通模板");
        if (!ObjectUtils.isEmpty(mould.getLabels())) {
            mould.setLabel(Joiner.on(Constant.SPLIT_STR).join(mould.getLabels()));
        }
    }

    /**
     * 生成报告测试
     */
    public void genReportTest(HttpServletResponse response, Integer titlePageId) throws Exception {
        Mould titlePage = mouldMapper.getById(titlePageId);
        if (!ObjectUtils.isEmpty(titlePage)) {
            long s = System.currentTimeMillis();

//            removeStyle(titlePage);
//            replaceVar(titlePage);
//            exportWord(response, titlePage.getContent(), "报告测试");

            long e = System.currentTimeMillis();
            System.out.println((e - s));
        }
    }

    private void removeStyle(ReportCover titlePage) {
        String content = titlePage.getContent();
        if (ObjectUtils.isEmpty(content)) {
            return;
        }
    }

    /**
     * 替换变量
     */
    private void replaceVar(Mould titlePage) {
//        Random r = new Random();
//
//        String content = titlePage.getContent();
//        if (ObjectUtils.isEmpty(content)) {
//            return;
//        }
//        Pattern p = Pattern.compile("@\\{[^@]*}");
//        Matcher m = p.matcher(content);
//        if (m.find()) {
//            titlePage.setContent(m.replaceAll(r.nextInt(1000) + ""));
////            System.out.println();
//        }
////        while (m.find()) {
////            switch (m.group()) {
////                case "@{监测年份}":
////                    System.out.println(m.replaceAll(r.nextInt(100) + ""));
////                    break;
////                case "@{当年月报期数}":
////
////                    break;
////                case "@{总月报期数}":
////
////                    break;
////                case "@{编写日期}":
////
////                    break;
////                case "@{监测时段（yyyy年mm月dd日 ~ yyyy年mm月dd日）}":
////
////                    break;
////                case "@{监测时段（yyyy-mm-dd ~ yyyy-mm-dd）}":
////
////                    break;
////            }
////        }

    }

//    /**
//     *
//     * @param response
//     * @param content  富文本内容
//     * @param fileName 生成word名字
//     * @throws Exception
//     */
//    public static void exportWord(HttpServletResponse response, String content, String fileName) throws Exception {
//
//        content = content.replace("\n", "");
//        try {
//            //新建Document对象
//            Document document = new Document();
//            //添加section
//            Section section = document.addSection();
//
//            //获取第一节的页边距
//            MarginsF pageMargin = section.getPageSetup().getMargins();
//
//            //设置第一节的上下左右页边距
//            pageMargin.setTop(57.9f);
//            pageMargin.setBottom(57.9f);
//            pageMargin.setLeft(57.9f);
//            pageMargin.setRight(57.9f);
//
//            ByteArrayOutputStream os = new ByteArrayOutputStream();
//            //添加段落并写入HTML文本
//            String[] contents = content.split("<!-- pagebreak -->");
//            for (int i = 0; i < contents.length; i++) {
//                section.addParagraph().appendHTML(contents[i]);
//                if (i != contents.length - 1) {
//                    section.addParagraph().appendBreak(BreakType.Page_Break);
//                }
//            }
//
//            document.saveToStream(os, FileFormat.Docx);
//
//            InputStream is = new ByteArrayInputStream(os.toByteArray());
//
//            //输出文件
//            response.setContentType("application/msword");//导出word格式
//            response.addHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("GB2312"), "ISO8859-1") + ".docx");
//            ServletOutputStream outputStream = response.getOutputStream();
//
//            int len;
//            byte []by = new byte[1024];
//            while((len = is.read(by))!=-1) {
//                outputStream.write(by,0,len);
//            }
//            outputStream.flush();
//            outputStream.close();
//            is.close();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    /**
     * html转word
     */
    public static void html2Word(HttpServletResponse response, String content, String filename) throws Exception {

//                byte b[] = content.getBytes("GBK"); //这里是必须要设置编码的，不然导出中文就会乱码。
//        ByteArrayInputStream bais = new ByteArrayInputStream(b);//将字节数组包装到流中
//        POIFSFileSystem poifs = new POIFSFileSystem();
//        DirectoryEntry directory = poifs.getRoot();
//        DocumentEntry documentEntry = directory.createDocument("WordDocument", bais); //该步骤不可省略，否则会出现乱码。
//        //输出文件
//        request.setCharacterEncoding("utf-8");
//        response.setContentType("application/msword");//导出word格式
//        response.addHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("GB2312"),"ISO8859-1") + ".doc");
//        ServletOutputStream ostream = response.getOutputStream();
//        poifs.writeFilesystem(ostream);
//        bais.close();
//        ostream.close();
//        poifs.close();


//        try {
//            Document document = new Document();
//            Section section = document.addSection();
//            ByteArrayOutputStream os = new ByteArrayOutputStream();
//            section.addParagraph().appendHTML(content);
//            document.saveToStream(os, FileFormat.Docx);
//
//            InputStream input = new ByteArrayInputStream(os.toByteArray());
//
//
//            response.reset();
//            response.setCharacterEncoding("UTF-8");
//            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8") + ".docx");
//            response.addHeader("Content-Length", "-1");
//            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
//            response.setContentType("application/octet-stream");
//            outputStream.flush();
//
//            int len;
//            byte[] buf = new byte[1024];
//            while ((len = input.read(buf)) != -1) {
//                outputStream.write(buf, 0, len);
//            }
//            outputStream.flush();
//            outputStream.close();
//            input.close();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }

    /**
     * 获取某个模板详情
     */
    public Mould detail(Integer id) {
        Mould mould = mouldMapper.getById(id);

        if (!ObjectUtils.isEmpty(mould.getLabel())) {
            mould.setLabels(mould.getLabel().split(Constant.SPLIT_STR));
        }
        return mould;
    }

    /**
     * 批量删除
     */
    public R delete(Integer[] ids) {
        /* 判断模板是否在被使用 */
        for (Integer id : ids) {
            List<Integer> outlineIds = navMouldMapper.getOutlineByMould(id);
            if (!ObjectUtils.isEmpty(outlineIds)) {
                Mould mould = mouldMapper.getById(id);
                return R.error("模板 " + (mould == null ? "未知" : mould.getName()) + " 正在被大纲引用，请先删除引用大纲");
            }
        }
        mouldMapper.delete(ids);
        return R.success("删除成功");
    }

    /**
     * 获取所有模板标签
     */
    public Set<String> getAllMouldLabels(String fieldNum) {
        List<String> labels = mouldMapper.getAllMouldLabels(fieldNum);
        if (ObjectUtils.isEmpty(labels)) {
            return Collections.emptySet();
        }

        Set<String> set = new HashSet<>();
        for (String label : labels) {
            set.addAll(Arrays.asList(label.split(Constant.SPLIT_STR)));
        }
        return set;
    }

    /**
     * 列出某模板中的所有图表编号
     */
    public List<ChartNum> listChartNumsByMould(Integer mouldId) {
        String content = mouldMapper.getById(mouldId).getContent();
        return getChartNumsByMould(content);
    }

    /**
     * 列出某大纲中的所有图表编号(导航树方式)
     */
    public List<ReportOutlineNav> listChartNumsByOutline(String fieldNum, Integer outlineId) {
        /* 获取该大纲下导航树 */
        List<ReportOutlineNav> navs = outlineService.nav(fieldNum, outlineId);
        if (ObjectUtils.isEmpty(navs)) {
            return Collections.emptyList();
        }

        AtomicInteger index = new AtomicInteger(1000);
        iteratorNavTree(fieldNum, navs, index);
        return navs;
    }

    /**
     * 遍历树(往树的叶子节点下添加图表编号)
     */
    private void iteratorNavTree(String fieldNum, List<ReportOutlineNav> navTree, AtomicInteger index) {
        for (ReportOutlineNav nav : navTree) {
            if (nav.getChildren() == null) {
                /* 叶子节点，在叶子下封装模板图表编号，需要封装两层 */
                List<Mould> moulds = outlineService.getMouldByNav(fieldNum, Integer.parseInt(nav.getBid()));
                if (ObjectUtils.isEmpty(moulds)) {
                    nav.setChildren(Collections.emptyList());
                }

                List<ReportOutlineNav> mouldNavs = new ArrayList<>();
                for (Mould mould : moulds) {
                    ReportOutlineNav mouldNav = new ReportOutlineNav(index.getAndIncrement(), String.valueOf(mould.getId()), mould.getName());
                    mouldNavs.add(mouldNav);
                    List<ReportOutlineNav> charNumNavs = new ArrayList<>();
                    List<ChartNum> mouldChartNums = getChartNumsByMould(mould.getContent());
                    for (ChartNum mouldChartNum : mouldChartNums) {
                        String pid = nav.getId() + "_" + mouldChartNum.getId();
                        charNumNavs.add(new ReportOutlineNav(index.getAndIncrement(), pid, mouldChartNum.getContent()));
                    }
                    mouldNav.setChildren(charNumNavs);
                }
                nav.setChildren(mouldNavs);
            } else {
                if (nav.getChildren().size() > 0) {
                    iteratorNavTree(fieldNum, nav.getChildren(), index);
                }
            }
        }
    }

    /**
     * 获取某模板中的所有图表编号
     */
    private static List<ChartNum> getChartNumsByMould(String content) {
        List<ChartNum> vos = new ArrayList<>();
        Document document = Jsoup.parse(content);
        List<Element> chartNumElements = JsoupUtil.getElementsByAttr(document, "data-type", "chartNum");
        for (Element element : chartNumElements) {
            String id = element.id();
            if (ObjectUtils.isEmpty(id)) {
                continue;
            }

            vos.add(new ChartNum(id, ReportUtil.mySpecialTrim(CommonUtil.removeZeroWidthChar(loopUpPText(element)).toCharArray())));
        }
        return vos;
    }

    /**
     * 向上找P标签内容
     */
    private static String loopUpPText(Element element) {
        Element parent = element.parent();
        while (parent != null && !"p".equals(parent.tagName())) {
            parent = parent.parent();
        }
        if (parent == null) {
            return element.text();
        }
        return parent.text();
    }

    /**
     * 批量获取模板
     */
    public List<Mould> getByIds(Integer[] mouldIds) {
        if (ObjectUtils.isEmpty(mouldIds)) {
            return Collections.emptyList();
        }

        return mouldMapper.getByIds(mouldIds);
    }

    /**
     * 由ID获取模板
     */
    public Mould getById(Integer mouldId) {
        return mouldMapper.getById(mouldId);
    }

    /**
     * 获取权限范围内所有风场
     */
    public List<FieldVo> listAuthFields(String curFieldNum, String sessionId) {
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        List<String> fieldNums = userService.getFieldResource(resource.getUnitId());
        fieldNums.remove(curFieldNum);

        Map<String, ProResourceCommon> fieldNamesMap = getFieldNamesMap(fieldNums);
        List<FieldVo> vos = new ArrayList<>();
        for (String fieldNum : fieldNums) {
            ProResourceCommon field = fieldNamesMap.get(fieldNum);
            if (field != null && !"测试项目".equals(field.getName())) {
                vos.add(new FieldVo(fieldNum, field.getName()));
            }
        }
        return vos;
//
//        /* 工程云 - 测试服 */
//        List<FieldVo> vos = new ArrayList<>();
//        vos.add(new FieldVo("owp00059", "竞赛测试项目"));
//        return vos;
    }


    /**
     * 获取权限范围内所有风场的模板(导航树方式)
     */
    public List<ReportOutlineNav> listAuthFieldMoulds(String curFieldNum, String sessionId) {
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        List<String> fieldNums = userService.getFieldResource(resource.getUnitId());
        fieldNums.remove(curFieldNum);

        Map<String, ProResourceCommon> fieldNamesMap = getFieldNamesMap(fieldNums);

        // 用于设置下标
        AtomicInteger index = new AtomicInteger(0);

        // 第一层
        List<ReportOutlineNav> root = new ArrayList<>();
        for (String fieldNum : fieldNums) {
            ReportOutlineNav nav = new ReportOutlineNav(index.incrementAndGet(), fieldNum, ObjectUtils.isEmpty(fieldNamesMap.get(fieldNum)) ? "未知风场" : fieldNamesMap.get(fieldNum).getName());
            // 第二层
            List<Mould> moulds = this.list(fieldNum, new MouldQo());
            List<ReportOutlineNav> second = new ArrayList<>();
            nav.setChildren(second);
            for (Mould mould : moulds) {
                second.add(new ReportOutlineNav(index.incrementAndGet(), String.valueOf(mould.getId()), mould.getName()));
            }
            root.add(nav);
        }
        return root;
    }

    /**
     * 由风场编码获取风场名称Map
     */
    private Map<String, ProResourceCommon> getFieldNamesMap(List<String> fieldNums) {
        List<ProResourceCommon> fieldNames = windService.getFieldNameByFieldNums(fieldNums);
        if (ObjectUtils.isEmpty(fieldNames)) {
            return Collections.emptyMap();
        }
        return fieldNames.stream().collect(Collectors.toMap(ProResourceCommon::getFieldNum, Function.identity(), (key1, key2) -> key2));
    }

    /**
     * 导入模板
     */
    public void importMoulds(String fieldNum, Integer[] mouldIds, String sessionId) {
        List<String> customVars = reportVarService.getCustomVars(fieldNum).stream().map(ReportVar::getName).collect(Collectors.toList());

        Map<String, Mould> fieldNameMouldMap = this.getFieldNameMouldMap(fieldNum);

        List<Mould> moulds = this.getByIds(mouldIds);

        /* 重命名重名模板 */
        renameDuplicate(moulds, fieldNameMouldMap);

        /* 替换模板中的表格 */
        for (Mould mould : moulds) {
            mould.setFieldNum(fieldNum);

            replaceAttr(mould);
            replaceTable(mould);
            replaceLine(mould);
            replaceChart(mould);
            replaceCustomVar(mould, customVars);
        }

        /* 批量添加 */
        this.adds(moulds, sessionId);
    }

    /**
     * 替换自定义变量
     */
    private void replaceCustomVar(Mould mould, List<String> customVars) {
        Document doc = Jsoup.parse(mould.getContent());

        /* 分量属性 */
        List<Element> attrs = JsoupUtil.getElementsByAttr(doc, "data-type", "attr");
        for (Element ele : attrs) {
            String json = ele.attr("data-json");
            if (!ObjectUtils.isEmpty(json)) {
                String seaType = JSON.parseObject(json).getString("seaType");
                if (!ObjectUtils.isEmpty(seaType) && !"所有风机".equals(seaType) && !"典型风机".equals(seaType) && !"非典型风机".equals(seaType) && !"@{风机编号}".equals(seaType) && !"@{升压站}".equals(seaType)) {
                    if (!customVars.contains(seaType)) {
                        ele.append("<p style=\"color: rgb(255, 0, 0);\">请先创建变量 '" + seaType + "' 后再次导入该模板</p>");
                    }
                }
            }
        }

        /* 表格属性 */
        List<Element> tables = JsoupUtil.getElementsByAttr(doc, "data-type", "table");
        for (Element ele : tables) {
            String json = ele.attr("data-json");
            if (!ObjectUtils.isEmpty(json)) {
                String customVar = JSON.parseObject(json).getString("customVar");
                if (!ObjectUtils.isEmpty(customVar) && !customVars.contains(customVar)) {
                    ele.append("<p style=\"color: rgb(255, 0, 0);\">请先创建变量 '" + customVar + "' 后再次导入该模板</p>");
                }
            }
        }
        mould.setContent(doc.html());
    }

    /**
     * 重命名重名模板
     */
    private void renameDuplicate(List<Mould> moulds, Map<String, Mould> fieldNameMouldMap) {
        for (Mould mould : moulds) {
            if (!ObjectUtils.isEmpty(fieldNameMouldMap.get(mould.getName()))) {
                for (int i = 1; i < 100; i++) {
                    String newName = mould.getName() + "(" + i + ")";
                    if (ObjectUtils.isEmpty(fieldNameMouldMap.get(newName))) {
                        mould.setName(newName);
                        fieldNameMouldMap.put(newName, new Mould());
                        break;
                    }
                }
            }
        }
    }

    /**
     * 获取风场下所有模板并按名称分组
     */
    private Map<String, Mould> getFieldNameMouldMap(String fieldNum) {
        List<Mould> fieldMoulds = this.list(fieldNum, new MouldQo());
        if (ObjectUtils.isEmpty(fieldMoulds)) {
            return Collections.emptyMap();
        }
        return fieldMoulds.stream().collect(Collectors.toMap(Mould::getName, Function.identity(), (key1, key2) -> key2));
    }

    /**
     * 替换模板中的统计图
     */
    private void replaceChart(Mould mould) {
        String content = mould.getContent();
        if (ObjectUtils.isEmpty(content)) {
            return;
        }

        Document document = Jsoup.parse(content);

        List<Element> chartElements = JsoupUtil.getElementsByAttr(document, "data-type", "chart");
        for (Element chartElement : chartElements) {
            ColumnarJsonQo columnar = JSON.parseObject(chartElement.attr("data-json"), ColumnarJsonQo.class);
            InstCommon newInst = monitorService.getOtherFieldInst(columnar.getInstId(), mould.getFieldNum());
            if (ObjectUtils.isEmpty(newInst)) {
                continue;
            }
            columnar.setInstId(newInst.getId());
            AttrCommon attr = monitorService.getOtherFieldAttr(newInst.getId(), columnar.getAttrId());
            columnar.setAttrId(attr == null ? null : attr.getId());
            chartElement.attr("data-json", JSON.toJSONString(columnar));

            System.out.println(chartElement.html());
        }
        mould.setContent(document.html());
    }


    /**
     * 替换模板中的过程线
     */
    private void replaceLine(Mould mould) {
        String content = mould.getContent();
        if (ObjectUtils.isEmpty(content)) {
            return;
        }

        Document document = Jsoup.parse(content);

        List<Element> lineWrapElements = JsoupUtil.getElementsByAttr(document, "data-type", "lineWrap");
        for (Element lineWrapElement : lineWrapElements) {
            ProcessLineJsonQo lineJsonQo = JSON.parseObject(lineWrapElement.attr("data-json"), ProcessLineJsonQo.class);

            InstCommon newInst = monitorService.getOtherFieldInst(lineJsonQo.getInstId(), mould.getFieldNum());
            if (ObjectUtils.isEmpty(newInst)) {
                lineWrapElement.html("");
                continue;
            }
            lineJsonQo.setInstId(newInst.getId());

            AttrCommon leftAttr = monitorService.getOtherFieldAttr(newInst.getId(), lineJsonQo.getLeftAttrId());
            lineJsonQo.setLeftAttrId(leftAttr.getId());
            if (lineJsonQo.getRightAttrId() != null) {
                AttrCommon rightAttr = monitorService.getOtherFieldAttr(newInst.getId(), lineJsonQo.getRightAttrId());
                lineJsonQo.setRightAttrId(rightAttr.getId());
            }

            int columnsNum = getColumnsNum(lineJsonQo.getOccupyWidth());
            List<ChartNumVo> vos = tabService.processLineNum(mould.getFieldNum(), lineJsonQo);

            String lineHtml = getLineHtml(vos, columnsNum);
            lineWrapElement.html(lineHtml);
        }
        mould.setContent(document.html());
    }

    private int getColumnsNum(String occupyWidth) {
        if (ObjectUtils.isEmpty(occupyWidth)) {
            return 1;
        }
        if (occupyWidth.contains("一半")) {
            return 2;
        }
        if (occupyWidth.contains("三分之一")) {
            return 3;
        }
        if (occupyWidth.contains("四分之一")) {
            return 4;
        }
        return 1;
    }

    private String getLineHtml(List<ChartNumVo> vos, int columnsNum) {
        if (ObjectUtils.isEmpty(vos)) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (ChartNumVo vo : vos) {
            sb.append("<img class=\"img-holder mce-img-holder\" data-path=\"@{img-path}\" data-type=\"line\" data-placeholder=\"{&quot;caption&quot;:&quot;【过程线图】&quot;,&quot;columns&quot;:" + columnsNum + ",&quot;formCfg&quot;:&quot;" + JSON.toJSONString(vo.getLabels()).replace("\"", "\\&quot;") + "&quot;}\" data-json=\"" + vo.getJsonParams().replace("\"", "&quot;") + "\">");
        }
        return sb.toString();
    }

    /**
     * 替换模板中的表格
     */
    private void replaceTable(Mould mould) {
        String content = mould.getContent();
        if (ObjectUtils.isEmpty(content)) {
            return;
        }

        Document document = Jsoup.parse(content);
        Elements typeElements = document.getElementsByAttribute("data-type");
        for (Element element : typeElements) {
            String dataType = element.attr("data-type");
            if (dataType.startsWith("pre_table")) {
                /* 预制格式表格 */
                TabVo tabVo = monitorService.preTabListRemote(Integer.parseInt(dataType.split("_")[2]));

                replaceRemark(element, tabVo.getDesc());
                replacePreTabHtml(element, tabVo.getPreTabVos());
            } else if (dataType.startsWith("custom_table")) {
                /* 自定义表格 */
                replaceCustomTabHtml(mould.getFieldNum(), element, element.attr("data-json"));
            }
        }

        Elements desc1 = document.getElementsByAttributeValue("data-type", "pre_table_desc_1");
        if (!ObjectUtils.isEmpty(desc1)) {
            TabVo tabVo = monitorService.preTabListRemote(1);
            for (Element element : desc1) {
                element.text(tabVo.getDesc());
            }
        }
        Elements desc2 = document.getElementsByAttributeValue("data-type", "pre_table_desc_2");
        if (!ObjectUtils.isEmpty(desc2)) {
            TabVo tabVo = monitorService.preTabListRemote(2);
            for (Element element : desc2) {
                element.text(tabVo.getDesc());
            }
        }
        mould.setContent(document.html());
    }

    /**
     * 替换描述
     */
    private void replaceRemark(Element element, String desc) {
        Element sibling = element.nextElementSibling();
        if (sibling != null && sibling.text().startsWith("注")) {
            sibling.text("注：1、" + desc);
        }
    }

    private void replaceAttr(Mould mould) {
        String content = mould.getContent();
        if (ObjectUtils.isEmpty(content)) {
            return;
        }

        Document document = Jsoup.parse(content);
        Elements typeElements = document.getElementsByAttribute("data-type");
        System.out.println("size:" + typeElements.size());
        for (Element typeElement : typeElements) {
            String dataType = typeElement.attr("data-type");
            System.out.println("dataType:" + dataType);
            if (!"attr".equals(dataType)) {
                continue;
            }

            String dataJson = typeElement.attr("data-json");
            dataJson = dataJson.replace("&quot;", "\"");
            AttrStatJsonQo statAttr = JSON.parseObject(dataJson, AttrStatJsonQo.class);
            System.out.println("statAttr:" + statAttr);

            /* 替换分量ID */
            if (!ObjectUtils.isEmpty(statAttr)) {
                Integer instId = monitorService.getInstIdByAttrId(statAttr.getAttrId());
                InstCommon newInst = monitorService.getOtherFieldInst(instId, mould.getFieldNum());
                if (newInst != null) {
                    AttrCommon newAttr = monitorService.getOtherFieldAttr(newInst.getId(), statAttr.getAttrId());
                    System.out.println("newAttr:" + newAttr);
                    statAttr.setAttrId(newAttr.getId());
                }
            }

            typeElement.attr("data-json", JSON.toJSONString(statAttr));

        }
        mould.setContent(document.html());
    }

    /**
     * 替换自定义格式html
     */
    private void replaceCustomTabHtml(String fieldNum, Element typeElement, String jsonParams) {
        CustomTabQo customTab = JSON.parseObject(jsonParams, CustomTabQo.class);
        /* 完成仪器ID和分量ID的替换 */
        System.out.println("替换前customTab:" + customTab);
        replaceInstIdAndAttrId(fieldNum, customTab);
        System.out.println("替换后customTab:" + customTab);

        List<List<Td>> bodyTrs = tabService.customTabList(fieldNum, customTab).getBodyTrs();

        String style = getFirstTdStyle(typeElement);
        StringBuilder sb = new StringBuilder();
        for (List<Td> tds : bodyTrs) {
            if (ObjectUtils.isEmpty(tds)) {
                continue;
            }

            sb.append("<tr>");
            for (Td td : tds) {
                System.out.println("td.getJ():" + td.getJ());
                String id = CommonUtil.uuid();
                if (td.getV() != null && td.getV().startsWith("@{")) {
                    sb.append("<td style=\"" + style + "\" data-json=\"" + td.getJ().replace("\"", "&quot;") + "\" colspan=\"" + td.getC() + "\" rowspan=\"" + td.getR() + "\" id=\"" + id + "\" class=\"table-variable\" data-type=\"table\" contenteditable=\"false\" data-mce-id=\"" + id + "\" data-dblclick=\"false\" data-var-type=\"table\">" + td.getV() + "</td>");
                } else {
                    sb.append("<td style=\"" + style + "\" colspan=\"" + td.getC() + "\" rowspan=\"" + td.getR() + "\">" + td.getV() + "</td>");
                }
            }
            sb.append("</tr>");
        }

        System.out.println("=======sb====");
        System.out.println(sb.toString());
        System.out.println("=======sb====");

        setText2Tbody(typeElement, sb);
    }

    /**
     * 完成仪器ID和分量ID的替换
     */
    private void replaceInstIdAndAttrId(String fieldNum, CustomTabQo customTab) {
        /* 替换仪器ID */
        InstCommon newInst = monitorService.getOtherFieldInst(customTab.getInstId(), fieldNum);
        if (newInst == null) {
            return;
        }
        customTab.setInstId(newInst.getId());

        /* 替换分量ID */
        List<StatAttr> statAttrs = customTab.getStatAttrs();
        if (ObjectUtils.isEmpty(statAttrs)) {
            return;
        }
        for (StatAttr statAttr : statAttrs) {
            AttrCommon newAttr = monitorService.getOtherFieldAttr(newInst.getId(), statAttr.getAttrId());
            statAttr.setAttrId(newAttr.getId());
        }
    }

    /**
     * 替换预制格式html
     */
    private void replacePreTabHtml(Element typeElement, List<PreTabVo> preTabVos) {
        String style = getFirstTdStyle(typeElement);
        StringBuilder sb = new StringBuilder();
        for (PreTabVo vo : preTabVos) {
            sb.append("<tr>");
            sb.append("<td style=\"" + style + "\" colspan=\"1\" rowspan=\"1\">" + vo.getItemName() + "</td>");
            sb.append("<td style=\"" + style + "\" colspan=\"1\" rowspan=\"1\">" + vo.getInstName() + "</td>");
            sb.append("<td style=\"" + style + "\" colspan=\"1\" rowspan=\"1\">" + vo.getPointNos() + "</td>");
            sb.append("<td style=\"" + style + "\" colspan=\"1\" rowspan=\"1\">" + vo.getPointNum() + "</td>");
            sb.append("<td style=\"" + style + "\" colspan=\"1\" rowspan=\"1\">" + vo.getModelName() + "</td>");
            sb.append("<td style=\"" + style + "\" colspan=\"1\" rowspan=\"1\">" + vo.getFrequency() + "</td>");
            sb.append("</tr>");
        }

        setText2Tbody(typeElement, sb);
    }

    /**
     * 重新设置tbody内容
     */
    private void setText2Tbody(Element typeElement, StringBuilder sb) {
        Element tbody = typeElement.getElementsByTag("tbody").first();
        if (!ObjectUtils.isEmpty(tbody)) {
            tbody.html(sb.toString());
        }
    }

    /**
     * 获取第一个td单元格样式
     */
    private String getFirstTdStyle(Element typeElement) {
        Elements tds = typeElement.getElementsByTag("td");
        if (!ObjectUtils.isEmpty(tds)) {
            return tds.first().attr("style");
        }
        Elements ths = typeElement.getElementsByTag("th");
        if (!ObjectUtils.isEmpty(ths)) {
            return ths.first().attr("style");
        }
        return "text-align: center; background-color: rgb(255, 255, 255);";
    }

    public String getRefNameByRefId(Integer refMouldId, String refId) {
        Mould mould = mouldMapper.getById(refMouldId);
        if (mould != null) {
            List<ChartNum> chartNumsByMould = getChartNumsByMould(mould.getContent());
            for (ChartNum chartNum : chartNumsByMould) {
                if (chartNum.getId().equals(refId)) {
                    return chartNum.getContent();
                }
            }
        }
        return null;
    }

    /**
     * 模板预览
     */
    public String mouldPreview(Integer mouldId) {
        Mould mould = mouldMapper.getById(mouldId);
        if (ObjectUtils.isEmpty(mould) || ObjectUtils.isEmpty(mould.getContent())) {
            return null;
        }

        /* 替换变量 */
        replaceVar(mould);

        /* 生成word并转为pdf */
        String docPath = linuxPath + "/reports_review/mould/" + mould.getId() + "/";
        FileUtil.makeSureDirExist(docPath);
        EasyWord.html2NormalWord(mould.getContent(), "/home/<USER>", docPath + "模板预览.docx");

        WordToPdfUtil.convert(docPath + "模板预览.docx", docPath + "模板预览.pdf");
        return "owp-data-files/reports_review/mould/" + mould.getId() + "/模板预览.pdf";
    }

}
