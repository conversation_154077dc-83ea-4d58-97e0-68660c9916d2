package com.hdec.data.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.hdec.common.domain.AttrCommon;
import com.hdec.common.domain.PointCommon;
import com.hdec.common.domain.SeaFacilityCommon;
import com.hdec.common.util.CommonUtil;
import com.hdec.common.util.ReportUtil;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.feign.WindService;
import com.hdec.data.qo.*;
import com.hdec.data.vo.ChartNumVo;
import com.hdec.data.vo.LineLabel;
import com.hdec.data.vo.TabResVo;
import com.hdec.data.vo.Td;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @date 2023/7/29
 */
@Slf4j
@Service
public class ReportTabService {

    @Autowired
    private WindService windService;

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private ReportVarService varService;

    /**
     * 自定义表格数据
     */
    public TabResVo customTabList(String fieldNum, CustomTabQo qo) {
        /* 获取风场下所有海上设施、测点 */
        List<PointCommon> fieldPoints = monitorService.getPointsByFieldNum(fieldNum);
        Map<Integer, AttrCommon> fieldAttrMap = this.getFieldAttrMap(fieldNum);
        Map<String, SeaFacilityCommon> fieldSeaMap = getFieldSeaMap(fieldNum);
        List<SeaFacilityCommon> fieldSeaFacility = windService.allSeaFacility(fieldNum);

        List<List<Td>> headerTrs = new ArrayList<>();
        List<List<Td>> bodyTrs = new ArrayList<>();
        TabResVo vo = new TabResVo(JSON.toJSONString(qo), headerTrs, bodyTrs);

        /* 设置表头 */
        setTabHeader(headerTrs, qo, fieldAttrMap);

        /* 设置表体 */
        setTabBody(fieldNum, bodyTrs, qo, fieldPoints, fieldSeaMap, fieldAttrMap, fieldSeaFacility);
        return vo;
    }

    /**
     * 获取目标海上设施
     */
    private List<String> getSeaFacilityIds(String item, List<SeaFacilityCommon> fieldSeaFacility) {
        if (ObjectUtils.isEmpty(fieldSeaFacility)) {
            return Collections.emptyList();
        }

        if ("所有风机".equals(item)) {
            return fieldSeaFacility.stream().filter(e -> e.getId().startsWith("1-")).map(SeaFacilityCommon::getId).collect(Collectors.toList());
        } else if ("典型风机".equals(item)) {
            return fieldSeaFacility.stream().filter(e -> e.getId().startsWith("1-")).filter(e -> e.getIsTypical() != null && e.getIsTypical()).map(SeaFacilityCommon::getId).collect(Collectors.toList());
        } else if ("非典型风机".equals(item)) {
            return fieldSeaFacility.stream().filter(e -> e.getId().startsWith("1-")).filter(e -> e.getIsTypical() == null || !e.getIsTypical()).map(SeaFacilityCommon::getId).collect(Collectors.toList());
        } else if ("升压站".equals(item)) {
            return fieldSeaFacility.stream().filter(e -> e.getId().startsWith("2-")).map(SeaFacilityCommon::getId).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 设置表体
     */
    private void setTabBody(String fieldNum, List<List<Td>> trs, CustomTabQo qo, List<PointCommon> fieldPoints, Map<String, SeaFacilityCommon> fieldSeaMap, Map<Integer, AttrCommon> fieldAttrMap, List<SeaFacilityCommon> fieldSeaFacility) {
        // 唯一序号
        AtomicInteger uniqueSeq = new AtomicInteger(1);

        /* 获取相关测点并按海上设施分组 */
        List<String> seaIds;
        if (qo.getFacilityId() != null && !qo.getFacilityId().trim().isEmpty()) {
            /* 如果绑定的有设施，直接使用绑定的设施 */
            seaIds = Collections.singletonList(qo.getFacilityId());
        } else {
            seaIds = getSeaFacilityIds(qo.getType(), fieldSeaFacility);
        }
        LinkedHashMap<String, List<PointCommon>> seaMap = fieldPoints.stream().filter(e -> seaIds.contains(e.getSeaFacilityId())).filter(e -> qo.getInstId().equals(e.getInstId())).collect(Collectors.groupingBy(PointCommon::getSeaFacilityId, LinkedHashMap::new, Collectors.toList()));

        /* 排序 */
        List<SeaFacilityCommon> sortedFacilities = seaMap.keySet().stream().map(fieldSeaMap::get)
                .filter(Objects::nonNull)
                .sorted((o1, o2) ->{
                    Integer i1 = CommonUtil.extractUniqueNum(o1.getName());
                    Integer i2 = CommonUtil.extractUniqueNum(o2.getName());
                    if (i1 == null && i2 == null) {
                        return 0;
                    }
                    if (i1 == null) {
                        return -1;
                    }
                    if (i2 == null) {
                        return -1;
                    }
                    return i1 - i2;
                })
                .collect(Collectors.toList());

        sortedFacilities.forEach(seaFacility -> {
            String seaId = seaFacility.getId();
            List<PointCommon> seaPoints = seaMap.get(seaId);
            /* 对测点进行排序处理 */
            seaPoints.sort((o1, o2) -> CommonUtil.lastNumberComparator('-').compare(o1.getNo(), o2.getNo()));

            /* 风机、测点信息 */
            if (qo.getIsByPoint()) {
                List<Td> tds = new ArrayList<>();
                trs.add(tds);

                boolean isFirstFlag = true;
                for (PointCommon point : seaPoints) {
                    if (isFirstFlag) {
                        tds.add(new Td(1, seaPoints.size(), ReportUtil.addNewRome2EngNumber(seaFacility.getName())));
                        isFirstFlag = false;
                    } else {
                        tds = new ArrayList<>();
                        trs.add(tds);
                    }

                    /* 测点编号、安装高程、安装方位 */
                    tds.add(new Td(ReportUtil.addNewRome2EngNumber(point.getNo())));
                    if (qo.getIsShowInstallEle()) {
                        tds.add(new Td(point.getInstallElevation()));
                    }
                    if (qo.getIsShowInstallOrient()) {
                        tds.add(new Td(point.getInstallOrient()));
                    }
                    /* 属性统计信息 */
                    for (StatAttr statAttr : qo.getStatAttrs()) {
                        AttrCommon attr = fieldAttrMap.get(statAttr.getAttrId());
                        if (attr == null) {
                            continue;
                        }

                        for (String direct : statAttr.getDirects()) {
                            for (String statItem : statAttr.getStatItems()) {
                                String content = "@{" + seaFacility.getName() + "/" + point.getNo() + "/" + direct + "/" + attr.getName() + "/" + statItem + "}";
                                JSONObject jsonObject = getJsonObject(uniqueSeq, seaFacility.getId(), qo.getInstId(), Arrays.asList(point.getId()), statAttr.getAttrId(), direct, statItem, null);
                                /* 设置相对变化值计算基准信息 */
                                setReferInfo(jsonObject,statAttr);
                                tds.add(new Td(content, jsonObject.toJSONString()));
                            }
                        }
                    }
                }
            } else {
                List<Td> tds = new ArrayList<>();
                trs.add(tds);

                tds.add(new Td(ReportUtil.addNewRome2EngNumber(seaFacility.getName())));
                /* 属性统计信息 */
                for (StatAttr statAttr : qo.getStatAttrs()) {
                    AttrCommon attr = fieldAttrMap.get(statAttr.getAttrId());
                    if (attr == null) {
                        continue;
                    }

                    for (String direct : statAttr.getDirects()) {
                        for (String statItem : statAttr.getStatItems()) {
                            String content = "@{" + seaFacility.getName() + "/" + (ObjectUtils.isEmpty(statAttr.getCustomVar()) ? "全部测点" : statAttr.getCustomVar()) + "/" + direct + "/" + attr.getName() + "/" + statItem + "}";
                            JSONObject jsonObject = getJsonObject(uniqueSeq, seaFacility.getId(), qo.getInstId(), null, statAttr.getAttrId(), direct, statItem, statAttr.getCustomVar());
                            tds.add(new Td(content, jsonObject.toJSONString()));
                        }
                    }
                }
            }
        });
    }

    /**
     * 组装json对象
     */
    private JSONObject getJsonObject(AtomicInteger uniqueSeq, String seaId, Integer instId, List<Integer> pointIds, Integer attrId, String direct, String statItem, String customVar) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id", "tab-" + uniqueSeq.getAndIncrement());
        jsonObject.put("seaId", seaId);
        jsonObject.put("instId", instId);
        jsonObject.put("pointIds", pointIds);
        jsonObject.put("attrId", attrId);
        jsonObject.put("direct", direct);
        jsonObject.put("statItem", statItem);
        jsonObject.put("customVar", customVar);
        return jsonObject;
    }

    /**
     * 设置相对变化值计算基准信息
     *
     * @param json json
     * @param attr attr
     */
    public void setReferInfo(JSONObject json, StatAttr attr) {
        if (attr.getReferStatItem() != null && !attr.getReferStatItem().isEmpty()) {
            json.put("referStatItem", attr.getReferStatItem());
        }
        if (attr.getReferParamId() != null && attr.getReferParamId() != 0) {
            json.put("referParamId", attr.getReferParamId());
        }
    }

    /**
     * 设置表头
     */
    private void setTabHeader(List<List<Td>> trs, CustomTabQo qo, Map<Integer, AttrCommon> fieldAttrMap) {
        List<Td> tds = new ArrayList<>();
        trs.add(tds);

        /* 风机、测点、安装高程、安装方位 */
        int rowspan = getRowspan(qo.getStatAttrs());
        String rowName = getFacilityRowName(qo.getFacilityId());
        tds.add(new Td(1, rowspan, rowName != null ? rowName : qo.getType().contains("风机") ? "风机编号" : "升压站名称"));
        if (qo.getIsByPoint() && ObjectUtils.isEmpty(qo.getStatAttrs().get(0).getCustomVar())) {
            tds.add(new Td(1, rowspan, "测点编号"));
            if (qo.getIsShowInstallEle()) {
                tds.add(new Td(1, rowspan, "安装高程"));
            }
            if (qo.getIsShowInstallOrient()) {
                tds.add(new Td(1, rowspan, "安装方位"));
            }
        }

        // 查询仪器方向
        String instDirect = monitorService.getInstDirectById(qo.getInstId());

        /* 方向、分量、统计项 */
        boolean flag = true;
        List<Td> titleSecondTds = new ArrayList<>();
        for (StatAttr statAttr : qo.getStatAttrs()) {
            AttrCommon attr = fieldAttrMap.get(statAttr.getAttrId());
            if (attr == null) {
                continue;
            }
            String unit = getAttrUnit(attr);

            int colspan = statAttr.getStatItems().length;
            if (colspan == 1) {
                /* 只会有一行，或独占1行，或上下合并 */
                for (String direct : statAttr.getDirects()) {
                    tds.add(new Td(1, rowspan, ("单向".equals(instDirect) ? "" : direct) + (ObjectUtils.isEmpty(statAttr.getCustomVar()) ? attr.getName() : statAttr.getCustomVar() + attr.getName()) + statAttr.getStatItems()[0] + unit));
                }
            } else {
                /* 有两行 */
                if (flag) {
                    trs.add(titleSecondTds);
                    flag = false;
                }
                for (String direct : statAttr.getDirects()) {
                    tds.add(new Td(colspan, 1, ("单向".equals(instDirect) ? "" : direct) + (ObjectUtils.isEmpty(statAttr.getCustomVar()) ? attr.getName() : statAttr.getCustomVar() + attr.getName())));
                }
                for (String direct : statAttr.getDirects()) {
                    for (String statItem : statAttr.getStatItems()) {
                        titleSecondTds.add(new Td(statItem + unit));
                    }
                }
            }
        }
    }

    /**
     * 获取分量单位
     */
    private String getAttrUnit(AttrCommon attr) {
        if (ObjectUtils.isEmpty(attr.getUnit())) {
            return "";
        }
        return ReportUtil.addNewRome2EngNumber("(" + attr.getUnit() + ")");
    }

    /**
     * 获取行合并数量
     */
    private int getRowspan(List<StatAttr> statAttrs) {
        /* 方向、统计项同时超过1个就需要合并行 */
        for (StatAttr statAttr : statAttrs) {
            if (statAttr.getStatItems().length > 1) {
                return 2;
            }
        }
        return 1;
    }

    /**
     * 获取某风场下所有属性
     */
    private Map<Integer, AttrCommon> getFieldAttrMap(String fieldNum) {
        List<AttrCommon> fieldAttrs = monitorService.allAttrByField(fieldNum);
        if (ObjectUtils.isEmpty(fieldAttrs)) {
            return Collections.emptyMap();
        }

        Map<Integer, AttrCommon> map = new HashMap<>(fieldAttrs.size());
        for (AttrCommon attr : fieldAttrs) {
            map.put(attr.getId(), attr);
        }
        return map;
    }

    private Map<String, SeaFacilityCommon> getFieldSeaMap(String fieldNum) {
        Map<String, SeaFacilityCommon> map = new HashMap<>();
        List<SeaFacilityCommon> fieldSeaFacilities = windService.allSeaFacility(fieldNum);
        for (SeaFacilityCommon seaFacility : fieldSeaFacilities) {
            map.put(seaFacility.getId(), seaFacility);
        }
        return map;
    }

    /**
     * 获取过程线图
     */
    public List<ChartNumVo> processLineNum(String fieldNum, ProcessLineJsonQo qo) {
        /* 获取需要用到的数据 */
        List<SeaFacilityCommon> fieldSeaFacility = windService.allSeaFacility(fieldNum);
        Map<String, SeaFacilityCommon> seaFacilityMap = fieldSeaFacility.stream().collect(Collectors.toMap(SeaFacilityCommon::getId, Function.identity(), (key1, key2) -> key2));
        List<PointCommon> fieldPoints = monitorService.getPointsByFieldNum(fieldNum);
        Map<String, List<PointCommon>> seaPointMap = fieldPoints.stream().filter(e -> qo.getInstId().equals(e.getInstId())).collect(Collectors.groupingBy(PointCommon::getSeaFacilityId));
        List<ChartNumVo> chartNums = new ArrayList<>();

        /* 获取涉及到的海上设施ID */
        List<String> seaIds = getSeaFacilityIds(qo.getSeaType(), fieldSeaFacility);
        seaIds = seaIds.stream().map(seaFacilityMap::get)
                .filter(Objects::nonNull)
                .sorted((o1, o2) -> {
                    Integer i1 = CommonUtil.extractUniqueNum(o1.getName());
                    Integer i2 = CommonUtil.extractUniqueNum(o2.getName());
                    if (i1 == null && i2 == null) {
                        return 0;
                    }
                    if (i1 == null) {
                        return -1;
                    }
                    if (i2 == null) {
                        return -1;
                    }
                    return i1 - i2;
                }).map(SeaFacilityCommon::getId)
                .collect(Collectors.toList());
        if (ObjectUtils.isEmpty(seaIds)) {
            qo.setSeaName(qo.getSeaType());
            /* 多分量走自定义的规则 */
            List<LineLabel> labels = Objects.equals("multi", qo.getAttrType()) ?
                    getLineMultipleLabels(qo) : getLineLabels(qo);
            chartNums.add(new ChartNumVo(labels, JSON.toJSONString(qo)));
            return chartNums;
        }

        for (String seaId : seaIds) {
            SeaFacilityCommon seaFacility = seaFacilityMap.get(seaId);
            List<PointCommon> seaPoints = seaPointMap.get(seaId);
            if (seaFacility == null || ObjectUtils.isEmpty(seaPoints)) {
                continue;
            }

            qo.setSeaId(seaId);
            qo.setSeaName(seaFacility.getName());
            for (int i = 0; i < seaPoints.size(); i += qo.getPointNum()) {
                List<Integer> pointIds = new ArrayList<>();
                List<String> pointNos = new ArrayList<>();
                for (int j = 0; j < qo.getPointNum() && i + j < seaPoints.size(); j++) {
                    PointCommon point = seaPoints.get(i + j);
                    pointIds.add(point.getId());
                    pointNos.add(point.getNo());
                }
                qo.setPointIds(pointIds);
                qo.setPointNos(pointNos);
                /* 多分量走自定义的规则 */
                List<LineLabel> labels = Objects.equals("multi", qo.getAttrType()) ?
                        getLineMultipleLabels(qo) : getLineLabels(qo);
                chartNums.add(new ChartNumVo(labels, JSON.toJSONString(qo)));
            }
        }
        return chartNums;
    }

    /**
     * 获取频谱图
     */
    public List<ChartNumVo> freqNum(String fieldNum, ProcessLineJsonQo qo) {
        qo.setPointNum(1);

        /* 获取需要用到的数据 */
        List<SeaFacilityCommon> fieldSeaFacility = windService.allSeaFacility(fieldNum);
        Map<String, SeaFacilityCommon> seaFacilityMap = fieldSeaFacility.stream().collect(Collectors.toMap(SeaFacilityCommon::getId, Function.identity(), (key1, key2) -> key2));
        List<PointCommon> fieldPoints = monitorService.getPointsByFieldNum(fieldNum);
        Map<String, List<PointCommon>> seaPointMap = fieldPoints.stream().filter(e -> qo.getTarget().equals(e.getTarget())).collect(Collectors.groupingBy(PointCommon::getSeaFacilityId));
        List<ChartNumVo> chartNums = new ArrayList<>();

        /* 获取涉及到的海上设施ID */
        List<String> seaIds = getSeaFacilityIds(qo.getSeaType(), fieldSeaFacility);
        System.out.println("fieldSeaFacility:"+fieldSeaFacility);
        System.out.println("seaIds:"+seaIds);
        System.out.println("seaPointMap:"+seaPointMap);
        seaIds = seaIds.stream().map(seaFacilityMap::get)
                .filter(Objects::nonNull)
                .sorted((o1, o2) -> {
                    Integer i1 = CommonUtil.extractUniqueNum(o1.getName());
                    Integer i2 = CommonUtil.extractUniqueNum(o2.getName());
                    if (i1 == null && i2 == null) {
                        return 0;
                    }
                    if (i1 == null) {
                        return -1;
                    }
                    if (i2 == null) {
                        return -1;
                    }
                    return i1 - i2;
                }).map(SeaFacilityCommon::getId)
                .collect(Collectors.toList());

        if (ObjectUtils.isEmpty(seaIds) && qo.getSeaType().startsWith("@{")) {
            // @{风机编号}
            qo.setSeaName(qo.getSeaType());
            for (String direct : qo.getDirects()) {
                qo.setDirect(direct);
                List<LineLabel> labels = getLineLabels2(qo);
                chartNums.add(new ChartNumVo(labels, JSON.toJSONString(qo)));
            }
        } else {
            for (String seaId : seaIds) {
                SeaFacilityCommon seaFacility = seaFacilityMap.get(seaId);
                List<PointCommon> seaPoints = seaPointMap.get(seaId);
                if (seaFacility == null || ObjectUtils.isEmpty(seaPoints)) {
                    continue;
                }

                qo.setSeaId(seaId);
                qo.setSeaName(seaFacility.getName());

                for (PointCommon seaPoint : seaPoints) {
                    qo.setPointId(seaPoint.getId());
                    qo.setPointNo(seaPoint.getNo());
                    for (String direct : qo.getDirects()) {
                        qo.setDirect(direct);
                        List<LineLabel> labels = getLineLabels2(qo);
                        chartNums.add(new ChartNumVo(labels, JSON.toJSONString(qo)));
                    }
                }
            }
        }
        return chartNums;
    }

    /**
     * 获取过程线标签说明
     */
    private List<LineLabel> getLineMultipleLabels(ProcessLineJsonQo qo) {
        List<LineLabel> labels = new ArrayList<>();
        labels.add(new LineLabel("海上设施", qo.getSeaName()));
        if (!ObjectUtils.isEmpty(qo.getPointNos())) {
            labels.add(new LineLabel("测点编号", Joiner.on(",").join(qo.getPointNos())));
        }
        labels.add(new LineLabel("图片规格", qo.getImgType()));
        labels.add(new LineLabel("线条标记", qo.getIsMark() ? "是" : "否"));
        labels.add(new LineLabel("采样频率", qo.getPointNum() == null ? "不采样" : qo.getPointNum() + "数据点/小时"));
        String attrName = "未知分量";
        List<Integer> attrs = qo.getMultiAttrs();
        if (attrs == null || attrs.isEmpty()) {
            attrName = "未设置分量";
        }else {
            attrName = attrs.stream().map(attId -> {
                AttrCommon attrCommon = monitorService.getProAttrById(attId);
                if (attrCommon == null) {
                    return "未知分量";
                }else {
                    return attrCommon.getName();
                }
            }).collect(Collectors.joining(","));
        }
        labels.add(new LineLabel("左侧分量",attrName));
        labels.add(new LineLabel("左侧别名", ObjectUtils.isEmpty(qo.getLeftAttrAlias()) ? "无" : qo.getLeftAttrAlias()));
        labels.add(new LineLabel("左侧方向", Joiner.on(",").join(qo.getLeftDirects())));
        labels.add(new LineLabel("随值调整", qo.getLeftIsAuto() ? "是" : "否"));

        if (qo.getLimitLow() != null || qo.getLimitHigh() != null) {
            String low = qo.getLimitLow() == null ? "/" : String.valueOf(qo.getLimitLow());
            String high = qo.getLimitHigh() == null ? "/" : String.valueOf(qo.getLimitHigh());
            labels.add(new LineLabel("限值", low + " ~ " + high));
        }
        labels.add(new LineLabel("图例别名", qo.getUseDirectionAlias() != null && qo.getUseDirectionAlias() ? "是" : "否"));
        return labels;
    }

    /**
     * 获取过程线标签说明
     */
    private List<LineLabel> getLineLabels(ProcessLineJsonQo qo) {
        List<LineLabel> labels = new ArrayList<>();
        labels.add(new LineLabel("海上设施", qo.getSeaName()));
        if (!ObjectUtils.isEmpty(qo.getPointNos())) {
            labels.add(new LineLabel("测点编号", Joiner.on(",").join(qo.getPointNos())));
        }
        labels.add(new LineLabel("图片规格", qo.getImgType()));
        labels.add(new LineLabel("线条标记", qo.getIsMark() ? "是" : "否"));
        labels.add(new LineLabel("采样频率", qo.getPointNum() == null ? "不采样" : qo.getPointNum() + "数据点/小时"));

        AttrCommon leftAttr = monitorService.getProAttrById(qo.getLeftAttrId());
        labels.add(new LineLabel("左侧分量", leftAttr == null ? "未知分量" : leftAttr.getName()));
        labels.add(new LineLabel("左侧别名", ObjectUtils.isEmpty(qo.getLeftAttrAlias()) ? "无" : qo.getLeftAttrAlias()));
        labels.add(new LineLabel("左侧方向", Joiner.on(",").join(qo.getLeftDirects())));
        labels.add(new LineLabel("随值调整", qo.getLeftIsAuto() ? "是" : "否"));

        if (qo.getRightAttrId() != null) {
            AttrCommon rightAttr = monitorService.getProAttrById(qo.getRightAttrId());
            labels.add(new LineLabel("右侧分量", rightAttr == null ? "未知分量" : rightAttr.getName()));
            labels.add(new LineLabel("右侧别名", ObjectUtils.isEmpty(qo.getRightAttrAlias()) ? "无" : qo.getRightAttrAlias()));
            labels.add(new LineLabel("右侧方向", Joiner.on(",").join(qo.getRightDirects())));
            labels.add(new LineLabel("随值调整", qo.getRightIsAuto() ? "是" : "否"));
        }
        if (qo.getLimitLow() != null || qo.getLimitHigh() != null) {
            String low = qo.getLimitLow() == null ? "/" : String.valueOf(qo.getLimitLow());
            String high = qo.getLimitHigh() == null ? "/" : String.valueOf(qo.getLimitHigh());
            labels.add(new LineLabel("限值", low + " ~ " + high));
        }
        labels.add(new LineLabel("图例别名", qo.getUseDirectionAlias() != null && qo.getUseDirectionAlias() ? "是" : "否"));
        return labels;
    }

    /**
     * 获取过程线标签说明
     */
    private List<LineLabel> getLineLabels2(ProcessLineJsonQo qo) {
        List<LineLabel> labels = new ArrayList<>();
        if (!ObjectUtils.isEmpty(qo.getSeaName())) {
            labels.add(new LineLabel("海上设施", qo.getSeaName()));
        }
        if (!ObjectUtils.isEmpty(qo.getPointNo())) {
            labels.add(new LineLabel("测点编号", qo.getPointNo()));
        }
        if (!ObjectUtils.isEmpty(qo.getAttrName())) {
            labels.add(new LineLabel("分量", qo.getAttrName()));
        }
        if (!ObjectUtils.isEmpty(qo.getDirect())) {
            labels.add(new LineLabel("方向", parseDirects(qo.getDirect())));
        }
        return labels;
    }

    private String parseDirects(String direct) {
        if ("1".equals(direct)) {
            return "X方向";
        }
        if ("2".equals(direct)) {
            return "Y方向";
        }
        return "Z方向";
    }

    public TabResVo historyEigenTabList(String fieldNum, HistoryEigenTabQo qo) {
        StatAttr statAttr = qo.getStatAttr();
        /* 获取需要的分量 */
        Map<Integer, AttrCommon> fieldAttrMap = this.getFieldAttrMap(fieldNum);
        AttrCommon attrCommon = fieldAttrMap.get(qo.getStatAttr().getAttrId());

        List<List<Td>> headerTrs = getTabHeader(statAttr, attrCommon, qo.getIsShowInstallEle());

        List<List<Td>> bodyTrs = getTabBody(statAttr, attrCommon);

        return new TabResVo(JSON.toJSONString(qo), headerTrs, bodyTrs);
    }

    List<List<Td>> getTabHeader(StatAttr statAttr, AttrCommon attr, boolean showElevation) {
        List<List<Td>> headerTrs = new ArrayList<>();
        /* 第一行 */
        List<Td> firstTd = new ArrayList<>();
        firstTd.add(new Td(1, 2, "时间"));

        /* 第二行 */
        List<Td> secondTd = new ArrayList<>();

        if (ObjectUtils.isEmpty(statAttr) || ObjectUtils.isEmpty(attr)) {
            return headerTrs;
        }
        /* 获取需要仪器方向 */
        String[] directs = ObjectUtils.isEmpty(statAttr.getDirects()) ? new String[]{} : statAttr.getDirects();

        /* 获取需要的分量 */
        String[] statItems = ObjectUtils.isEmpty(statAttr.getStatItems()) ? new String[]{} : statAttr.getStatItems();

        String unit = getAttrUnit(attr);

        /* 第一行采集点合并数 */
        int colspan = directs.length;
        /* 第一行设置 测点-分量-单位 */
        String name = (ObjectUtils.isEmpty(statAttr.getCustomVar()) ? attr.getName() : statAttr.getCustomVar() + attr.getName());
        String prefix = (showElevation ? "<p>"+getTagSpan("高程")+"<p>" : "") + getTagSpan("测点编号");
        Arrays.stream(statItems).map(stat -> new Td(colspan, 1, prefix + name + stat + unit)).forEach(firstTd::add);
        /* 第一行设置 方向 */
        Arrays.stream(statItems).forEach(stat -> Arrays.stream(directs).map(Td::new).forEach(secondTd::add));
        headerTrs.add(firstTd);
        headerTrs.add(secondTd);
        return headerTrs;
    }

    List<List<Td>> getTabBody(StatAttr statAttr, AttrCommon attr) {
        List<List<Td>> bodyTrs = new ArrayList<>();
        List<Td> tds = new ArrayList<>();
        AtomicInteger uniqueSeq = new AtomicInteger(0);
        tds.add(new Td(getTagSpan("yyyy年MM月")));

        /* 获取需要仪器方向 */
        String[] directs = ObjectUtils.isEmpty(statAttr.getDirects()) ? new String[]{} : statAttr.getDirects();

        /* 获取需要的分量 */
        String[] statItems = ObjectUtils.isEmpty(statAttr.getStatItems()) ? new String[]{} : statAttr.getStatItems();

        /* 第一行设置 方向 */
        Arrays.stream(statItems).forEach(stat -> Arrays.stream(directs).map(direct -> {
            String content = "测点编号" + "/" + direct + "/" + attr.getName() + "/" + stat;
            return new Td(getTagSpan(content));
        }).forEach(tds::add));

        bodyTrs.add(tds);
        return bodyTrs;
    }

    public String getTagSpan(String content) {
        return "<span class=\"variable\" draggable=\"true\" >@{" + content + "}</span>";
    }


    /**
     * 自定义表格数据
     */
    public TabResVo weekStatisticsTabList(String fieldNum, WeekStatisticsTabQo qo) {
        /* 获取需要的分量 */
        Map<Integer, AttrCommon> fieldAttrMap = this.getFieldAttrMap(fieldNum);
        AttrCommon attrCommon = fieldAttrMap.get(qo.getStatAttr().getAttrId());

        /* 获取需要的风机 */
        List<SeaFacilityCommon> fieldSeaFacility = windService.allSeaFacility(fieldNum);
        List<String> seaIds;
        if (qo.getFacilityId() != null && !qo.getFacilityId().trim().isEmpty()) {
            /* 如果绑定的有设施，直接使用绑定的设施 */
            seaIds = Collections.singletonList(qo.getFacilityId());
        } else {
            seaIds = getSeaFacilityIds(qo.getType(), fieldSeaFacility);
        }
        List<SeaFacilityCommon> facilities = fieldSeaFacility.stream()
                .filter(facility -> seaIds.contains(facility.getId())).collect(Collectors.toList());

        /* 获取需要的采集点 */
        List<PointCommon> fieldPoints = monitorService.getPointsByFieldNum(fieldNum);
        List<PointCommon> points = fieldPoints.stream()
                .filter(e -> seaIds.contains(e.getSeaFacilityId()))
                .filter(e -> qo.getInstId().equals(e.getInstId())).collect(Collectors.toList());

        /* 构建表头 */
        List<List<Td>> headerTrs = getWeekTabHeader(qo, attrCommon);

        /* 构建表体 */
        List<List<Td>> bodyTrs = getWeekTabBody(facilities, points, qo.getInstId(), qo.getStatAttr(), attrCommon);

        return new TabResVo(JSON.toJSONString(qo), headerTrs, bodyTrs);
    }

    List<List<Td>> getWeekTabHeader(WeekStatisticsTabQo qo, AttrCommon attr) {
        String rowName = getFacilityRowName(qo.getFacilityId());
        rowName = rowName != null ? rowName : qo.getType().contains("风机") ? "风机编号" : "升压站名称";
        StatAttr statAttr = qo.getStatAttr();
        List<List<Td>> headerTrs = new ArrayList<>();
        List<Td> tds1 = new ArrayList<>();
        tds1.add(new Td(1, 2, rowName));
        tds1.add(new Td(2, 2, "测点编号"));
        tds1.add(new Td(1, 2, "安装高程(m)"));
        String name = attr.getName() + getAttrUnit(attr);
        int length = statAttr.getStatItems().length;
        IntStream.range(0, 4).forEach(index -> {
            String content = "第" + (index + 1) + "周" + name;
            tds1.add(new Td(length, 1, content));
        });
        tds1.add(new Td(length, 1, "本月" + name));
        headerTrs.add(tds1);
        List<Td> tds2 = new ArrayList<>();
        IntStream.range(0, 5).forEach(index -> {
            for (String item : statAttr.getStatItems()) {
                tds2.add(new Td(item));
            }
        });
        headerTrs.add(tds2);
        return headerTrs;
    }

    List<List<Td>> getWeekTabBody(List<SeaFacilityCommon> facilities, List<PointCommon> points, Integer instId, StatAttr statAttr, AttrCommon attr) {
        // 唯一序号
        AtomicInteger uniqueSeq = new AtomicInteger(1);

        Map<String, List<PointCommon>> pointMap = points.stream().collect(Collectors.groupingBy(PointCommon::getSeaFacilityId));
        String[] directs = statAttr.getDirects();
        if (directs == null || directs.length == 0) {
            return Collections.emptyList();
        }
        List<List<Td>> bodyTrs = new ArrayList<>();
        facilities.forEach(facility -> {
            List<PointCommon> ps = pointMap.get(facility.getId());

            if (ps == null || ps.isEmpty()) {
                return;
            }
            int rowSize = ps.size() * directs.length;
            Td facilityNoTd = new Td(1, rowSize, facility.getName());
            /* 根据测点绘制行 */
            IntStream.range(0, ps.size()).forEach(index -> {
                PointCommon p = ps.get(index);
                Td pointNoTd = new Td(1, directs.length, p.getNo());
                Td ElevTd = new Td(1, directs.length, p.getInstallElevation());
                IntStream.range(0, directs.length).forEach(di -> {
                    String direct = directs[di];
                    List<Td> tds = new ArrayList<>();
                    if (di == 0 && index == 0) {
                        tds.add(facilityNoTd);
                    }
                    if (di == 0) {
                        tds.add(pointNoTd);
                    }
                    tds.add(new Td(direct));
                    if (di == 0) {
                        tds.add(ElevTd);
                    }
                    IntStream.range(0, 5).forEach(i -> {
                        for (String statItem : statAttr.getStatItems()) {
                            int week = i < 4 ? i + 1 : 0;
                            String content = "@{" + facility.getName() + "/" + p.getNo() + "/" + direct + "/" + attr.getName() + "/" + statItem + "}";
                            JSONObject param = getParamJson(week, uniqueSeq, facility.getId(), instId, p.getId(), statAttr.getAttrId(), direct, statItem, null);
                            tds.add(new Td(content, param.toJSONString()));
                        }
                    });
                    bodyTrs.add(tds);

                });
            });
        });
        return bodyTrs;
    }

    /**
     * 组装json对象
     */
    private JSONObject getParamJson(int week, AtomicInteger uniqueSeq, String seaId, Integer instId, Integer pointId, Integer attrId, String direct, String statItem, String customVar) {
        JSONObject param = getJsonObject(uniqueSeq, seaId, instId, Collections.singletonList(pointId), attrId, direct, statItem, customVar);
        param.put("week", week);
        param.put("pointId", pointId);
        return param;
    }

    public String getFacilityRowName(String facilityId) {
        if (facilityId == null || facilityId.trim().isEmpty()) {
            return null;
        } else if (facilityId.startsWith("1-")) {
            return "风机编号";
        } else if (facilityId.startsWith("2-")) {
            return "升压站名称";
        } else {
            return null;
        }
    }

}
