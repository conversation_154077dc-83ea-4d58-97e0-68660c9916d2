package com.hdec.data.service;

import com.alibaba.fastjson.JSON;
import com.hdec.common.constant.Constant;
import com.hdec.common.domain.PointCommon;
import com.hdec.common.domain.SeaFacilityCommon;
import com.hdec.data.domain.CustomVarParam;
import com.hdec.data.domain.template.ReportVar;
import com.hdec.data.domain.template.ReportVarVo;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.feign.WindService;
import com.hdec.data.mapper.VarMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 变量业务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ReportVarService {

    @Autowired
    private VarMapper varMapper;

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private WindService windService;

    /**
     * 报告变量列表
     */
    public ReportVarVo list() {
        /* 获取所有变量 */
        List<ReportVar> vars = varMapper.list();

        /* 按变量类型分组 */
        Map<String, List<ReportVar>> varMap = vars.stream().collect(Collectors.groupingBy(ReportVar::getType));
        return new ReportVarVo(varMap.get(Constant.REPORT_VAR_BASE), varMap.get(Constant.REPORT_VAR_MULTI), varMap.get(Constant.REPORT_VAR_CHART_NUM));
    }

    /**
     * 添加变量修饰符
     */
    public String addVarModifier(String name) {
        return "@{" + name + "}";
    }

    /**
     * 去除变量修饰符
     */
    public String removeVarModifier(String name) {
        if (ObjectUtils.isEmpty(name)) {
            return name;
        }
        return name.replace("@{", "").replace("}", "");
    }

    /**
     * 查询
     */
    public List<ReportVar> select(ReportVar var) {
        return varMapper.select(var);
    }

    /**
     * 由自定义变量名和海上设施查询其下测点ID
     */
    public List<String> getPointIdsByVarNameAndSea(String fieldNum, String varName, String seaId) {
        if (ObjectUtils.isEmpty(fieldNum) || ObjectUtils.isEmpty(varName)) {
            return Collections.emptyList();
        }

        ReportVar reportVar = varMapper.getPointIdsByVarName(fieldNum, varName);
        if (reportVar == null || ObjectUtils.isEmpty(reportVar.getParamJson())) {
            return Collections.emptyList();
        }

        String paramJson = reportVar.getParamJson();
        String type = reportVar.getType();
        List<CustomVarParam> params = JSON.parseArray(paramJson, CustomVarParam.class);
        if (Objects.equals(type, "custom")) {
            if (ObjectUtils.isEmpty(seaId)){
                return Collections.emptyList();
            }
            for (CustomVarParam param : params) {
                if (seaId.equals(param.getSeaId())) {
                    return param.getPointIds().stream().map(Objects::toString).collect(Collectors.toList());
                }
            }
        } else if (Objects.equals(type, "global")) {
            List<String> pointIds = new ArrayList<>();
            params.forEach(param -> pointIds.addAll(param.getPointIds().stream().map(Objects::toString).collect(Collectors.toList())));
            return pointIds;
        }

        return Collections.emptyList();
    }

    /**
     * 保存
     */
    public void save(ReportVar var) {
        var.setParamJson(JSON.toJSONString(var.getParams()));
        varMapper.save(var);
    }

    /**
     * 删除
     */
    public void delete(Integer[] ids) {
        varMapper.delete(ids);
    }

    /**
     * 获取某风场下自定义变量
     */
    public List<ReportVar> getCustomVars(String fieldNum) {
        List<ReportVar> customVars = varMapper.getCustomVars(fieldNum);
        return customVars;
    }

    /**
     * 添加变量修饰符
     */
    private void addVarModifiers(List<ReportVar> customVars) {
        if (ObjectUtils.isEmpty(customVars)) {
            return;
        }

        for (ReportVar customVar : customVars) {
            customVar.setName(addVarModifier(customVar.getName()));
        }
    }

    /**
     * 某风场下自定义变量列表
     */
    public List<ReportVar> customList(String fieldNum) {
        List<ReportVar> customVars = varMapper.getCustomVars(fieldNum);
        if (ObjectUtils.isEmpty(customVars)) {
            return Collections.emptyList();
        }

        Map<Integer, PointCommon> fieldPointMap = getFieldPointMap(fieldNum);
        Map<String, SeaFacilityCommon> fieldSeaMap = getFieldSeaMap(fieldNum);
        for (ReportVar var : customVars) {
            if (ObjectUtils.isEmpty(var.getParamJson())) {
                continue;
            }

            var.setParams(JSON.parseArray(var.getParamJson(), CustomVarParam.class));
            setPointNos(fieldPointMap, var.getParams());
            setSeaNames(fieldSeaMap, var.getParams());
        }
        return customVars;
    }

    /**
     * 设置海上设施名称
     */
    private void setSeaNames(Map<String, SeaFacilityCommon> fieldSeaMap, List<CustomVarParam> params) {
        for (CustomVarParam param : params) {
            if (ObjectUtils.isEmpty(param.getSeaId())) {
                continue;
            }
            SeaFacilityCommon sea = fieldSeaMap.get(param.getSeaId());
            if (sea != null) {
                param.setSeaName(sea.getName());
            }
        }
    }

    /**
     * 设置测点编号
     */
    private void setPointNos(Map<Integer, PointCommon> fieldPointMap, List<CustomVarParam> params) {
        for (CustomVarParam param : params) {
            if (ObjectUtils.isEmpty(param.getPointIds())) {
                continue;
            }
            List<String> pointIdList = new ArrayList<>(param.getPointIds().size());
            for (Integer pointId : param.getPointIds()) {
                PointCommon point = fieldPointMap.get(pointId);
                if (point != null) {
                    pointIdList.add(point.getNo());
                }
            }
            param.setPointNos(pointIdList);
        }
    }

    /**
     * 获取某风场下所有测点Map
     */
    private Map<Integer, PointCommon> getFieldPointMap(String fieldNum) {
        List<PointCommon> fieldPoints = monitorService.getPointsByFieldNum(fieldNum);
        if (!ObjectUtils.isEmpty(fieldNum)) {
            return fieldPoints.stream().collect(Collectors.toMap(PointCommon::getId, Function.identity(), (key1, key2) -> key2));
        }
        return Collections.emptyMap();
    }

    /**
     * 获取某风场下所有测点Map
     */
    private Map<String, SeaFacilityCommon> getFieldSeaMap(String fieldNum) {
        List<SeaFacilityCommon> seas = windService.allSeaFacility(fieldNum);
        if (!ObjectUtils.isEmpty(seas)) {
            return seas.stream().collect(Collectors.toMap(SeaFacilityCommon::getId, Function.identity(), (key1, key2) -> key2));
        }
        return Collections.emptyMap();
    }

    /**
     * 修改自定义变量
     */
    public void update(ReportVar var) {
        var.setParamJson(JSON.toJSONString(var.getParams()));
        varMapper.update(var);
    }

    /**
     * 判断一个变量是否是自定义变量
     */
    public boolean isCustomVar(String var) {
        if (ObjectUtils.isEmpty(var)) {
            return false;
        }

        if ("典型风机".equals(var) || "非典型风机".equals(var) || "所有风机".equals(var) || "升压站".equals(var) || "@{风机编号}".equals(var) || "@{升压站}".equals(var)) {
            return false;
        }
        return true;
    }

}
