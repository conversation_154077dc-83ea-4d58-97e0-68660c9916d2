package com.hdec.data.service;

import com.alibaba.fastjson.JSON;
import com.hdec.common.domain.PointCommon;
import com.hdec.common.util.CompressUtil;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.domain.FreqResult;
import com.hdec.data.domain.FreqTask;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.mapper.FreqResultMapper;
import com.hdec.data.qo.AccQo;
import com.hdec.data.qo.FreqResultQo;
import com.hdec.data.vo.FreqChartVo;
import com.hdec.data.vo.FrequencyVo;
import com.hdec.data.vo.TimeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class FreqResultService {

    @Autowired
    private FreqResultMapper freqResultMapper;

    @Autowired
    private FrequencyService frequencyService;

    @Autowired
    private FrequencyValueFilter valueFilter;

    @Autowired
    private MonitorService monitorService;

    /**
     * 结果列表
     */
    public List<FreqResult> list(FreqResultQo qo) {
        return freqResultMapper.list(qo.getPointId(), qo.getDirectId(), qo.getStartTime(), qo.getEndTime());
    }

    /**
     * 结果列表
     */
    public List<FreqResult> picList(FreqResultQo qo) {
        return freqResultMapper.picList(qo.getPointId(), qo.getDirectId(), qo.getStartTime(), qo.getEndTime());
    }

    /**
     * 根据id返回对象
     */
    public FreqResult getById(Integer id) {
        FreqResult result = freqResultMapper.getById(id);
        if (ObjectUtils.isEmpty(result)) {
            return null;
        }

        FrequencyVo freqVo = frequencyService.analysisFrequency(JSON.parseObject(result.getTaskParam(), FreqTask.class), new AccQo(result.getPointId(), result.getDirectId(), result.getTime(), TimeUtil.addMinutes(result.getTime(), result.getDuration())));
        if (!ObjectUtils.isEmpty(freqVo.getXArr()) && !ObjectUtils.isEmpty(freqVo.getYArr())) {
            result.setData(CompressUtil.compress(JSON.toJSONString(freqVo, valueFilter)));
        }
        return result;
    }

    /**
     * 保存频谱结果
     */
    public int save(FreqResult freqResult) {
        return freqResultMapper.save(freqResult);
    }

    /**
     * 更新传入对象数据
     */
    public int update(FreqResult owpFreqResult) {
        return freqResultMapper.update(owpFreqResult);
    }

    /**
     * 删除
     */
    public void delete(Integer id) {
        if (ObjectUtils.isEmpty(id)) {
            return;
        }
        freqResultMapper.delete(id);
    }

    /**
     * 删除
     */
    public void deleteBatch(Integer[] ids) {
        for (Integer id : ids) {
            freqResultMapper.delete(id);
        }
    }

    /**
     * 按条件删除频谱分析结果
     */
    public void delByCondition(FreqResult freqResult) {
        freqResultMapper.delByCondition(freqResult);
    }

    /**
     * 甘特图
     */
    public List<FreqChartVo> chart(String fieldNum, Integer monitorId, String startTime, String endTime) {
        List<FreqChartVo> vos = new ArrayList<>();

        /* 获取该监测项目下所有测点 */
        List<PointCommon> points = monitorService.getPointsByMonitor(fieldNum, monitorId);
        if (ObjectUtils.isEmpty(points)) {
            return vos;
        }

        Map<Integer, PointCommon> pointMap = points.stream().collect(Collectors.toMap(PointCommon::getId, Function.identity(), (key1, key2) -> key2));
        List<Integer> pointIds = points.stream().map(PointCommon::getId).collect(Collectors.toList());
        List<FreqResult> results = freqResultMapper.selectTimesByPoints(pointIds, TimeUtil.completeStart(startTime), TimeUtil.completeEnd(endTime));
        if (ObjectUtils.isEmpty(results)) {
            return vos;
        }

        Map<Integer, List<FreqResult>> pointTimes = results.stream().collect(Collectors.groupingBy(FreqResult::getPointId));
        pointTimes.forEach((pointId, times) -> {
            String pointNo = pointMap.get(pointId).getNo();
            List<TimeVo> timeVos = new ArrayList<>(times.size());
            for (FreqResult time : times) {
                timeVos.add(new TimeVo(TimeUtil.completeStart(time.getTime()), TimeUtil.completeEnd(time.getTime())));
            }
            vos.add(new FreqChartVo(pointNo, timeVos));
        });
        return vos;
    }

    /**
     * 标记
     */
    public void mark(Integer id) {
        freqResultMapper.mark(id);
    }

    public String getByPoint(Integer pointId, Integer direct, String startTime, String endTime) {
        return freqResultMapper.getByPoint(pointId, direct, startTime, endTime);
    }
}
