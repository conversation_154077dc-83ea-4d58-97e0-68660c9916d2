package com.hdec.data.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.hdec.common.constant.Constant;
import com.hdec.common.constant.RedisKey;
import com.hdec.common.domain.AttrCommon;
import com.hdec.common.domain.PointCommon;
import com.hdec.common.util.CommonUtil;
import com.hdec.common.util.DataServiceUtil;
import com.hdec.common.util.TimeUtil;
import com.hdec.common.vo.Direct;
import com.hdec.common.vo.InstDirectAttr;
import com.hdec.data.domain.RedoParam;
import com.hdec.data.domain.Stat;
import com.hdec.data.domain.Task;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.mapper.DataStatMapper;
import com.hdec.data.mapper.TaskMapper;
import com.hdec.data.qo.CheckQo;
import com.hdec.data.qo.TaskQo;
import com.hdec.data.vo.TaskVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import sun.security.pkcs11.wrapper.Constants;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 任务业务逻辑类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TaskService {

    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private DataStatMapper statMapper;

    @Autowired
    private TdService tdService;

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private TdBaseService tdBaseService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 任务列表
     */
    public List<TaskVo> list(TaskQo qo, List<String> fieldNums, Map<String, String> fieldNamesMap, PageInfo<TaskVo> page) {
        /* 查询风场任务 */
        List<Task> tasks = taskMapper.list(qo, fieldNums);

        /* 按类型设置任务数量 */
        Map<Integer, List<Task>> taskMap = tasks.stream().collect(Collectors.groupingBy(Task::getStatus));
        int progressNum = (taskMap.get(0) == null ? Constant.ZERO : taskMap.get(0).size()) + (taskMap.get(1) == null ? Constant.ZERO : taskMap.get(1).size());
        int completedNum = taskMap.get(2) == null ? Constant.ZERO : taskMap.get(2).size();
        int failedNum = taskMap.get(3) == null ? Constant.ZERO : taskMap.get(3).size();
        TaskVo taskVo = new TaskVo(progressNum, completedNum, failedNum);

        /* 设置风场名称 */
        setFieldName(tasks, fieldNamesMap);

        if (!ObjectUtils.isEmpty(qo.getKw())) {
            tasks = tasks.stream().filter(e -> e.getFieldName() != null && e.getFieldName().contains(qo.getKw())).collect(Collectors.toList());
        }

        /* 过滤并分页 */
        List<Task> resTasks = tasks.stream().filter(e -> {
            if (qo.getStatus() == 1) {
                return e.getStatus() == 2;
            } else if (qo.getStatus() == 2) {
                return e.getStatus() == 3;
            }
            return e.getStatus() == 0 || e.getStatus() == 1;
        }).skip((qo.getPageNum() - 1) * qo.getPageSize()).limit(qo.getPageSize()).collect(Collectors.toList());

        page.setTotal(tasks.size());
        taskVo.setTasks(resTasks);

        return Arrays.asList(taskVo);
    }

    /**
     * 设置风场名称
     */
    private void setFieldName(List<Task> tasks, Map<String, String> fieldNamesMap) {
        for (Task task : tasks) {
            task.setFieldName(fieldNamesMap.get(task.getFieldNum()));
        }
    }

    /**
     * 保存任务
     */
    public void saveTask(Task task) {
        taskMapper.saveTask(task);
    }

    /**
     * 批量保存任务
     */
    public void saveTasks(List<Task> tasks) {
        taskMapper.saveTasks(tasks);
    }

    /**
     * 更新任务
     */
    public void update(Task task) {
        taskMapper.update(task);
    }

    /**
     * 循环执行快速队列的Redo任务
     */
    public void fastLoop() {
        while (true) {
            /* 取最早提交的一条任务 */
            Task task = selectFirstTaskByLevel("fast");
            handleTask(task, 1);
        }
    }

    /**
     * 循环执行慢速队列的Redo任务
     */
    public void slowLoop() {
        while (true) {
            /* 取最早提交的一条任务 */
            Task task = selectFirstTaskByLevel("slow");
            handleTask(task, 3);
        }
    }

    /**
     * 执行任务
     */
    private void handleTask(Task task, int idleSeconds) {
        if (task == null) {
            CommonUtil.sleepSeconds(idleSeconds);
            return;
        }

        /* 执行任务，执行完毕后更新任务状态 */
        try {
            doTask(task);

            finishBySuccess(task);
        } catch (Exception e) {
            e.printStackTrace();
            finishByFail(task, e);
        }
    }

    /**
     * 失败结束
     */
    public void finishByFail(Task task, Exception e) {
        task.setStatus(3);
        task.setFinishTime(new Date());
        task.setErrMsg("任务" + task.getId() + "执行失败，请反馈管理员：" + e);
        taskMapper.update(task);
    }

    /**
     * 成功结束
     */
    public void finishBySuccess(Task task) {
        task.setStatus(2);
        task.setProcess(100);
        task.setFinishTime(new Date());
        taskMapper.update(task);
    }

    /**
     * 查询最先提交的N条任务
     */
    public List<Task> selectTopNTask(int N) {
        List<Task> tasks = taskMapper.selectTopN(N);
        if (ObjectUtils.isEmpty(tasks)) {
            return Collections.emptyList();
        }

        /* 更新标志位 */
        for (Task task : tasks) {
            task.setStartTime(new Date());
            task.setStatus(1);
        }
        taskMapper.updateList(tasks);
        return tasks;
    }

    /**
     * 取最早提交的一条任务
     */
    public synchronized Task selectFirstTaskByLevel(String level) {
        Task task = taskMapper.selectFirstTaskByLevel(level);
        if (task == null) {
            return null;
        }

        /* 更新标志位 */
        task.setStatus(1);
        task.setStartTime(new Date());
        taskMapper.update(task);
        return task;
    }

    /**
     * 执行任务
     */
    private void doTask(Task task) {
        List<RedoParam> params = JSON.parseArray(task.getParams(), RedoParam.class);

        // 将天按月分组
        Map<String, List<RedoParam>> monthMap = params.stream().collect(Collectors.groupingBy(e -> TimeUtil.format2Day(e.getDay()).substring(0, 7)));

        AtomicInteger count = new AtomicInteger();
        monthMap.forEach((month, dayParams) -> {
            Map<Integer, List<RedoParam>> pointDayParamsMap = dayParams.stream().collect(Collectors.groupingBy(RedoParam::getPointId));
            pointDayParamsMap.forEach((point, pointDayParams) -> {
                doTaskByDays(task.getFieldNum(), task.getRate(), pointDayParams, task.getType());
            });
            task.setProcess((int) (count.incrementAndGet() * 100.0 / monthMap.size()));
            taskMapper.update(task);
        });
    }

//    /**
//     * 执行任务中的某项
//     */
//    public void doTaskByParam(String filedNum, String rate, RedoParam param) {
//        InstDirectAttr info = monitorService.getInstDirectAttr(param.getPointId());
//        Integer monitorId = monitorService.getMonitorIdByInst(info.getInstId());
//        List<AttrCommon> attrs = info.getAttrs().stream().filter(e -> rate.equals(e.getRate())).collect(Collectors.toList());
//        if (ObjectUtils.isEmpty(attrs)) {
//            return;
//        }
//
//        String sTableName = DataServiceUtil.buildSTableName(info.getInstId(), rate);
//        String tableName = DataServiceUtil.buildTableName(info.getInstId(), rate, param.getPointId());
//        String day = TimeUtil.format2Day(param.getDay());
//
//        /* 数据统计 */
//        List<Map<String, Object>> records = handleDataStat(filedNum, param.getPointId(), day, monitorId, attrs, info.getDirects(), tableName, rate);
//        if ("小时级".equals(rate)) {
//            long s = System.currentTimeMillis();
//            handleMissStat(records, param.getPointId(), TimeUtil.completeStart(day), TimeUtil.completeEnd(day), info.getDirects(), attrs, sTableName, tableName, rate);
//            long e = System.currentTimeMillis();
//            long cost = (e - s) / 1000;
//            if (cost > 10) {
//                log.info(param.toString() + "缺测(" + records.size() + ")统计：" + cost + "s");
//            }
//        }
//    }

    /**
     * 按天执行任务(天必须同一个月)
     */
    public void doTaskByDays(String filedNum, String rate, List<RedoParam> dayParams, String type) {
        long s = System.currentTimeMillis();
        Integer pointId = dayParams.get(0).getPointId();
        InstDirectAttr info = monitorService.getInstDirectAttr(pointId);
        if (info == null || info.getInstId() == null) {
            return;
        }

        Integer monitorId = monitorService.getMonitorIdByInst(info.getInstId());
        if (monitorId == null) {
            return;
        }

        List<AttrCommon> attrs = info.getAttrs().stream().filter(e -> rate.equals(e.getRate())).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(attrs)) {
            return;
        }

        String sTableName = DataServiceUtil.buildSTableName(info.getInstId(), rate);
        String tableName = DataServiceUtil.buildTableName(info.getInstId(), rate, pointId);

        /* 求时间最大值和最小值 */
        String startTime = TimeUtil.completeStart(TimeUtil.format2Day(dayParams.stream().min(Comparator.comparing(RedoParam::getDay)).get().getDay()));
        String endTime = TimeUtil.completeEnd(TimeUtil.format2Day(dayParams.stream().max(Comparator.comparing(RedoParam::getDay)).get().getDay()));

        for (RedoParam monthParam : dayParams) {
            if (Constant.RATE_HIGH.equals(rate)) {
                handleDataStatWithHighRate(filedNum, pointId, TimeUtil.format2Day(monthParam.getDay()), monitorId, attrs, info.getDirects(), tableName, rate);
            } else {
                handleDataStat(filedNum, pointId, TimeUtil.format2Day(monthParam.getDay()), monitorId, attrs, info.getDirects(), tableName, rate);
            }
        }
        if ("小时级".equals(rate) && !"审核".equals(type)) {
            String cols = tdService.makeSelectAttrCols(attrs, info.getDirects(), rate);
            String sql = "select " + cols + " from " + tableName + " where ts between '" + startTime + "' and '" + endTime + "' and del = false order by ts asc";
            List<Map<String, Object>> records = tdBaseService.selectMulti(sql, rate);

            try {
                handleMissStat(records, pointId, startTime, endTime, info.getDirects(), attrs, sTableName, tableName, rate);
            } catch (Exception e) {
            }
        }

        long e = System.currentTimeMillis();
        String costTs = TimeUtil.second2Human((int) ((e - s) / 1000));
        log.info("rate:{}, day:{}, 统计耗时：{}", rate, TimeUtil.format2Day(dayParams.get(0).getDay()), costTs);
    }

    /**
     * 缺测统计
     */
    private void handleMissStat(List<Map<String, Object>> records, Integer pointId, String startTime, String endTime, List<Direct> instDirects, List<AttrCommon> attrs, String sTableName, String tableName, String rate) {
        /* 针对每一个属性，都需要获取该属性当前天前一条数据和后一条数据 */
        for (AttrCommon attr : attrs) {
            List<Direct> directs = tdBaseService.getFinalDirects(attr.getIsPolar(), instDirects);
            for (Direct direct : directs) {
                String colName = tdService.enColName(attr.getId(), direct.getDirectId());
                String d1Name = tdService.enD1ColName(attr.getId(), direct.getDirectId());
                String d2Name = tdService.enD2ColName(attr.getId(), direct.getDirectId());

                String sql = "select ts, " + colName + ", " + d1Name + ", " + d2Name + " from " + tableName + " where ts < '" + startTime + "' and " + colName + " is not null and del = false order by ts desc limit 1";
                records.add(0, tdBaseService.selectOne(sql, rate));

                sql = "select ts, " + colName + ", " + d1Name + ", " + d2Name + " from " + tableName + " where ts > '" + endTime + "' and " + colName + " is not null and del = false order by ts asc limit 1";
                records.add(tdBaseService.selectOne(sql, rate));
                calcInterval(records, sTableName, tableName, pointId, colName, d1Name, d2Name, rate);
            }
        }
    }

    /**
     * 计算时间间隔
     */
    private void calcInterval(List<Map<String, Object>> records, String sTableName, String tableName, Integer pointId, String colName, String d1Name, String d2Name, String rate) {
        /* 计算时间间隔 */
        for (int i = 1; i < records.size(); i++) {
            /* 当前非空值 */
            Map<String, Object> cur = getRecordByIndex(records, i);
            if (ObjectUtils.isEmpty(cur)) {
                continue;
            }
            if (cur.get(colName) == null) {
                cur.put(d1Name, null);
                continue;
            }

            /* 前一个非空值 */
            boolean flag = false;
            for (int j = i - 1; j >= 0; j--) {
                Map<String, Object> prev = getRecordByIndex(records, j);
                if (prev != null && prev.get(colName) != null) {
                    long interval = ((Date) cur.get("ts")).getTime() - ((Date) prev.get("ts")).getTime();
                    cur.put(d1Name, (int) (interval / 1000));
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                cur.put(d1Name, Integer.MAX_VALUE);
            }
        }

        for (int i = records.size() - 1; i >= 0; i--) {
            /* 当前非空值 */
            Map<String, Object> cur = getRecordByIndex(records, i);
            if (ObjectUtils.isEmpty(cur)) {
                continue;
            }
            if (cur.get(colName) == null) {
                cur.put(d2Name, null);
                continue;
            }

            /* 后一个非空值 */
            boolean flag = false;
            for (int j = i + 1; j < records.size(); j++) {
                Map<String, Object> next = getRecordByIndex(records, j);
                if (next != null && next.get(colName) != null) {
                    long interval = ((Date) next.get("ts")).getTime() - ((Date) cur.get("ts")).getTime();
                    cur.put(d2Name, (int) (interval / 1000));
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                cur.put(d2Name, Integer.MAX_VALUE);
            }
        }

        /* 去除首尾可能的空值 */
        if (records.get(0).get("ts") == null) {
            records.remove(0);
        }
        if (records.get(records.size() - 1).get("ts") == null) {
            records.remove(records.size() - 1);
        }
        if (ObjectUtils.isEmpty(records)) {
            return;
        }

        /* 批量入库 */
        String sql = buildInsertSql(records, sTableName, tableName, pointId, d1Name, d2Name);
        if (sql.getBytes().length < 1024 * 1024) {
            tdBaseService.executeSql(sql, rate);
        } else {
            List<List<Map<String, Object>>> parts = partRecords(records, sTableName, tableName, pointId, d1Name, d2Name);
            for (List<Map<String, Object>> part : parts) {
                sql = buildInsertSql(part, sTableName, tableName, pointId, d1Name, d2Name);
                tdBaseService.executeSql(sql, rate);
            }
        }
    }

    /**
     * 拆分待插入集合数据
     */
    private List<List<Map<String, Object>>> partRecords(List<Map<String, Object>> records, String sTableName, String tableName, Integer pointId, String colD1Name, String colD2Name) {
        int partSize = 2;
        for (int i = 0; i < 100; i++) {
            boolean allIsOk = true;
            List<List<Map<String, Object>>> parts = Lists.partition(records, records.size() / partSize);
            L1:
            for (List<Map<String, Object>> part : parts) {
                String sql = buildInsertSql(part, sTableName, tableName, pointId, colD1Name, colD2Name);
                if (sql.getBytes().length >= 1024 * 1024) {
                    partSize *= 2;
                    allIsOk = false;
                    break L1;
                }
            }
            if (allIsOk) {
                return parts;
            }
        }
        return Lists.partition(records, 1);
    }

    /**
     * 构造插入语句
     */
    private String buildInsertSql(List<Map<String, Object>> records, String sTableName, String tableName, Integer pointId, String d1Name, String d2Name) {
        StringBuilder sb = new StringBuilder("insert into ");
        for (Map<String, Object> record : records) {
            Date ts = (Date) record.get("ts");
            Integer d1 = (Integer) record.get(d1Name);
            Integer d2 = (Integer) record.get(d2Name);

            sb.append(tableName + "(ts," + d1Name + "," + d2Name);
            sb.append(") using " + sTableName + " TAGS (" + pointId + ") VALUES ('" + ts + "'," + d1 + "," + d2 + ") ");
        }
        return sb.toString();
    }

    /**
     * 按下标获取记录
     */
    private Map<String, Object> getRecordByIndex(List<Map<String, Object>> records, int index) {
        if (index >= 0 && index < records.size()) {
            return records.get(index);
        }
        return null;
    }

    /**
     * 数据统计(高频)
     */
    private List<Map<String, Object>> handleDataStatWithHighRate(String fieldNum, Integer pointId, String day, Integer monitorId, List<AttrCommon> attrs, List<Direct> instDirects, String tableName, String rate) {
        /* 确保统计表存在 */
        for (AttrCommon attr : attrs) {
            statMapper.createStatTableIfNotExist(fieldNum, monitorId, attr.getId());
        }

        /* 查询数据 */
//        long s = System.currentTimeMillis();
        List<Map<String, Object>> records = new ArrayList<>();
        String sufSql = " from " + tableName + " where ts between '" + TimeUtil.completeStart(day) + ".000' and '" + TimeUtil.completeEnd(day) + ".999' and del = false";
        List<String> sqls = tdService.makeSelectAttrColsWithHigh(attrs, instDirects, sufSql);
        for (String sql : sqls) {
            List<Map<String, Object>> list = tdBaseService.selectMulti(sql, rate);
            if (!ObjectUtils.isEmpty(list)) {
                records.addAll(list);
            }
        }
        records.forEach(System.out::println);
//        long e = System.currentTimeMillis();
//        log.info("高频sql结果条数：{}，查询耗时：{} s", records.size(), (e - s) / 1000);
        if (ObjectUtils.isEmpty(records)) {
            for (AttrCommon attr : attrs) {
                statMapper.deleteByDayPointAttr(fieldNum, day, monitorId, attr.getId(), pointId);
            }
            return Collections.emptyList();
        }

        /* 统计 */
        List<Stat> statList = new ArrayList<>();
        for (AttrCommon attr : attrs) {
            List<Direct> directs = tdBaseService.getFinalDirects(attr.getIsPolar(), instDirects);
            for (Direct direct : directs) {
                Stat stat = new Stat(day, pointId, direct.getDirectId(), attr.getId());
                String colName = tdService.enColName(attr.getId(), direct.getDirectId());
                Map<String, Object> minRecord = findRecord(records, colName, "1");
                Map<String, Object> maxRecord = findRecord(records, colName, "2");
                Map<String, Object> avgRecord = findRecord(records, colName, "3");
                Map<String, Object> countRecord = findRecord(records, colName, "4");
                if (minRecord != null) {
                    stat.setMinValue((Float) minRecord.get(colName));
                    stat.setMinValueTime((Date) minRecord.get("ts"));
                }
                if (maxRecord != null) {
                    stat.setMaxValue((Float) maxRecord.get(colName));
                    stat.setMaxValueTime((Date) maxRecord.get("ts"));
                }
                if (avgRecord != null) {
                    stat.setAvgValue((Float) avgRecord.get(colName));
                }
                if (countRecord != null) {
                    int total = (int) Double.parseDouble(String.valueOf(countRecord.get(colName)));
                    stat.setApprovalCountInit(total);
                }
                statList.add(stat);
            }
        }

        for (AttrCommon attr : attrs) {
            statMapper.deleteByDayPointAttr(fieldNum, day, monitorId, attr.getId(), pointId);
        }

        /* 保存，数据按分量分组 */
        Map<Integer, List<Stat>> attrMaps = statList.stream().collect(Collectors.groupingBy(Stat::getAttrId));
        attrMaps.forEach((attrId, stats) -> {
            saveStat(fieldNum, monitorId, attrId, stats);
        });
        return Collections.emptyList();
    }

    private Map<String, Object> findRecord(List<Map<String, Object>> records, String colName, String type) {
        for (Map<String, Object> record : records) {
            if (record.keySet().contains(colName) && type.equals(String.valueOf(record.get("type")))) {
                return record;
            }
        }
        return null;
    }


    /**
     * 数据统计
     */
    private List<Map<String, Object>> handleDataStat(String fieldNum, Integer pointId, String day, Integer monitorId, List<AttrCommon> attrs, List<Direct> instDirects, String tableName, String rate) {
        /* 确保统计表存在 */
        for (AttrCommon attr : attrs) {
            statMapper.createStatTableIfNotExist(fieldNum, monitorId, attr.getId());
            statMapper.deleteByDayPointAttr(fieldNum, day, monitorId, attr.getId(), pointId);
        }

        /* 查询数据 */
        String cols = tdService.makeSelectAttrCols(attrs, instDirects, rate);
        String sql = "select " + cols + " from " + tableName + " where ts between '" + TimeUtil.completeStart(day) + "' and '" + TimeUtil.completeEnd(day) + "' and del = false order by ts asc";
//        long s = System.currentTimeMillis();
        List<Map<String, Object>> records = tdBaseService.selectMulti(sql, rate);
//        long e = System.currentTimeMillis();
//        log.info("sql结果条数：{}，查询耗时：{} ms", records.size(), (e - s));

        if (ObjectUtils.isEmpty(records)) {
            for (AttrCommon attr : attrs) {
                statMapper.deleteByDayPointAttr(fieldNum, day, monitorId, attr.getId(), pointId);
            }
            return Collections.emptyList();
        }

        /* 统计 */
        List<Stat> statList = new ArrayList<>();
        for (AttrCommon attr : attrs) {
            List<Direct> directs = tdBaseService.getFinalDirects(attr.getIsPolar(), instDirects);
            for (Direct direct : directs) {
                Stat stat = new Stat(day, pointId, direct.getDirectId(), attr.getId());
                if (statByAttr(stat, records, attr.getId(), direct.getDirectId())) {
                    statList.add(stat);
                }
            }
        }

        for (AttrCommon attr : attrs) {
            statMapper.deleteByDayPointAttr(fieldNum, day, monitorId, attr.getId(), pointId);
        }

        /* 保存，数据按分量分组 */
        Map<Integer, List<Stat>> attrMaps = statList.stream().collect(Collectors.groupingBy(Stat::getAttrId));
        attrMaps.forEach((attrId, stats) -> {
            saveStat(fieldNum, monitorId, attrId, stats);
        });
        return records;
    }

    /**
     * 保存统计数据
     */
    private synchronized void saveStat(String fieldNum, Integer monitorId, Integer attrId, List<Stat> statList) {
        if (!ObjectUtils.isEmpty(statList)) {
            statMapper.saveBatch(fieldNum, monitorId, attrId, statList);
        }
    }

    /**
     * 按属性进行统计相关数据
     */
    private boolean statByAttr(Stat stat, List<Map<String, Object>> records, Integer attrId, Integer directId) {
        float max = Integer.MIN_VALUE;
        float min = Integer.MAX_VALUE;
        Date minTime = null, maxTime = null;
        float total = 0f;
        int count = 0;
        boolean flag = false;

        /* 记录未审核、审核通过、审核不通过数据量 */
        int approvalCountInit = 0;
        int approvalCountPass = 0;
        int approvalCountFail = 0;

        for (Map<String, Object> record : records) {
            Float val = CommonUtil.getFloatFromObj(record.get(tdService.enColName(attrId, directId)));
            if (val == null) {
                continue;
            }
            flag = true;

            Integer status = (Integer) record.get("status_" + directId);
            status = status == null ? 0 : status;
            /* 统计数量 */
            if (status == 1) {
                approvalCountPass++;
            } else if (status == 2) {
                approvalCountFail++;
            } else {
                approvalCountInit++;
            }

            /* 统计最值（审核不通过不参与统计） */
            if (status != 2) {
                if (val > max) {
                    max = val;
                    maxTime = (Date) record.get("ts");
                }
                if (val < min) {
                    min = val;
                    minTime = (Date) record.get("ts");
                }
                total += val;
                count ++;
            }
        }

        /* 赋值 */
        stat.setApprovalCountInit(approvalCountInit);
        stat.setApprovalCountPass(approvalCountPass);
        stat.setApprovalCountFail(approvalCountFail);
        if (count != 0) {
            stat.setMinValue(min);
            stat.setMinValueTime(minTime);
            stat.setMaxValue(max);
            stat.setMaxValueTime(maxTime);
            stat.setAvgValue(total / count);
        }
        return flag;
    }

    /**
     * 校验相关任务是否执行完成
     */
    public boolean checkIsFinished(String fieldNum, CheckQo qo) {
        Date startTime = TimeUtil.parse2Day(qo.getStartTime());
        Date endTime = TimeUtil.parse2Day(qo.getStartTime());

        /* 查询未完成任务的参数并进行汇总 */
        List<String> params = taskMapper.selectNotFinishedParamByField(fieldNum);
        List<RedoParam> allParams = new ArrayList<>();
        for (String param : params) {
            List<RedoParam> redoParams = null;
            try {
                redoParams = JSON.parseArray(param, RedoParam.class);
            } catch (Exception e) { }
            if (!ObjectUtils.isEmpty(redoParams)) {
                allParams.addAll(redoParams);
            }
        }
        if (ObjectUtils.isEmpty(allParams)) {
            return false;
        }

        if (!ObjectUtils.isEmpty(qo.getInstId())) {
            List<PointCommon> points = monitorService.getPointsByInst(qo.getInstId());
            if (!ObjectUtils.isEmpty(points)) {
                qo.setPointIds(points.stream().map(PointCommon::getId).collect(Collectors.toList()));
            }
        }
        if (ObjectUtils.isEmpty(qo.getPointIds())) {
            return false;
        }

        for (Integer pointId : qo.getPointIds()) {
            if (isInNotFinished(pointId, startTime, endTime, allParams)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 某测点是否在相应时间段内存在未完成任务
     */
    private boolean isInNotFinished(Integer pointId, Date startTime, Date endTime, List<RedoParam> allParams) {
        for (RedoParam param : allParams) {
            if (pointId.equals(param.getPointId()) && TimeUtil.isTimeBetween(param.getDay(), startTime, endTime)) {
                return true;
            }
        }
        return false;
    }

}
