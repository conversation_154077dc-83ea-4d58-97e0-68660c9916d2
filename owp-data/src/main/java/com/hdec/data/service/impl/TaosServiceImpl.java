package com.hdec.data.service.impl;

import com.hdec.common.constant.Constant;
import com.hdec.common.domain.AttrCommon;
import com.hdec.common.formula.AttrVal;
import com.hdec.common.formula.AttrVar;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.domain.DailyCount;
import com.hdec.data.domain.RedoJob;
import com.hdec.data.domain.taos.TableColumn;
import com.hdec.data.mapper.TaosMapper;
import com.hdec.data.service.TaosService;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
public class TaosServiceImpl implements TaosService {

    @Value("${tdengine.database.owp:owp}")
    private String normalDatabase;

    @Value("${tdengine.database.high:owp_high}")
    private String highDatabase;

    @Value("${tdengine.database.job:owp_job}")
    private String jobDatabase;

    @Resource
    private TaosMapper taosMapper;


    @Override
    public AttrVal selectAttrVar(AttrVar attrVar, Date ts) {
        String rate = attrVar.getRate();
        String inst = attrVar.getInst();
        String point = attrVar.getPoint();
        String attr = attrVar.getAttr();
        String direct = attrVar.getDirect();
        if (rate == null || inst == null || point == null || attr == null || direct == null) {
            return null;
        }
        String matchType = attrVar.getMatchType();
        Integer timeScope = attrVar.getTimeScope();
        String tableName = getTableName(rate, Integer.valueOf(inst), true);
        String column = enColName(Integer.valueOf(attr), Integer.valueOf(direct));
        AttrVal val = null;
        if (Objects.equals("1", matchType)) {
            /*  当前时间值  */
            val = taosMapper.selectAttrValAtSameTime(tableName, Integer.valueOf(point), column, ts);

            return val;
        } else if (Objects.equals("2", matchType)) {
            /*  时间范围内的值  */
            if (timeScope == null) {
                return null;
            }
            Date start = new Date(ts.getTime() - timeScope / 2);
            Date end = new Date(ts.getTime() + timeScope / 2);
            val = taosMapper.selectAttrValAtScopeTime(tableName, Integer.valueOf(point), column, ts, start, end);
        } else if (Objects.equals("3", matchType)) {
            /*  之前最近测值(当月)  */
            LocalDateTime dateTime = ts.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            LocalDateTime startDateTime = dateTime.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
            Date start = Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant());
            Date end = DateUtils.addMonths(start, 1);
            val = taosMapper.selectAttrValAtScopeTime(tableName, Integer.valueOf(point), column, ts, start, end);
        }
        if (val == null) {
            return null;
        }
        val.setInst(attrVar.getInst());
        val.setPoint(attrVar.getPoint());
        val.setAttr(attrVar.getAttr());
        val.setDirect(attrVar.getDirect());
        val.setRate(attrVar.getRate());
        return val;
    }

    @Override
    public List<AttrVal> selectAttrVal(AttrVar attrVar, Date startTime, Date endTime) {
        String rate = attrVar.getRate();
        String inst = attrVar.getInst();
        String point = attrVar.getPoint();
        String attr = attrVar.getAttr();
        String direct = attrVar.getDirect();
        if (rate == null || inst == null || point == null || attr == null || direct == null) {
            return null;
        }
        String tableName = getTableName(rate, Integer.valueOf(inst), true);
        String column = enColName(Integer.valueOf(attr), Integer.valueOf(direct));
        List<AttrVal> attrVals = taosMapper.selectAttrVal(tableName, Integer.valueOf(point), column, startTime, endTime);
        if (attrVals == null) {
            return null;
        }
        attrVals.forEach(e -> {
            e.setInst(inst);
            e.setPoint(point);
            e.setAttr(attr);
            e.setDirect(direct);
            e.setRate(rate);
        });
        return attrVals;
    }

    @Override
    public void batchSave(List<AttrVal> values) {
        /*  先根据频率分组  */
        Map<String, List<AttrVal>> rateMap = values.stream().filter(e -> e.getRate() != null)
                .collect(Collectors.groupingBy(AttrVal::getRate));
        rateMap.forEach((rate, rateValues) -> {
            AtomicInteger weight = new AtomicInteger(0);
            List<Pair<String, Integer>> sqlWeightList = new ArrayList<>();
            Map<String, List<AttrVal>> instMap = rateValues.stream().collect(Collectors.groupingBy(AttrVal::getInst));
            instMap.forEach((inst, instValues) -> {
                Map<String, List<AttrVal>> pointMap = instValues.stream().collect(Collectors.groupingBy(AttrVal::getPoint));
                pointMap.forEach((point, pointValues) -> {
                    Pair<String, Integer> sw = buildInsertSql(rate, inst, point, pointValues);
                    sqlWeightList.add(sw);
                    /*  判断是否需要执行写入  */
                    int w = weight.addAndGet(sw.getRight());
                    if (w >= 10000) {
                        /*  执行sql  */
                        List<String> ss = sqlWeightList.stream().map(Pair::getLeft).collect(Collectors.toList());
                        sqlWeightList.clear();
                        weight.set(0);
                        String sql = "INSERT INTO " + String.join(" ", ss);
                        taosMapper.insertSql(sql);
                    }
                });
            });
            /*  写入暂存区剩余数据  */
            if (!sqlWeightList.isEmpty()) {
                List<String> ss = sqlWeightList.stream().map(Pair::getLeft).collect(Collectors.toList());
                String sql = "INSERT INTO " + String.join(" ", ss);
                taosMapper.insertSql(sql);
            }
        });
    }

    public Pair<String, Integer> buildInsertSql(String rate, String inst, String point, List<AttrVal> values) {
        boolean isHighRate = Constant.RATE_HIGH.equals(rate);
        AtomicInteger index = new AtomicInteger(0);
        StringBuilder sb = new StringBuilder();
        String tableName = getTableName(rate, Integer.parseInt(inst), true);
        Map<String, List<AttrVal>> colMap = values.stream().collect(Collectors.groupingBy(e -> "a_" + e.getAttr() + "_" + e.getDirect()));
        colMap.forEach((colName, attrValues) -> {
            sb.append(tableName).append("_").append(point).append(" (ts, ").append(colName);
            /*  非高频数据插入扩展字段  */
            if (!isHighRate) {
                sb.append(", ").append(colName.replace("a_", "o_"))
                        .append(", status_0,status_1,status_2,status_3");
            }
            sb.append(", del)")
                    .append(" using ").append(tableName)
                    .append(" TAGS (").append(point).append(") VALUES ");

            attrValues.forEach(e -> {
                sb.append("( '").append(TimeUtil.format2Ms(e.getTs())).append("', ").append(e.getVal());
                /*  非高频数据插入扩展字段  */
                if (!isHighRate) {
                    sb.append(", null, 0, 0, 0, 0");
                }
                sb.append(", false), ");
                index.incrementAndGet();
            });
            sb.deleteCharAt(sb.length() - 2);
        });
        return Pair.of(sb.toString(), index.get());
    }

    @Override
    public void commitRedoJob(Integer type, List<RedoJob> jobs) {
        if (type == null || jobs == null || jobs.isEmpty()) {
            return;
        }
        taosMapper.commitRedoJob(jobDatabase, type, jobs);
    }

    @Override
    public List<RedoJob> selectUndoTask(Integer type, Integer createInterval) {
        if (type == null || createInterval == null) {
            return Collections.emptyList();
        }
        return taosMapper.selectUndoTask(jobDatabase, type, createInterval);
    }

    @Override
    public void completeRedoJob(Integer type, List<RedoJob> jobs) {
        if (type == null || jobs == null || jobs.isEmpty()) {
            return;
        }
        taosMapper.completeRedoJob(jobDatabase, type, jobs);
    }

    @Override
    public Long selectDailyCount(Integer inst, Integer point, Date startTime, Date endTime) {
        if (inst == null || point == null || startTime == null || endTime == null) {
            return 0L;
        }
        return taosMapper.selectDailyCount(highDatabase, inst, point, startTime, endTime);
    }

    @Override
    public void insectDailyCount(List<DailyCount> dailyCounts) {
        if (dailyCounts == null || dailyCounts.isEmpty()) {
            return;
        }
        taosMapper.insectDailyCount(highDatabase, dailyCounts);
    }

    @Override
    public Long selectDataCount(List<Integer> points, Date startTime, Date endTime) {
        if (points == null || points.isEmpty() || startTime == null || endTime == null) {
            return 0L;
        }
        return taosMapper.selectDataCount(highDatabase, points, startTime, endTime);
    }

    @Override
    public void initializeHighSTable(Integer instId, List<AttrCommon> attrs, Integer[] instDirects) {
        if (instId == null || attrs == null || attrs.isEmpty()) {
            return;
        }
        String tableName = getTableName(Constant.RATE_HIGH, instId, false);
        /*  组装列  */
        List<TableColumn> columns = new ArrayList<>();
        columns.add(new TableColumn("ts", "timestamp"));
        columns.add(new TableColumn("del", "bool"));
        for (AttrCommon attr : attrs) {
            Integer[] directs = attr.getIsPolar() ? new Integer[]{0} : instDirects;
            for (Integer direct : directs) {
                String colName = enColName(attr.getId(), direct);
                columns.add(new TableColumn(colName, "float"));
            }
        }
        /*  组装 tags  */
        List<TableColumn> tags = getPointTags();
        /*  初始化表  */
        initializeSTable(highDatabase, tableName, columns, tags);
    }

    @Override
    public void initializeHighSampleSTable(Integer instId, List<AttrCommon> attrs, Integer[] instDirects) {
        if (instId == null || attrs == null || attrs.isEmpty()) {
            return;
        }
        String tableName = getTableName(Constant.RATE_SAMPLE, instId, false);
        /*  组装列  */
        List<TableColumn> columns = new ArrayList<>();
        columns.add(new TableColumn("ts", "timestamp"));
        for (AttrCommon attr : attrs) {
            Integer[] directs = attr.getIsPolar() ? new Integer[]{0} : instDirects;
            for (Integer direct : directs) {
                String colName = enColName(attr.getId(), direct);
                columns.add(new TableColumn(colName, "float"));
            }
        }
        /*  组装 tags  */
        List<TableColumn> tags = getPointTags();
        /*  初始化表  */
        initializeSTable(highDatabase, tableName, columns, tags);
    }

    @Override
    public void initializeNormalSTable(Integer instId, List<AttrCommon> attrs, Integer[] instDirects) {
        if (instId == null || attrs == null || attrs.isEmpty()) {
            return;
        }
        /*  根据频率分组  */
        Map<String, List<AttrCommon>> rateGroup = attrs.stream().filter(e -> e.getRate() != null)
                .collect(Collectors.groupingBy(AttrCommon::getRate));
        /*  初始化表  */
        rateGroup.forEach((rate, rateAttrs) -> {
            String tableName = getTableName(rate, instId, false);
            /*  组装列  */
            List<TableColumn> columns = new ArrayList<>();
            columns.add(new TableColumn("ts", "timestamp"));
            columns.add(new TableColumn("status_0", "int"));
            columns.add(new TableColumn("status_1", "int"));
            columns.add(new TableColumn("status_2", "int"));
            columns.add(new TableColumn("status_3", "int"));
            columns.add(new TableColumn("del", "bool"));
            for (AttrCommon attr : rateAttrs) {
                Integer[] directs = attr.getIsPolar() ? new Integer[]{0} : instDirects;
                for (Integer direct : directs) {
                    String colName = enColName(attr.getId(), direct);
                    columns.add(new TableColumn(colName, "float"));
                    columns.add(new TableColumn(enLackColName(attr.getId(), direct), "float"));
                    columns.add(new TableColumn(enLastTimeColName(attr.getId(), direct), "int"));
                    columns.add(new TableColumn(enNextTimeColName(attr.getId(), direct), "int"));
                    columns.add(new TableColumn(enLastHisColName(attr.getId(), direct), "float"));
                }
            }
            /*  组装 tags  */
            List<TableColumn> tags = getPointTags();
            /*  初始化表  */
            initializeSTable(normalDatabase, tableName, columns, tags);
        });
    }

    @Override
    public void initializeSTable(String database, String tableName, List<TableColumn> columns, List<TableColumn> tags) {
        /*  查询表是否存在  */
        List<String> tables = taosMapper.showStables(database, tableName);
        if (tables == null || tables.isEmpty()) {
            /*  创建表  */
            taosMapper.createStable(database, tableName, columns, tags);
        } else {
            /*  修改表  */
            List<TableColumn> existsColumns = taosMapper.tablesDescribe(database, tableName)
                    .stream().filter(e -> e.getField() != null && !Objects.equals(e.getNote(), "TAG"))
                    .collect(Collectors.toList());
            /*  添加列  */
            List<String> ecf = existsColumns.stream().map(TableColumn::getField).collect(Collectors.toList());
            columns.stream().filter(e -> !ecf.contains(e.getField()))
                    .forEach(e -> {
                        taosMapper.addColumn(database, tableName, e.getField(), e.getType());
                    });
            /*  删除列  */
            List<String> ncf = columns.stream().map(TableColumn::getField).collect(Collectors.toList());
            existsColumns.stream().filter(e -> !ncf.contains(e.getField()))
                    .forEach(e -> {
                        taosMapper.deleteColumn(database, tableName, e.getField());
                    });
        }
    }

    @Override
    public void createRedoJobStable() {
        taosMapper.createRedoJobStable(jobDatabase);
    }

    @Override
    public void createDailyCountJobStable() {
        taosMapper.createDailyCountJobStable(highDatabase);
    }

    @Override
    public void createOwpDatabase() {
        taosMapper.createDatabase(normalDatabase);
    }

    @Override
    public void createOwpHighDatabase() {
        taosMapper.createDatabase(highDatabase);
    }

    @Override
    public void createOwpJobDatabase() {
        taosMapper.createDatabase(jobDatabase);
    }

    @Override
    public List<TableColumn> getPointTags() {
        return Collections.singletonList(new TableColumn("point", "int"));
    }

    @Override
    public String getTableName(String rate, Integer inst, boolean withDbName) {
        String database;
        String tableName;
        switch (rate) {
            case Constant.RATE_HIGH: {
                database = highDatabase;
                tableName = "high_" + inst;
                break;
            }
            case Constant.RATE_SAMPLE: {
                database = highDatabase;
                tableName = "sample_" + inst;
                break;
            }
            case Constant.RATE_MIN: {
                database = normalDatabase;
                tableName = "min_" + inst;
                break;
            }
            case Constant.RATE_HOUR: {
                database = normalDatabase;
                tableName = "hour_" + inst;
                break;
            }
            default:
                return "";
        }
        return withDbName ? database + "." + tableName : tableName;
    }

    @Override
    public String getTableName(String rate, Integer inst, Integer point, boolean withDbName) {
        return getTableName(rate, inst, withDbName) + "_" + point;
    }

    @Override
    public String getRateDatabase(String rate) {
        switch (rate) {
            case Constant.RATE_HIGH:
            case Constant.RATE_SAMPLE:
                return highDatabase;
            case Constant.RATE_MIN:
            case Constant.RATE_HOUR:
                return normalDatabase;
            default:
                return "";
        }
    }

    public String enColName(Integer attr, Integer direct) {
        return "a_" + attr + "_" + direct;
    }

    @Override
    public String enLackColName(Integer attr, Integer direct) {
        return "d_" + attr + "_" + direct;
    }

    @Override
    public String enLastTimeColName(Integer attr, Integer direct) {
        return "d1_" + attr + "_" + direct;
    }

    @Override
    public String enNextTimeColName(Integer attr, Integer direct) {
        return "d2_" + attr + "_" + direct;
    }

    @Override
    public String enLastHisColName(Integer attr, Integer direct) {
        return "o_" + attr + "_" + direct;
    }
}
