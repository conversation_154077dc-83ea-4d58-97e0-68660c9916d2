package com.hdec.data.service.impl;

import com.hdec.common.constant.Constant;
import com.hdec.common.formula.AttrVal;
import com.hdec.data.domain.RedoJob;
import com.hdec.data.enums.RedoJobType;
import com.hdec.data.service.RedoJobService;
import com.hdec.data.service.TaosService;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class RedoJobServiceImpl implements RedoJobService {

    private static final Logger logger = LoggerFactory.getLogger(RedoJobServiceImpl.class);

    private static final int MINUTE_TIME_MILLIS = 1000 * 60;

    private static final int HOUR_TIME_MILLIS = MINUTE_TIME_MILLIS * 60;
    private static final int DAY_TIME_MILLIS = HOUR_TIME_MILLIS * 24;

    @Resource
    private TaosService taosService;

    @Override
    public void submitRedoJob(List<AttrVal> values) {
        if (values == null || values.isEmpty()) {
            return;
        }
        List<Pair<Date, String>> timePoints = values.stream().map(e -> Pair.of(e.getTs(), e.getPoint()))
                .distinct().collect(Collectors.toList());
        Map<String, List<AttrVal>> rateMap = values.stream().collect(Collectors.groupingBy(AttrVal::getRate));
        rateMap.forEach((rate, rateValues) -> {
            switch (rate) {
                case Constant.RATE_HIGH: {
                    /*  提交日统计任务  */
                    List<Pair<Long, String>> dailyValues = timePoints.stream().map(e ->
                                    Pair.of(e.getLeft().getTime() / DAY_TIME_MILLIS * DAY_TIME_MILLIS, e.getRight()))
                            .distinct().collect(Collectors.toList());
                    List<RedoJob> dailyJobs = dailyValues.stream()
                            .map(e -> new RedoJob(Integer.parseInt(e.getRight()), new Date(e.getLeft()), 0))
                            .collect(Collectors.toList());
                    commitJob(RedoJobType.DAILY_COUNT, dailyJobs);
                    /*  提交分钟计算任务  */
                    List<Pair<Long, String>> minuteValues = timePoints.stream().map(e ->
                                    Pair.of(e.getLeft().getTime() / MINUTE_TIME_MILLIS * MINUTE_TIME_MILLIS, e.getRight()))
                            .distinct().collect(Collectors.toList());
                    List<RedoJob> minuteJobs = minuteValues.stream()
                            .map(e -> new RedoJob(Integer.parseInt(e.getRight()), new Date(e.getLeft()), 0))
                            .collect(Collectors.toList());
                    commitJob(RedoJobType.MINUTE, minuteJobs);
                }
                case Constant.RATE_MIN: {
                    /*  提交小时计算任务  */
                    List<Pair<Long, String>> hourValues = timePoints.stream().map(e ->
                                    Pair.of(e.getLeft().getTime() / HOUR_TIME_MILLIS * HOUR_TIME_MILLIS, e.getRight()))
                            .distinct().collect(Collectors.toList());
                    List<RedoJob> hourJobs = hourValues.stream()
                            .map(e -> new RedoJob(Integer.parseInt(e.getRight()), new Date(e.getLeft()), 0))
                            .collect(Collectors.toList());
                    commitJob(RedoJobType.HOUR, hourJobs);
                }
                break;
            }
        });
    }

    @Override
    public void commitJob(RedoJobType type, List<RedoJob> jobs) {
        if (jobs == null || jobs.isEmpty()) {
            return;
        }
        taosService.commitRedoJob(type.getCode(), jobs);
    }

    @Override
    public List<RedoJob> selectUndoTask(RedoJobType type, Integer createInterval) {
        return taosService.selectUndoTask(type.getCode(), createInterval);
    }

    @Override
    public void completeJob(RedoJobType type, List<RedoJob> jobs) {
        if (jobs == null || jobs.isEmpty()) {
            return;
        }
        taosService.completeRedoJob(type.getCode(), jobs);
    }
}
