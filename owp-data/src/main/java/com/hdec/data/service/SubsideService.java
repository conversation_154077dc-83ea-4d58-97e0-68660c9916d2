package com.hdec.data.service;

import com.alibaba.fastjson.JSON;
import com.hdec.common.domain.PointCommon;
import com.hdec.common.domain.SeaFacilityCommon;
import com.hdec.common.domain.SettingsCommon;
import com.hdec.common.util.CommonUtil;
import com.hdec.common.util.DataServiceUtil;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.domain.AttrIdVal;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.feign.WindService;
import com.hdec.data.qo.SubsideConf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据查询
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Slf4j
@Service
public class SubsideService {

    private static final String SUBSIDE_CONF_CODE = "SUBSIDE_CONF_CODE";

    @Value("${deploy.fieldNum}")
    private String fieldNum;

    @Resource
    private TdBaseService tdBaseService;

    @Resource
    private WindService windService;
    @Resource
    private MonitorService monitorService;


    @Scheduled(cron = "${subside.calculate.cron:-}")
    public void subsideCalcSchedule() {
        long time = new Date().getTime() / 1000 / 60 * 1000 * 60;
        Date endTime = new Date(time);
        Date startTime = TimeUtil.addMinutes(endTime, -15);
        calcSubside(startTime, endTime);
    }

    /**
     * 计算沉降
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    public void calcSubside(Date startTime, Date endTime) {
        List<SubsideConf> conf = getSubsideConf(fieldNum);
        for (SubsideConf subsideConf : conf) {
            Integer inst = subsideConf.getInstId();
            Integer calcAttr = subsideConf.getCalcAttrId();
            Integer h2Attr = subsideConf.getH2AttrId();
            Integer h3Attr = subsideConf.getH3AttrId();
            Integer h4Attr = subsideConf.getH4AttrId();
            calcSubside(inst, calcAttr, h2Attr, h3Attr, h4Attr, startTime, endTime);
        }
    }

    /**
     * 设置沉降配置信息
     *
     * @param fieldNum 项目编码
     * @param conf     配置
     */
    public void settingSubsideConf(String fieldNum, List<SubsideConf> conf) {
        String value = JSON.toJSONString(conf);
        SettingsCommon settings = new SettingsCommon();
        settings.setFieldNum(fieldNum);
        settings.setCode(SUBSIDE_CONF_CODE);
        settings.setValue(value);
        settings.setDescription("沉降计算中参与计算的仪器和分量信息");
        windService.saveSettings(settings);
    }

    /**
     * 获取沉降配置信息
     *
     * @param fieldNum 项目编码
     * @return {@link List }<{@link SubsideConf }>
     */
    public List<SubsideConf> getSubsideConf(String fieldNum) {
        SettingsCommon settings = windService.getSettings(fieldNum, SUBSIDE_CONF_CODE);
        if (settings == null || settings.getValue() == null) {
            log.error("沉降计算-未获取到配置");
            return null;
        }
        return JSON.parseArray(settings.getValue(), SubsideConf.class);
    }


    /**
     * 计算沉降
     *
     * @param inst      仪器
     * @param calcAttr  计算分量
     * @param h2Attr    H2分量
     * @param h3Attr    H3分量
     * @param h4Attr    H4分量
     * @param startTime 开始时间
     * @param endTime   结束
     */
    public void calcSubside(Integer inst, Integer calcAttr, Integer h2Attr, Integer h3Attr, Integer h4Attr, Date startTime, Date endTime) {
        if (inst == null || calcAttr == null || h2Attr == null || h3Attr == null || h4Attr == null) {
            log.error("计算沉降的仪器及分量信息异常-仪器[{}]计算分量[{}]H2分量[{}]H3分量[{}]H4分量[{}]", inst, calcAttr, h2Attr, h3Attr, h4Attr);
        }

        /*  找出所有测点  */
        List<PointCommon> points = monitorService.getPointsByInst(inst);
        /*  找出基桩  */
        Map<String, List<PointCommon>> pointMap = points.stream().collect(Collectors.groupingBy(PointCommon::getSeaFacilityId));
        List<PointCommon> basePoints = new ArrayList<>();
        pointMap.forEach((k, v) -> v.stream().map(p -> CommonUtil.extractDecimal(p.getInstallElevation()))
                .filter(Objects::nonNull)
                .min(Comparator.comparing(e -> e))
                .ifPresent(high -> v.stream().filter(p -> Objects.equals(high, CommonUtil.extractDecimal(p.getInstallElevation())))
                        .forEach(basePoints::add)));

        Map<String, SeaFacilityCommon> facilityMap = windService.allSeaFacility(fieldNum).stream().collect(Collectors.toMap(SeaFacilityCommon::getId, e -> e));
        /*  计算基桩沉降  */
        for (PointCommon point : basePoints) {
            Integer pointId = point.getId();
            /* 获取基桩半径 */
            String facilityId = point.getSeaFacilityId();
            SeaFacilityCommon facility = facilityMap.get(facilityId);
            if (facility == null || facility.getPileDiameter() == null) {
                log.error("定时计算沉降-未找到风机或风机未配置桩径-{}-{}", facilityId, facility);
                continue;
            }
            Pair<Double, Double> val = selectSubsideRecord(inst, pointId, calcAttr, startTime, endTime);
            Double x = val.getLeft();
            Double y = val.getRight();
            if (x == null || y == null) {
                log.error("定时计算沉降-查询到 x 或 y 值:{}", val);
                continue;
            }
            /*  获取基桩半径  */
            /*
            H1=0
            H2=tanθy * r - tanθx * r
            H3=-2tanθx * r
            H4=-tanθy * r - tanθx * r
            */
            Double radius = facility.getPileDiameter();
            if (radius == null) {
                log.error("定时计算沉降-未找到半径");
                continue;
            }
            radius = radius / 2;
            /*  角度转弧度  */
            x = x * Math.PI / 180;
            y = y * Math.PI / 180;
            /* H2 */
            double h2 = Math.tan(y) * radius - Math.tan(x) * radius;
            /* H3 */
            double h3 = -2 * Math.tan(x) * radius;
            /* H4 */
            double h4 = -Math.tan(y) * radius - Math.tan(x) * radius;

            /*  沉降数据写入  */
            List<AttrIdVal> values = new ArrayList<>();
            values.add(new AttrIdVal(h2Attr, (float) h2));
            values.add(new AttrIdVal(h3Attr, (float) h3));
            values.add(new AttrIdVal(h4Attr, (float) h4));
            insertSubsideRecord(inst, pointId, endTime, values);
        }
    }

    private Pair<Double, Double> selectSubsideRecord(Integer inst, Integer point, Integer attr, Date startTime, Date endTime) {
        /*  查询td数据  */
        String sql = "select AVG(a_#{attr_id}_1) x, AVG(a_#{attr_id}_2) y from high_#{inst_id}_#{point_id} where ts >= '#{start}' and ts <= '#{end}'"
                .replace("#{inst_id}", inst + "")
                .replace("#{point_id}", point + "")
                .replace("#{attr_id}", attr + "")
                .replace("#{start}", TimeUtil.format2Second(startTime)).replace("#{end}", TimeUtil.format2Second(endTime));
        List<Map<String, Object>> record = tdBaseService.selectMulti(sql, "高频");
        if (record == null || record.isEmpty()) {
            log.error("定时计算沉降-查询td数据为空");
            return Pair.of(null, null);
        }
        Map<String, Object> resultMap = record.get(0);
        Object o = resultMap.get("x");
        Double x = o == null ? null : (Double) o;
        o = resultMap.get("y");
        Double y = o == null ? null : (Double) o;
        return Pair.of(x, y);
    }

    /**
     * 插入数据
     *
     * @param inst       仪器
     * @param point      测点
     * @param ts         时间戳
     * @param attrValues 分量数据
     */
    public void insertSubsideRecord(Integer inst, Integer point, Date ts, List<AttrIdVal> attrValues) {
        String rate = "分钟";
        List<String> attrs = attrValues.stream().map(attr -> "a_" + attr.getAttrId() + "_0").collect(Collectors.toList());
        List<String> values = attrValues.stream().map(attr -> attr.getAttrVal() + "").collect(Collectors.toList());
        /* 表名 */
        String sTableName = DataServiceUtil.buildSTableName(inst, rate);
        String tableName = DataServiceUtil.buildTableName(inst, rate, point);
        String sql = "insert into #{table_name} (ts, del, #{attrs}) using #{s_table_name} tags (#{point_id}) values ('#{ts}', false, #{values})"
                .replace("#{table_name}", tableName)
                .replace("#{s_table_name}", sTableName)
                .replace("#{point_id}", point + "")
                .replace("#{ts}", TimeUtil.format2Second(ts))
                .replace("#{attrs}", String.join(", ", attrs))
                .replace("#{values}", String.join(", ", values));
        log.info("sql:\n{}", sql);
        tdBaseService.executeSql(sql, "分钟级");
    }
}

