package com.hdec.data.service;

import com.hdec.common.constant.Constant;
import com.hdec.common.domain.AttrCommon;
import com.hdec.common.formula.*;
import com.hdec.data.cache.LocalCacheManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class FormulaReferenceParser implements ReferenceParser {

    @Resource
    private LocalCacheManager localCacheManager;

    @Resource
    private TaosService taosService;

    @Override
    public Number parseParamRefer(AviatorFormula formula, AviatorEnv env, String paramRefer) {
        if (paramRefer == null) {
            return null;
        }
        /*  测点参数  */
        String[] split = paramRefer.split("_");
        if (split.length < 3) {
            throw new RuntimeException("表达式引用错误[" + paramRefer + "]");
        }
        String d = split.length > 3 ? split[3] : formula.getDirect();
        return localCacheManager.getPointParamCache(split[1], split[2], d);
    }

    @Override
    public AttrVar parseAttrRefer(AviatorFormula formula, AviatorEnv env, String attrRefer) {
        String[] split = attrRefer.split("_");
        String point = split[1];
        String attr = split[2];
        String direct = split.length > 3 ? split[3] : formula.getDirect();
        String matchType = split.length > 4 ? split[4] : null;
        Integer timeScope = split.length > 5 ? Integer.valueOf(split[5]) : null;
        AttrCommon attrInfo = localCacheManager.getAttrCache(Integer.valueOf(attr));
        if (attrInfo == null || attrInfo.getInstId() == null || attrInfo.getRate() == null) {
            return null;
        }
        AttrVar attrVar = new AttrVar(attrInfo.getInstId().toString(), point, attr, direct, attrInfo.getRate(),
                matchType, timeScope, attrRefer);
        /*  高频数据计算时查询  */
        if (Objects.equals(attrInfo.getRate(), Constant.RATE_HIGH)) {
            return attrVar;
        }
        /*  非高频数据预查询时间段内数据  */
        Date startTime = env.getStartTime();
        Date endTime = env.getEndTime();
        if (startTime == null || endTime == null) {
            return attrVar;
        }
        List<AttrVal> attrVals = taosService.selectAttrVal(attrVar, startTime, endTime);
        attrVar.setBeforehandValues(attrVals);
        return attrVar;
    }
}
