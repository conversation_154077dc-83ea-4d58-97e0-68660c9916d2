package com.hdec.data.service;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.policy.NumberingRenderPolicy;
import com.deepoove.poi.template.ElementTemplate;
import lombok.extern.slf4j.Slf4j;
/**
 * <AUTHOR>
 * @date 2023/8/21
 */
@Slf4j
public class NumberingRender extends NumberingRenderPolicy {

    @Override
    public void render(ElementTemplate eleTemplate, Object data, XWPFTemplate template) {
        log.info("eleTemplate:{}, data:{},template:{}", eleTemplate, data, template);
        super.render(eleTemplate, data, template);
    }
}
