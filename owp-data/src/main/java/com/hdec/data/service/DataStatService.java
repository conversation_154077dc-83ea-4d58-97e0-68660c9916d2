package com.hdec.data.service;

import com.hdec.common.domain.AttrCommon;
import com.hdec.common.domain.AttrInfo;
import com.hdec.common.domain.Direct;
import com.hdec.common.domain.PointCommon;
import com.hdec.common.util.DataServiceUtil;
import com.hdec.common.util.TimeUtil;
import com.hdec.common.util.Utils;
import com.hdec.common.vo.InstDirectAttr;
import com.hdec.common.vo.PointDirectVo;
import com.hdec.data.domain.Stat;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.mapper.DataStatMapper;
import com.hdec.data.qo.ExistDataQo;
import com.hdec.data.qo.StatQo;
import com.hdec.data.util.ExcelUtil;
import com.hdec.data.vo.ExistStatVo;
import com.hdec.data.vo.StatVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 测值统计业务类
 *
 * <AUTHOR>
 */
@Service
public class DataStatService {

    @Autowired
    private DataStatMapper statMapper;

    @Autowired
    private MonitorService monitorService;

    /**
     * 统计表不存在就创建
     */
    public void createStatTableIfNotExist(String fieldNum, Integer pointId, Integer attrId) {
        InstDirectAttr info = monitorService.getInstDirectAttr(pointId);
        Integer monitorId = monitorService.getMonitorIdByInst(info.getInstId());
        statMapper.createStatTableIfNotExist(fieldNum, monitorId, attrId);
    }

    /**
     * 按条件获取统计数据
     */
    public Object getStatData(String fieldNum, StatQo qo) {
        switch (qo.getType()) {
            /* 特征值统计 */
            case "feature-custom":
                return featureStat(fieldNum, qo, null, "时段");
            case "feature-month":
                return featureStat(fieldNum, qo, qo.getMonthStartDay(), "月");
            case "feature-quarter":
                return featureStat(fieldNum, qo, qo.getQuarterStartMonth(), "季");
            case "feature-year":
                return featureStat(fieldNum, qo, null, "年");

            /* 日常统计 */
            case "daily-custom":
                return dailyStat(fieldNum, qo, null, "按时段");
            case "daily-month":
                return dailyStat(fieldNum, qo, qo.getMonthStartDay(), "按月");
            case "daily-quarter":
                return dailyStat(fieldNum, qo, qo.getQuarterStartMonth(), "按季");
            case "daily-year":
                return dailyStat(fieldNum, qo, null, "按年");
        }
        return null;
    }

    /**
     * 特征值统计
     */
    public StatVo featureStat(String fieldNum, StatQo qo, Integer start, String title) {
        /* 获取该分量信息（包括所属仪器、测点信息） */
        AttrInfo attrInfo = monitorService.getAttrInfo(qo.getAttrId(), "withPoint");
        Integer monitorId = monitorService.getMonitorIdByInst(attrInfo.getInstId());

        /* 获取该分量下实际测点和用户所勾选测点的交集 */
        List<PointCommon> points = getIntersectPoints(attrInfo, qo.getPointIds());
        if (ObjectUtils.isEmpty(points)) {
            // 说明勾选的测点和该分量没有交集
            return new StatVo();
        }
        List<Integer> pointIds = points.stream().map(PointCommon::getId).collect(Collectors.toList());
        Map<Integer, String> pointIdNoMap = points.stream().collect(Collectors.toMap(PointCommon::getId, PointCommon::getNo, (key1, key2) -> key2));

        /* 获取该分量所属仪器方向 */
        List<Direct> directs = Utils.parseDirect(attrInfo.getInstDirect(), attrInfo.getIsPolar());

        /* 唯一标识 */
        AtomicInteger index = new AtomicInteger();

        /* 查询统计数据集并归类 */
        List<Stat> stats = statMapper.select(new StatQo(qo.getStartTime(), qo.getEndTime(), pointIds, qo.getAttrId(), monitorId, fieldNum));
        if (!ObjectUtils.isEmpty(stats)) {
            for (Stat stat : stats) {
                String classify;
                if (qo.getType().endsWith("custom")) {
                    classify = classifyDay(qo.getStartTime(), qo.getEndTime(), qo.getType(), start).get(0);
                } else {
                    classify = classifyDay(stat.getDay(), stat.getDay(), qo.getType(), start).get(0);
                }
                stat.setDay(classify);
            }
        } else {
            stats = new ArrayList<>(0);
        }

        List<Stat> statRes = new ArrayList<>();
        Map<String, List<Stat>> pointDirectDayGroup = stats.stream().collect(Collectors.groupingBy(e -> e.getPointId() + "#" + e.getDirect() + "#" + e.getDay()));
        List<String> allItems = classifyDay(qo.getStartTime(), qo.getEndTime(), qo.getType(), start);
        StatVo vo = new StatVo(qo.getAttrName() + title + "特征值统计(" + qo.getStartTime() + " ~ " + qo.getEndTime() + ")", qo.getIsPolar(), statRes);
        sortByNo(points);
        for (PointCommon point : points) {
            Integer pointId = point.getId();
            for (Direct direct : directs) {
                for (String item : allItems) {
                    List<Stat> statList = pointDirectDayGroup.get(pointId + "#" + direct.getId() + "#" + item);
                    if (!ObjectUtils.isEmpty(statList)) {
                        Stat s = togetherStat(statList);
                        s.setAttrId(qo.getAttrId());
                        s.setAttrName(qo.getAttrName());
                        s.setId(index.incrementAndGet());
                        s.setPointId(pointId);
                        s.setPointNo(pointIdNoMap.get(pointId));
                        s.setDirect(direct.getId());
                        s.setItem(item);
                        statRes.add(s);
                    } else {
                        Stat s = new Stat();
                        s.setAttrId(qo.getAttrId());
                        s.setAttrName(qo.getAttrName());
                        s.setId(index.incrementAndGet());
                        s.setPointId(pointId);
                        s.setPointNo(pointIdNoMap.get(pointId));
                        s.setDirect(direct.getId());
                        s.setItem(item);
                        statRes.add(s);
                    }
                }
            }
        }
        return vo;
    }

    /**
     * 获取分量下实际测点和用户所勾选测点的交集
     */
    private List<PointCommon> getIntersectPoints(AttrInfo attrInfo, List<Integer> pointIds) {
        if (ObjectUtils.isEmpty(attrInfo) || ObjectUtils.isEmpty(attrInfo.getPoints())) {
            return Collections.emptyList();
        }

        List<PointCommon> res = new ArrayList<>();
        for (PointCommon point : attrInfo.getPoints()) {
            if (pointIds.contains(point.getId())) {
                res.add(point);
            }
        }
        return res;
    }

    /**
     * 日常统计
     */
    public List<StatVo> dailyStat(String fieldNum, StatQo qo, Integer start, String title) {
        AttrCommon attr = monitorService.getProAttrById(qo.getAttrId());
        String rate = attr == null ? null : attr.getRate();
        if (ObjectUtils.isEmpty(rate)) {
            return Collections.emptyList();
        }
        Integer monitorId = monitorService.getMonitorIdByInst(attr.getInstId());

        /* 查询该分量下实际测点 */
        List<PointDirectVo> infos = monitorService.getPointIdsByAttr(qo.getAttrId());
        removeNotSelected(infos, qo.getPointIds());
        if (ObjectUtils.isEmpty(infos)) {
            return Collections.emptyList();
        }
        AtomicInteger index = new AtomicInteger();
        List<StatVo> vos = new ArrayList<>();

        // 建立测点映射
        Map<Integer, String> pointMap = getPointIdAndNoMapping(qo.getPointIds(), qo.getPointNos());

        /* 查询目标数据集并归类 */
        List<Integer> pointIds = infos.stream().map(PointDirectVo::getPointId).collect(Collectors.toList());
        List<Stat> stats = statMapper.getStatDataByTimePointAttrRate(fieldNum, qo.getStartTime(), qo.getEndTime(), pointIds, qo.getAttrId(), monitorId, rate);
        if (!ObjectUtils.isEmpty(stats)) {
            for (Stat stat : stats) {
                String classify;
                if (qo.getType().endsWith("custom")) {
                    classify = classifyDay(qo.getStartTime(), qo.getEndTime(), qo.getType(), start).get(0);
                } else {
                    classify = classifyDay(stat.getDay(), stat.getDay(), qo.getType(), start).get(0);
                }
                stat.setDay(classify);
            }
        } else {
            stats = new ArrayList<>(0);
        }

        List<Stat> statRes = new ArrayList<>();
        Map<String, List<Stat>> pointDirectDayGroup = stats.stream().collect(Collectors.groupingBy(e -> e.getPointId() + "#" + e.getDirect() + "#" + e.getDay()));
        List<String> allItems = classifyDay(qo.getStartTime(), qo.getEndTime(), qo.getType(), start);
        for (PointDirectVo info : infos) {
            Integer pointId = info.getPointId();
            String pointNo = pointMap.get(pointId);
            StatVo vo = new StatVo(pointNo + " -- " + qo.getAttrName() + title + "统计(" + qo.getStartTime() + " ~ " + qo.getEndTime() + ")", qo.getIsPolar(), statRes);
            vos.add(vo);
            for (Integer directId : info.getDirectIds()) {
                for (String item : allItems) {
                    List<Stat> statList = pointDirectDayGroup.get(pointId + "#" + directId + "#" + item);
                    if (!ObjectUtils.isEmpty(statList)) {
                        Stat s = togetherStat(statList);
                        s.setAttrId(qo.getAttrId());
                        s.setAttrName(qo.getAttrName());
                        s.setId(index.incrementAndGet());
                        s.setPointId(pointId);
                        s.setPointNo(pointNo);
                        s.setDirect(directId);
                        s.setItem(item);
                        statRes.add(s);
                    } else {
                        Stat s = new Stat();
                        s.setAttrId(qo.getAttrId());
                        s.setAttrName(qo.getAttrName());
                        s.setId(index.incrementAndGet());
                        s.setPointId(pointId);
                        s.setPointNo(pointNo);
                        s.setDirect(directId);
                        s.setItem(item);
                        statRes.add(s);
                    }
                }
            }
        }
        return vos;
    }


    /**
     * 导出Excel
     */
    public void exportExcel(StatQo qo, HttpServletResponse response) throws IOException {
        String[] keys = new String[]{"pointNo", "direct", "measureNum", "maxVal", "maxValTime", "minVal", "minValTime", "avgVal", "changeRange"};
        String[] titles = new String[]{"测点编号", "方向", "测次", "最大值", "最大值时间", "最小值", "最小值时间", "平均值", "变幅"};

        Map<String, List<Map<String, Object>>> map = new HashMap<>();
        for (int i = 0; i < qo.getAttrIds().length; i++) {
            Integer attrId = qo.getAttrIds()[i];
            String attrName = qo.getAttrNames()[i];

            qo.setAttrId(attrId);
            qo.setAttrName(attrName);

            List<StatVo> vos = new ArrayList<>();
            if ("feature-custom".equals(qo.getType())) {
                keys = new String[]{"pointNo", "direct", "measureNum", "maxVal", "maxValTime", "minVal", "minValTime", "avgVal", "changeRange"};
                titles = new String[]{"测点编号", "方向", "测次", "最大值", "最大值时间", "最小值", "最小值时间", "平均值", "变幅"};
                StatVo statVo = featureStat(qo.getFieldNum(), qo, null, "时段");
                if (statVo != null) {
                    vos.add(statVo);
                }
            } else if ("feature-month".equals(qo.getType())) {
                keys = new String[]{"pointNo", "direct", "month", "measureNum", "maxVal", "maxValTime", "minVal", "minValTime", "avgVal", "changeRange"};
                titles = new String[]{"测点编号", "方向", "月份", "测次", "最大值", "最大值时间", "最小值", "最小值时间", "平均值", "变幅"};
                StatVo statVo = featureStat(qo.getFieldNum(), qo, qo.getMonthStartDay(), "月度");
                if (statVo != null) {
                    vos.add(statVo);
                }
            } else if ("feature-quarter".equals(qo.getType())) {
                keys = new String[]{"pointNo", "direct", "qu", "measureNum", "maxVal", "maxValTime", "minVal", "minValTime", "avgVal", "changeRange"};
                titles = new String[]{"测点编号", "方向", "季度", "测次", "最大值", "最大值时间", "最小值", "最小值时间", "平均值", "变幅"};
                StatVo statVo = featureStat(qo.getFieldNum(), qo, qo.getQuarterStartMonth(), "季度");
                if (statVo != null) {
                    vos.add(statVo);
                }
            } else if ("feature-year".equals(qo.getType())) {
                keys = new String[]{"pointNo", "direct", "year", "measureNum", "maxVal", "maxValTime", "minVal", "minValTime", "avgVal", "changeRange"};
                titles = new String[]{"测点编号", "方向", "年份", "测次", "最大值", "最大值时间", "最小值", "最小值时间", "平均值", "变幅"};
                StatVo statVo = featureStat(qo.getFieldNum(), qo, null, "年度");
                if (statVo != null) {
                    vos.add(statVo);
                }
            } else if ("daily-custom".equals(qo.getType())) {
                vos = dailyStat(qo.getFieldNum(), qo, null, "时段");
            } else if ("daily-month".equals(qo.getType())) {
                keys = new String[]{"pointNo", "direct", "month", "measureNum", "maxVal", "maxValTime", "minVal", "minValTime", "avgVal", "changeRange"};
                titles = new String[]{"测点编号", "方向", "月份", "测次", "最大值", "最大值时间", "最小值", "最小值时间", "平均值", "变幅"};
                vos = dailyStat(qo.getFieldNum(), qo, qo.getMonthStartDay(), "月度");
            } else if ("daily-quarter".equals(qo.getType())) {
                keys = new String[]{"pointNo", "direct", "qu", "measureNum", "maxVal", "maxValTime", "minVal", "minValTime", "avgVal", "changeRange"};
                titles = new String[]{"测点编号", "方向", "季度", "测次", "最大值", "最大值时间", "最小值", "最小值时间", "平均值", "变幅"};
                vos = dailyStat(qo.getFieldNum(), qo, qo.getQuarterStartMonth(), "季度");
            } else if ("daily-year".equals(qo.getType())) {
                keys = new String[]{"pointNo", "direct", "year", "measureNum", "maxVal", "maxValTime", "minVal", "minValTime", "avgVal", "changeRange"};
                titles = new String[]{"测点编号", "方向", "年份", "测次", "最大值", "最大值时间", "最小值", "最小值时间", "平均值", "变幅"};
                vos = dailyStat(qo.getFieldNum(), qo, null, "年度");
            }
            if (ObjectUtils.isEmpty(vos)) {
                return;
            }

            for (StatVo vo : vos) {
                String sheetName = vo.getTableName();
                List<Stat> stats = vo.getStats();
                sortByStat(stats);

                List<Map<String, Object>> list = new ArrayList<>();
                for (Stat stat : stats) {
                    Map<String, Object> oneData = new HashMap<>();
                    oneData.put("pointNo", stat.getPointNo());
                    oneData.put("direct", stat.getDirectName());
                    oneData.put("maxVal", stat.getMaxValue());
                    oneData.put("maxValTime", stat.getMaxValueTime());
                    oneData.put("minVal", stat.getMinValue());
                    oneData.put("minValTime", stat.getMinValueTime());
                    oneData.put("measureNum", stat.getMeasureNum());
                    oneData.put("changeRange", stat.getChangeRange());
                    oneData.put("avgVal", stat.getAvgValue());
                    oneData.put("item", stat.getItem());
                    oneData.put("month", stat.getItem());
                    oneData.put("year", stat.getItem());
                    oneData.put("qu", stat.getItem());
                    list.add(oneData);
                }
                map.put(sheetName, list);
            }
        }
        ExcelUtil.exportNormalExcel("测值统计", titles, keys, map, response, true, "yyyy-MM-dd HH:mm:ss", true);
    }

    /**
     * 按测点编号排序
     */
    public static void sortByStat(List<Stat> stats) {
        if (ObjectUtils.isEmpty(stats)) {
            return;
        }

        Collections.sort(stats, (p1, p2) -> {
            if (p1.getPointNo() == null || p2.getPointNo() == null) {
                return 0;
            }

            String[] arr1 = p1.getPointNo().split("-");
            String[] arr2 = p2.getPointNo().split("-");
            if (arr1.length != 2 && arr2.length != 2) {
                return arr1[0].compareTo(arr2[0]);
            } else if (arr1.length == 2 && arr2.length != 2) {
                return -1;
            } else if (arr1.length != 2 && arr2.length == 2) {
                return 1;
            }

            String letter1 = extractLetter(arr1[0]);
            String letter2 = extractLetter(arr2[0]);
            if (letter1 == null && letter2 == null) {
                return 0;
            } else if (letter1 != null && letter2 == null) {
                return -1;
            } else if (letter1 == null && letter2 != null) {
                return 1;
            } else {
                int res = letter1.compareTo(letter2);
                if (res == 0) {
                    for (int i = 0; i < 2; i++) {
                        int num1 = extractNum(arr1[i]);
                        int num2 = extractNum(arr2[i]);
                        if (num1 == num2) {
                            continue;
                        } else {
                            return num1 - num2;
                        }
                    }
                } else {
                    return res;
                }
            }
            return 0;
        });
    }

    /**
     * 获取测点ID和编号的映射关系
     */
    private Map<Integer, String> getPointIdAndNoMapping(List<Integer> ids, String[] names) {
        Map<Integer, String> map = new HashMap<>(ids.size());
        for (int i = 0; i < ids.size(); i++) {
            map.put(ids.get(i), names[i]);
        }
        return map;
    }

    /**
     * 聚合
     */
    private Stat togetherStat(List<Stat> stats) {
        float max = Integer.MIN_VALUE;
        float min = Integer.MAX_VALUE;
        Date maxTime = null, minTime = null;
        float total = 0f;
        int measureNum = 0;
        for (Stat stat : stats) {
            if (stat.getMaxValue() != null && stat.getMaxValue() > max) {
                max = stat.getMaxValue();
                maxTime = stat.getMaxValueTime();
            }
            if (stat.getMinValue() != null && stat.getMinValue() < min) {
                min = stat.getMinValue();
                minTime = stat.getMinValueTime();
            }
            /* 记录上一次值，用于计算本次的总测次数 */
            int tempMeasureNum = measureNum;
            if (stat.getApprovalCountInit() != null) {
                measureNum += stat.getApprovalCountInit();
            }
            if (stat.getApprovalCountPass() != null) {
                measureNum += stat.getApprovalCountPass();
            }
            if (stat.getAvgValue() != null) {
                /* 原为记录本次平均值，现在改为记录本次总值 */
                total += stat.getAvgValue() * (measureNum - tempMeasureNum);
            }
        }
        Stat statRes = new Stat();
        if (minTime != null) {
            statRes.setMinValue(min);
            statRes.setMinValueTime(minTime);
        }
        if (maxTime != null) {
            statRes.setMaxValue(max);
            statRes.setMaxValueTime(maxTime);
        }
        statRes.setMeasureNum(measureNum);
        if (measureNum != 0) {
            /* 原为计算每次平均值和的平均值，现改为计算总数据/总测次 */
            statRes.setAvgValue(total / measureNum);
        }
        return statRes;
    }

    private String toMonthStr(int temMonth) {
        String month = temMonth + "";
        if (month.length() == 1) {
            return "0" + month;
        }
        return month;
    }

    /**
     * 日期归类
     */
    private List<String> classifyDay(String startDay, String endDay, String type, Integer start) {
        List<String> days = TimeUtil.days(startDay, endDay);

        Set<String> set = new HashSet<>();
        if (type.endsWith("custom")) {
            /* 自定义归类 */
            set.add(startDay + " ~ " + endDay);
        } else if (type.endsWith("month")) {
            /* 按月归类 */
            for (String day : days) {
                String[] dayArr = day.split("-");
                String y = dayArr[0];
                String m = dayArr[1];
                String d = dayArr[2];
                if (Integer.parseInt(d) < start) {
                    int temM = Integer.parseInt(m) - 1;
                    if (temM < 1) {
                        // 往前推一个月
                        temM = 12;
                        y = String.valueOf(Integer.parseInt(y) - 1);
                    }
                    m = toMonthStr(temM);
                }
                set.add(y + "-" + m);
            }
        } else if (type.endsWith("quarter")) {
            /* 按季归类 */
            for (String day : days) {
                int quarter;
                String[] dayArr = day.split("-");
                String y = dayArr[0];
                String m = dayArr[1];
                if (Integer.parseInt(m) < start) {
                    // 往前推一年
                    y = String.valueOf(Integer.parseInt(y) - 1);
                    quarter = ((Integer.parseInt(m) + 12 + start)) % 12 / 3 + 1;
                } else {
                    quarter = ((Integer.parseInt(m) + 12 - start)) % 12 / 3 + 1;
                }
                set.add(y + "-" + quarter + "季度");
            }
        } else if (type.endsWith("year")) {
            /* 按年归类 */
            for (String day : days) {
                String[] dayArr = day.split("-");
                String y = dayArr[0];
                set.add(y + "年");
            }
        }

        /* 排序 */
        List<String> list = new ArrayList<>(set.size());
        list.addAll(set);
        Collections.sort(list);
        return list;
    }

    /**
     * 去掉用户没有勾选的测点
     */
    private void removeNotSelected(List<PointDirectVo> infos, List<Integer> pointIds) {
        if (ObjectUtils.isEmpty(infos)) {
            return;
        }
        Iterator<PointDirectVo> it = infos.iterator();
        while (it.hasNext()) {
            PointDirectVo vo = it.next();
            if (!pointIds.contains(vo.getPointId())) {
                it.remove();
            }
        }
    }

    /**
     * 存量数据甘特图
     */
    public Map<String, Object> existDataChart(String fieldNum, ExistDataQo qo) {
        /* 获取仪器下所有测点 */
        List<PointCommon> points = monitorService.getPointsByInst(qo.getInstId());
        sortByNo(points);

        if (ObjectUtils.isEmpty(points)) {
            return Collections.emptyMap();
        }

        /* 获取仪器所属监测项目以及该仪器下所有分量 */
        Integer monitorId = monitorService.getMonitorIdByInst(qo.getInstId());
        Map<String, List<AttrCommon>> rateAttrs = monitorService.getInstAttrs(points.get(0).getId()).getRateAttrs();

        Map<String, Object> map = new LinkedHashMap<>();
        List<Integer> pointIds = points.stream().map(PointCommon::getId).collect(Collectors.toList());

        /* 获取统计数据 */
        List<Stat> stats = new ArrayList<>();
        rateAttrs.forEach((k, v) -> {
            for (AttrCommon attr : v) {
                try {
                    stats.addAll(statMapper.getStatDataByTimePointAttrRate(fieldNum, qo.getStartTime(), qo.getEndTime(), pointIds, attr.getId(), monitorId, null));
                } catch (Exception e) {
                }
            }
        });

        /* 将统计数据并按测点分组，判断每个测点下是否有数据 */
        Map<Integer, List<Stat>> pointStatMap = stats.stream().collect(Collectors.groupingBy(Stat::getPointId));
        for (PointCommon point : points) {
            List<Stat> statList = pointStatMap.get(point.getId());
            if (ObjectUtils.isEmpty(statList)) {
                map.put(point.getNo(), null);
            } else {
                Set<ExistStatVo> vos = new HashSet<>();
                for (Stat stat : statList) {
                    if (!isExistData(stat)) {
                        continue;
                    }

                    Date startDay = TimeUtil.parse2Day(stat.getDay());
                    vos.add(new ExistStatVo(startDay, TimeUtil.addDays(startDay, 1)));
                }
                map.put(point.getNo(), vos);
            }
        }
        return map;
    }

    /**
     * 判断是否有数据
     */
    private boolean isExistData(Stat stat) {
        if (stat.getApprovalCountInit() != null && stat.getApprovalCountInit() != 0) {
            return true;
        }
        if (stat.getApprovalCountPass() != null && stat.getApprovalCountPass() != 0) {
            return true;
        }
        return stat.getApprovalCountFail() != null && stat.getApprovalCountFail() != 0;
    }

    /**
     * 按测点编号排序
     */
    public static void sortByNo(List<PointCommon> points) {
        if (ObjectUtils.isEmpty(points)) {
            return;
        }

        Collections.sort(points, (p1, p2) -> {
            if (p1.getNo() == null || p2.getNo() == null) {
                return 0;
            }

            String[] arr1 = p1.getNo().split("-");
            String[] arr2 = p2.getNo().split("-");
            if (arr1.length != 2 && arr2.length != 2) {
                return arr1[0].compareTo(arr2[0]);
            } else if (arr1.length == 2 && arr2.length != 2) {
                return -1;
            } else if (arr1.length != 2 && arr2.length == 2) {
                return 1;
            }

            String letter1 = extractLetter(arr1[0]);
            String letter2 = extractLetter(arr2[0]);
            if (letter1 == null && letter2 == null) {
                return 0;
            } else if (letter1 != null && letter2 == null) {
                return -1;
            } else if (letter1 == null && letter2 != null) {
                return 1;
            } else {
                int res = letter1.compareTo(letter2);
                if (res == 0) {
                    for (int i = 0; i < 2; i++) {
                        int num1 = extractNum(arr1[i]);
                        int num2 = extractNum(arr2[i]);
                        if (num1 == num2) {
                            continue;
                        } else {
                            return num1 - num2;
                        }
                    }
                } else {
                    return res;
                }
            }
            return 0;
        });
    }

    /**
     * 提取字母
     */
    private static String extractLetter(String s) {
        if (ObjectUtils.isEmpty(s)) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i <s.length(); i++) {
            char c = s.charAt(i);
            if (Character.isLetter(c)) {
                sb.append(c);
            }
        }
        if (sb.length() == 0) {
            return null;
        }
        return sb.toString();
    }

    /**
     * 从字符串的最后一位往前提取数字
     */
    private static Integer extractNum(String s) {
        if (ObjectUtils.isEmpty(s)) {
            return Integer.MAX_VALUE;
        }

        StringBuilder sb = new StringBuilder();
        for (int i = s.length() - 1; i >= 0; i--) {
            char c = s.charAt(i);
            if (Character.isDigit(c)) {
                sb.append(c);
            } else {
                continue;
            }
        }
        if (sb.length() == 0) {
            return Integer.MAX_VALUE;
        }
        return Integer.parseInt(sb.reverse().toString());
    }

}




