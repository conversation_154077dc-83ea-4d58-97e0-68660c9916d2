package com.hdec.data.service;

import com.hdec.data.util.ElementRendererUtil;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.ddr.poi.html.HtmlRenderContext;
import org.ddr.poi.html.tag.ImageRenderer;
import org.jsoup.nodes.Element;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTOnOff;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STOnOff;
import org.springframework.util.ObjectUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;

public class Pic<PERSON><PERSON> extends ImageRenderer {

    protected final static String NO_RES = " / ";

    public boolean renderStart(Element element, HtmlRenderContext context) {
        String dataPath = element.attr("data-path");
        System.out.println("==========dataPath:" + dataPath);
        if (ObjectUtils.isEmpty(dataPath) || NO_RES.equals(dataPath)) {
            //默认的重载实现
            super.renderStart(element, context);
            return false;
        }

        int width = 0;
        int wholeWidth = 5720000;
        String dataJson = element.attr("data-json");
        if (!ObjectUtils.isEmpty(dataJson)) {
            if (dataJson.contains("一半")) {
                width = wholeWidth / 2;
            } else if (dataJson.contains("三分之一")) {
                width = wholeWidth / 3;
            } else if (dataJson.contains("四分之一")) {
                width = wholeWidth / 4;
            } else {
                width = wholeWidth;
            }
        }

//        if (element.parent() != null && "lineWrap".equals(element.parent().attr("data-type"))) {
//        }
        /* 通过修改XML文件来更改配置 */
        XWPFParagraph closestParagraph = context.getClosestParagraph();
        /* 图片居中显示 */
        closestParagraph.setAlignment(ParagraphAlignment.LEFT);
        CTP ctp = closestParagraph.getCTP();
        CTPPr pPr = ctp.getPPr();
        if (!pPr.isSetSnapToGrid()) {
            CTOnOff ctOnOff = pPr.addNewSnapToGrid();
            /* 取消掉"定义文档网格时对齐网格" */
            ctOnOff.setVal(STOnOff.X_0);
        }

        try {
            FileInputStream is = new FileInputStream(dataPath);
            BufferedImage img = ImageIO.read(new File(dataPath));
            int compHeight = getImageHeight(img.getWidth(), img.getHeight(), width);
            context.renderPicture(is, 6, "img", width, compHeight, null);
            is.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    //算法:等比例缩小
    private int getImageHeight(int originWidth, int originHeight, int renderEMUs) {
        int originEMUs = Units.pixelToEMU(originWidth);
        int renderEUMsHeight = Math.round(Units.pixelToEMU(originHeight) * ((float) renderEMUs / originEMUs));
        return renderEUMsHeight;
    }

    @Override
    public void renderEnd(Element element, HtmlRenderContext context) {
        /* 处理 margin 属性未正确识别为对齐方式导致的 table 设置了对齐方式不生效 */
        String style = element.attributes().get("style");
        ParagraphAlignment alignment = ElementRendererUtil.getParagraphAlignmentFromStyle(style);
        if (alignment != null) {
            context.getClosestParagraph().setAlignment(alignment);
        }
        super.renderEnd(element, context);
    }
}