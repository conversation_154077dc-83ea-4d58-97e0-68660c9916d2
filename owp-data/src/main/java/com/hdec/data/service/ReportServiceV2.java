package com.hdec.data.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hdec.common.domain.AttrCommon;
import com.hdec.common.domain.InstCommon;
import com.hdec.common.domain.PointCommon;
import com.hdec.common.domain.SeaFacilityCommon;
import com.hdec.common.util.FileUtil;
import com.hdec.common.util.ReportUtil;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.domain.BindInfo;
import com.hdec.data.domain.ContentVo;
import com.hdec.data.domain.OutlineNav;
import com.hdec.data.domain.ReportOutlineNav;
import com.hdec.data.domain.report2.Report;
import com.hdec.data.domain.template.Mould;
import com.hdec.data.domain.template.ReportCover;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.feign.WindService;
import com.hdec.data.mapper.NavMouldMapper;
import com.hdec.data.mapper.ReportMapperV2;
import com.hdec.data.mapper.ReportOutlineMapper;
import com.hdec.data.qo.*;
import com.hdec.data.util.EasyWord;
import com.hdec.data.util.JsoupUtil;
import com.hdec.data.vo.ParamVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.util.Pair;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 报告管理业务版本2
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ReportServiceV2 {

    @Autowired
    private ReportMapperV2 reportMapperV2;

    @Autowired
    private ReportOutlineMapper outlineMapper;

    @Autowired
    private WindService windService;

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private ReportOutlineService outlineService;

    @Autowired
    private ReportCoverService coverService;

    @Autowired
    private VarService varService;

    @Autowired
    private NavMouldMapper navMouldMapper;

    /**
     * 访问映射
     */
    @Value("${file.visitMapping}")
    private String visitMapping;

    /**
     * 文件本地路径 - Windows
     */
    @Value("${file.windowPath}")
    private String windowPath;

    /**
     * 文件本地路径 - Linux
     */
    @Value("${file.linuxPath}")
    private String linuxPath;

    private static Map<Integer, Integer> processMap = new HashMap<>();

    /**
     * 报告列表
     */
    public List<Report> list(String fieldNum) {
        return reportMapperV2.list(fieldNum);
    }

    /**
     * 新增报告
     */
    public void add(Report report) {
        report.setStatus("生成中(0%)");
        reportMapperV2.add(report);

        /* 异步生成word */
        new Thread(() -> {
            try {
                genWordASync(report);
            } catch (Exception e) {
                log.error("生成报告出错：", e);
            } finally {
                processMap.remove(report.getId());
            }
        }).start();
    }

    /**
     * 批量删除报告
     */
    public void delete(Integer[] ids) {
        reportMapperV2.delete(ids);
    }

    /**
     * 异步生成word
     */
    private void genWordASync(Report report) throws Exception {
        /* 封面 */
        ReportCover cover = coverService.detail(report.getCoverId());
        // 替换封面变量
        replaceCoverVar(report, cover);

        /* 大纲 */
        /* 需要用到的数据 */
        List<SeaFacilityCommon> fieldSeaFacility = windService.allSeaFacility(report.getFieldNum());
        List<PointCommon> fieldPoints = monitorService.getPointsByFieldNum(report.getFieldNum()).stream()
                .filter(e -> ObjectUtils.isEmpty(e.getStatus()) || "正常".equals(e.getStatus())).collect(Collectors.toList());
        Map<Integer, AttrCommon> fieldAttrMap = monitorService.allAttrByField(report.getFieldNum()).stream().collect(Collectors.toMap(AttrCommon::getId, Function.identity()));
        Map<Integer, InstCommon> fieldInstMap = getFieldInstMap(report.getFieldNum());

        /*  填充 分量的仪器信息  */
        fieldAttrMap.forEach((k, v) -> {
            if (v.getInstId() != null) {
                InstCommon inst = fieldInstMap.get(v.getInstId());
                v.setInstName(inst.getName());
                v.setInstDirect(inst.getDirect());
            }
        });

        // 获取该大纲下所有导航树
        List<ReportOutlineNav> navTree = outlineService.nav(report.getFieldNum(), report.getOutlineId());

        AtomicInteger navCount = new AtomicInteger();
        countNavTree(navTree, navCount);

        /* 收集各个节点下所有模板内容 */
        List<ContentVo> mouldContents = new ArrayList<>();
        iteratorNavTreeForContent(report.getId(), TimeUtil.format2Second(report.getStartTime()), TimeUtil.format2Second(report.getEndTime()), navTree, mouldContents, 1, fieldSeaFacility, fieldPoints, fieldAttrMap, navCount.get(), fieldInstMap, report.getFieldNum());

        try {
            String docPath = linuxPath + "reports/" + report.getId() + "/";
            FileUtil.makeSureDirExist(docPath);
            String fieldName = windService.getFieldNameByFieldNum(report.getFieldNum());
            EasyWord.html2word(cover.getContent(), mouldContents, "/home/<USER>", docPath + "月报.docx", fieldName, report.getEndTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        reportMapperV2.update(new Report(report.getId(), "已完成", visitMapping + "/reports/" + report.getId() + "/月报.docx"));
    }

    public Map<Integer, InstCommon> getFieldInstMap(String fieldNum) {
        List<InstCommon> insts = monitorService.allInstByField(fieldNum);
        if (ObjectUtils.isEmpty(insts)) {
            return Collections.emptyMap();
        }

        return insts.stream().collect(Collectors.toMap(InstCommon::getId, Function.identity(), (key1, key2) -> key2));
    }

    /**
     * 遍历导航树
     */
    private void countNavTree(List<ReportOutlineNav> navTree, AtomicInteger navCount) {
        if (ObjectUtils.isEmpty(navTree)) {
            return;
        }

        for (ReportOutlineNav nav : navTree) {
            List<Mould> moulds = outlineMapper.getMouldByNavId(nav.getBid());
            nav.setMoulds(moulds);
            if (!ObjectUtils.isEmpty(moulds)) {
                navCount.addAndGet(countPicNum(moulds));
            }

            if (!ObjectUtils.isEmpty(nav.getChildren())) {
                countNavTree(nav.getChildren(), navCount);
            }
        }
    }

    private int countPicNum(List<Mould> moulds) {
        int count = 0;
        for (Mould mould : moulds) {
            String content = mould.getContent();
            if (!ObjectUtils.isEmpty(content)) {
                Document document = Jsoup.parse(content);
                count += document.getElementsByAttributeValue("data-type", "line").size();
                count += document.getElementsByAttributeValue("data-type", "chart").size();
            }
        }
        return count;
    }

    /**
     * 遍历导航树(封装模板内容)
     */
    private void iteratorNavTreeForContent(Integer reportId, String sTime, String eTime, List<ReportOutlineNav> navTree, List<ContentVo> mouldContents, int level, List<SeaFacilityCommon> fieldSeaFacility,
                                           List<PointCommon> fieldPoints, Map<Integer, AttrCommon> fieldAttrMap, int navCount, Map<Integer, InstCommon> fieldInstMap, String fieldNum) throws Exception {
        if (ObjectUtils.isEmpty(navTree)) {
            return;
        }
        for (ReportOutlineNav nav : navTree) {
            StringBuilder content = new StringBuilder();
            List<Mould> moulds = nav.getMoulds();
            String seaName = "";
            if (!ObjectUtils.isEmpty(moulds)) {
                for (Mould mould : moulds) {
                    if (ObjectUtils.isEmpty(mould.getContent())) {
                        continue;
                    }

                    /* 获取模板绑定信息 */
                    BindInfo bindInfo = getMouldBindSeaFacility(nav.getBid(), mould.getId(), fieldSeaFacility);
                    seaName = bindInfo.getSeaName();

                    /* 添加引用属性 */
                    addDataRefAttr(mould, bindInfo.getRefVal());

                    /* 去掉系统颜色 */
                    removeSystemColor(mould);

                    /* 替换模板变量 */
                    replaceMouldVar(reportId, sTime, eTime, mould, bindInfo.getSeaId(), fieldSeaFacility, fieldPoints, fieldAttrMap, navCount, fieldInstMap, fieldNum);
                    content.append(mould.getContent());
                }
            }
            mouldContents.add(new ContentVo(nav.getName(), level, nav.getSeqNum(), content.toString(), seaName));

            /* 递归遍历子节点 */
            if (!ObjectUtils.isEmpty(nav.getChildren())) {
                iteratorNavTreeForContent(reportId, sTime, eTime, nav.getChildren(), mouldContents, level + 1, fieldSeaFacility, fieldPoints, fieldAttrMap, navCount, fieldInstMap, fieldNum);
            }
        }
    }

    private BindInfo getMouldBindSeaFacility(String navId, Integer mouldId, List<SeaFacilityCommon> fieldSeaFacility) {
        MouldVal navMouldValue = navMouldMapper.getNavMouldValue(Integer.parseInt(navId), mouldId);
        if (navMouldValue == null) {
            return new BindInfo();
        }

        /* 获取绑定的海上设施名称 */
        String seaName = null;
        String seaId = navMouldValue.getFanVal();
        if (seaId != null) {
            List<SeaFacilityCommon> res = fieldSeaFacility.stream().filter(e -> e.getId().equals(seaId)).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(res) && res.size() == 1) {
                seaName = res.get(0).getName();
            }
        }
        return new BindInfo(seaId, seaName, navMouldValue.getRefVal());
    }

    /**
     * 去掉系统颜色
     */
    private void removeSystemColor(Mould mould) {
        Document document = Jsoup.parse(mould.getContent());
        List<Element> sysElements = JsoupUtil.getElementsByAttr(document, "data-color-type", "sys");
        for (Element sysElement : sysElements) {
            String style = sysElement.attr("style");
            if (ObjectUtils.isEmpty(style)) {
                continue;
            }

            String[] attrs = style.split(";");
            if (attrs.length == 1) {
                sysElement.removeAttr("style");
            } else {
                StringBuilder sb = new StringBuilder();
                for (String attr : attrs) {
                    if (!attr.startsWith("color:")) {
                        sb.append(attr);
                        sb.append(";");
                    }
                }
                sysElement.attr("style", sb.toString());
            }
        }
        mould.setContent(document.body().html());
    }

    /**
     * 更新进度
     */
    private void updateProcess(Integer reportId, int navCount) {
        Integer curCount = processMap.get(reportId);
        if (curCount == null) {
            curCount = 1;
            processMap.put(reportId, curCount);
        } else {
            processMap.put(reportId, curCount + 1);
        }

        if (navCount != 0) {
            int process = curCount * 100 / navCount;
            if (process > 95) {
                process = 95;
            }
            System.out.println("进行中(" + process + "%)");
            reportMapperV2.updateStatus(reportId, "进行中(" + process + "%)");
        }
    }

    /**
     * 添加引用属性
     */
    private void addDataRefAttr(Mould mould, String refVal) {
        if (ObjectUtils.isEmpty(refVal)) {
            return;
        }

        List<ParamVo> paramVos = JSON.parseArray(refVal, ParamVo.class);
        Map<String, ParamVo> paramVosMap = paramVos.stream().collect(Collectors.toMap(ParamVo::getId, Function.identity(), (key1, key2) -> key2));

        Document document = Jsoup.parse(mould.getContent());
        List<Element> refNumElements = JsoupUtil.getElementsByAttr(document, "data-type", "refNum");
        for (Element refNumElement : refNumElements) {
            ParamVo paramVo = paramVosMap.get(refNumElement.id());
            if (paramVo != null) {
                /* 获取当前RefId在文档中的第几个 */
                String refId = paramVo.getRefId();
                int index = getRefIndex(refId);
                refId = refId.substring(refId.indexOf("_") + 1);
                refNumElement.attr("data-ref", refId);
                refNumElement.attr("data-ref-index", String.valueOf(index));
            }
        }
        mould.setContent(document.body().html());
    }


    /**
     * 获取引用的图表是在文档中相同模版的第几个
     *
     * @param dataRef dataRef
     * @return int
     */
    private int getRefIndex(String dataRef) {
        int index = 0;
        if (!dataRef.contains("_")) {
            return index;
        }
        String navIdStr = dataRef.substring(0, dataRef.indexOf("_"));
        if (navIdStr.isEmpty()) {
            return index;
        }
        /* 查询文档中引用同一模版的nav */
        try {
            int navId = Integer.parseInt(navIdStr);
            List<Integer> navIds = outlineMapper.getSameMouldNavIds(navId);
            /* 根据大纲目录的顺序排序 */
            if (navIds.size() > 1) {
                List<OutlineNav> outlineNavs = outlineMapper.navListByNavId(navId);
                List<Integer> continsNavIds = outlineNavs.stream().filter(nav -> navIds.contains(nav.getId())).map(OutlineNav::getId).collect(Collectors.toList());
                List<Integer> sortNavIds = new ArrayList<>();
                sortNavIds.add(0);
                /* 逐级向下查找，直到找出所有包含的标题 */
                List<Integer> pids = new ArrayList<>();
                pids.add(0);
                while (continsNavIds.size() > 0) {
                    List<Integer> nextPids = new ArrayList<>();
                    for (Integer pid : pids) {
                        int pidIndex = sortNavIds.indexOf(pid);
                        List<OutlineNav> navs = outlineNavs.stream().filter(nav -> Objects.equals(nav.getPid(), pid)).collect(Collectors.toList());
                        List<Integer> navsId = navs.stream().map(OutlineNav::getId).collect(Collectors.toList());
                        continsNavIds.removeIf(n -> navsId.contains(n));
                        nextPids.addAll(navsId);
                        /* 将当前PID下的数据插入到pid后面 */
                        Collections.reverse(navsId);
                        navsId.forEach(n -> sortNavIds.add(pidIndex + 1, n));
                    }
                    pids.clear();
                    pids.addAll(nextPids);
                }
                /* 过滤掉不需要的ID */
                List<Integer> sortNavs = sortNavIds.stream().filter(id -> navIds.contains(id)).collect(Collectors.toList());
                for (int i = 0; i < sortNavs.size(); i++) {
                    if (navId == sortNavs.get(i)) {
                        index = i;
                        break;
                    }
                }

            }
        } catch (Exception e) {
            log.error("获取大纲中引用【{}】相同模版的标题异常:{}", dataRef, e.getMessage());
        }
        return index;
    }

    /**
     * 获取一行中显示的图片数量（通过occupyWidth属性）
     * 如果没有occupyWidth属性则返回-1
     *
     * @param dateJson dateJson
     * @return int
     */
    public static int getOneLineImageCount(String dateJson) {
        JSONObject json = JSONObject.parseObject(dateJson);
        String occupyWidth = json.getString("occupyWidth");
        if (occupyWidth == null) {
            return 1;
        }
        int count = -1;
        switch (occupyWidth) {
            case "占满整行": {
                count = 1;
                break;
            }
            case "占据一半": {
                count = 2;
                break;
            }
            case "占三分之一": {
                count = 3;
                break;
            }
            case "占四分之一": {
                count = 4;
                break;
            }
        }
        return count;
    }

    /**
     * 替换模板变量
     */
    private void replaceMouldVar(Integer reportId, String sTime, String eTime, Mould mould, String seaId, List<SeaFacilityCommon> fieldSeaFacility,
                                 List<PointCommon> fieldPoints, Map<Integer, AttrCommon> fieldAttrMap, int navCount, Map<Integer, InstCommon> fieldInstMap, String fieldNum) throws Exception {
        Document document = Jsoup.parse(mould.getContent());
        Elements jsonElements = document.getElementsByAttribute("data-json");
        for (Element element : jsonElements) {
            String dateType = element.attr("data-type");
            String dateJson = element.attr("data-json");

            String res = null;
            try {
                res = varService.getDataByVar(dateType, dateJson, sTime, eTime, seaId, fieldSeaFacility, fieldPoints, fieldAttrMap, fieldInstMap, fieldNum);
            } catch (Exception e) {
                log.error("dateType:{}, dateJson:{}替换模板变量:{}", dateType, dateJson, e.getMessage(), e);
            }
            log.info("dateType:{}, dateJson:{}, 结果:{}", dateType, dateJson, res);

            if ("attr".equals(dateType) || "table".equals(dateType)) {
                element.html(res == null ? " / " : ReportUtil.addNewRome2EngNumber(res));
            } else if ("line".equals(dateType) || "chart".equals(dateType)|| "spectrum".equals(dateType)) {
                updateProcess(reportId, navCount);
                if (ObjectUtils.isEmpty(res)) {
                    element.attr("data-path", "");
                } else {
                    String[] arr = res.split("&");
                    /* 获取一行中图片的数量 */
                    int groupCont = getOneLineImageCount(dateJson);
                    Element option = element.parent();
                    /* 设置默认1倍行距 */
                    String style = option.attr("style");
                    if (!style.contains("line-height:")) {
                        option.attr("style", style + "line-height: 1;");
                    }
                    for (int i = 0; i < arr.length; i++) {
                        if (i == 0) {
                            // 替换
                            element.attr("data-path", arr[i]);
                        } else {
                            // 增加
                            Element img = document.createElement("img");
                            img.attr("data-path", arr[i]);
                            img.attr("data-json", element.attr("data-json"));
                            /* 每行图片排满后新创建一个<p>标签容纳新的图片 */
                            if (groupCont > 0 && i % groupCont == 0) {
                                option = element.parent().appendElement("p");
                                option.attr("class", element.attr("class"));
                                option.attr("style", element.parent().attr("style"));
                                option.attr("data-type", element.parent().attr("data-type"));
                                option.attr("data-json", element.parent().attr("data-json"));
                                option.appendChild(img);
                            }
                            option.appendChild(img);
                        }
                    }
                }
            } else if ("history_eigen_table".equals(dateType)) {
                HistoryEigenTabQo qo = JSON.parseObject(dateJson, HistoryEigenTabQo.class);
                Integer instId = qo.getInstId();
                List<PointCommon> points = fieldPoints.stream().filter(p -> Objects.equals(p.getSeaFacilityId(), seaId)
                                && Objects.equals(p.getInstId(), instId))
                        .collect(Collectors.toList());
                fillHistoryEigenTable(element, points, dateJson, eTime, seaId, fieldSeaFacility, fieldPoints, fieldAttrMap, fieldInstMap, fieldNum);
            }
        }

        mould.setContent(document.body().html());
    }

    /**
     * 移除 span 标签中携带的 Times New Roman 替换封面中模版变量会自动设置 Times New Roman
     * 如果原模版变量span中设置了 Times New Roman
     * 此时替换的变量中存在中文会也会设置Times New Roman导致中西文之间的间距丢失
     *
     * @param element element
     */
    public static void removeFontFamilyTimesNewRoman(Element element) {
        Elements span = element.getElementsByTag("span");
        span.forEach(s -> {
            String style = span.attr("style");
            if (style.contains("font-family")) {
                style = Arrays.stream(style.split(";"))
                        .filter(attr -> !(attr.contains("font-family") && attr.contains("Times New Roman")))
                        .collect(Collectors.joining(";"));
                span.attr("style", style);
            }
        });
    }

    /**
     * 替换封面变量
     */
    private void replaceCoverVar(Report report, ReportCover cover) {
        String content = cover.getContent();
        if (ObjectUtils.isEmpty(content)) {
            return;
        }
        String year = TimeUtil.format(report.getStartTime(), "yyyy");

        Document document = Jsoup.parse(content);
        List<Element> baseElements = JsoupUtil.getElementsByAttr(document, "data-type", "base");
        for (Element baseElement : baseElements) {
            String text = baseElement.text();
            /* 先移除Times New Roman字体设置，放置模版变量中包含的中文也设置为Times New Roman */
            removeFontFamilyTimesNewRoman(baseElement);
            if ("@{监测年份}".equals(text)) {
                baseElement.html("<span style=\"font-family: Times New Roman\">" + year + "</span>");
            } else if ("@{月报期数}".equals(text)) {
                baseElement.html("<span style=\"font-family: Times New Roman\">" + report.getIssue() + "</span>");
            } else if ("@{总月报期数}".equals(text)) {
                baseElement.html("<span style=\"font-family: Times New Roman\">" + report.getTotalIssue() + "</span>");
            } else if ("@{编写日期(yyyy年mm月dd日)}".equals(text)) {
                baseElement.html(ReportUtil.addNewRome2EngNumber(TimeUtil.format(new Date(), "yyyy年MM月dd日")));
            } else if ("@{编写日期(yyyy年mm月)}".equals(text)) {
                baseElement.html(ReportUtil.addNewRome2EngNumber(TimeUtil.format(new Date(), "yyyy年MM月")));
            } else if ("@{监测时段(yyyy年mm月dd日～yyyy年mm月dd日)}".equals(text)) {
                baseElement.html(ReportUtil.addNewRome2EngNumber(TimeUtil.format(report.getStartTime(), "yyyy年MM月dd日") + "～" + TimeUtil.format(report.getEndTime(), "yyyy年MM月dd日")));
            } else if ("@{监测时段(yyyy-mm-dd～yyyy-mm-dd)}".equals(text)) {
                baseElement.html(ReportUtil.addNewRome2EngNumber(TimeUtil.format(report.getStartTime(), "yyyy-MM-dd") + "～" + TimeUtil.format(report.getEndTime(), "yyyy-MM-dd")));
            }
        }
        cover.setContent(document.body().html());
    }

    private void fillHistoryEigenTable(Element element, List<PointCommon> points, String dataJson, String endTime, String seaId,
                                       List<SeaFacilityCommon> fieldSeaFacility, List<PointCommon> fieldPoints,
                                       Map<Integer, AttrCommon> fieldAttrMap, Map<Integer, InstCommon> fieldInstMap, String fieldNum) {
        Elements trs = element.getElementsByTag("tr");
        HistoryEigenTabQo param = JSONObject.parseObject(dataJson, HistoryEigenTabQo.class);

        StatAttr statAttr = param.getStatAttr();
        String[] statItems = statAttr.getStatItems();
        String[] directs = statAttr.getDirects();

        if (trs.size() > 2) {
            /* 根据测点填充列 */
            /* 填充第一行 */
            Element tr1 = trs.get(0);
            Elements tr1Tds = tr1.getElementsByTag("th");
            Elements tr1TdsTemp = tr1Tds.clone();
            tr1TdsTemp.remove(0);
            IntStream.range(1, tr1Tds.size()).forEach(i -> tr1Tds.get(i).remove());
            points.forEach(point -> {
                String elev = point.getInstallElevation() + "高程";
                String no = point.getNo();
                tr1TdsTemp.forEach(td -> {
                    Element tdc = td.clone();
                    tdc.children().forEach(ch -> {
                        if (ch.text().contains("@{高程")) {
                            ch.text(ch.text().replace("@{高程}", elev));
                        }
                        if (ch.text().contains("@{测点编号}")) {
                            ch.text(ch.text().replace("@{测点编号}", no));
                        }
                    });
                    tr1.appendChild(tdc);
                });
            });

            /* 填充第二行 */
            Element tr2 = trs.get(1);
            Elements tr2Tds = tr2.getElementsByTag("th");
            Elements tr2TdsTemp = tr2Tds.clone();
            tr2Tds.forEach(Node::remove);
            points.forEach(point -> {
                tr2TdsTemp.forEach(td -> {
                    tr2.appendChild(td.clone());
                });
            });

            /* 根据时间填充行 */
            Date endDate = TimeUtil.parse2Second(endTime);
            Date startDate = param.getStartDate() == null ? TimeUtil.addSeconds(endDate, -1) : param.getStartDate();
            /* 获取到临时填充行 */
            Element tempTr = trs.get(2);
            List<Pair<Date, Date>> dateSegment = TimeUtil.getMonthSegment(startDate, endDate);
            Collections.reverse(dateSegment);
            /* 设置行数据 */
            dateSegment.forEach(segment -> {
                Date start = segment.getFirst();
                Date end = segment.getSecond();
                Element dataTr = tempTr.clone();
                Elements tds = dataTr.getElementsByTag("td");
                String date = TimeUtil.format(end, "yyyy年MM月");
                Element dateTd = tds.get(0).text(date);
                IntStream.range(1, tds.size()).forEach(i -> tds.get(i).remove());
                points.forEach(point -> {
                    Arrays.stream(statItems).forEach(stat -> {
                        Arrays.stream(directs).forEach(direct -> {
                            String dateType = "table";
                            String dateJson = getTableDataJson(seaId, point.getId(), statAttr.getAttrId(), direct, stat);
                            String sTime = TimeUtil.format2Second(start);
                            String eTime = TimeUtil.format2Second(end);
                            String var = "-";
                            try {
                                var = varService.getDataByVar(dateType, dateJson, sTime, eTime, seaId,
                                        fieldSeaFacility, fieldPoints, fieldAttrMap, fieldInstMap, fieldNum);
                            } catch (Exception e) {
                                log.error("获取历史特征值表数据异常：{}", e.getMessage());
                            }
                            Element valueTd = dateTd.clone().text(var);
                            dataTr.appendChild(valueTd);
                        });
                    });
                });
                element.appendChild(dataTr);
            });
            tempTr.remove();
        }
    }

    public String getTableDataJson(String seaId, Integer pointId, Integer attrId, String direct, String statItem) {
        TabJsonQo param = new TabJsonQo();
        param.setSeaId(seaId);
        param.setStatItem(statItem);
        param.setAttrId(attrId);
        param.setDirect(direct);
        param.setStatItem(statItem);
        param.setPointIds(Collections.singletonList(pointId));
        return JSONObject.toJSONString(param);
    }

}
