package com.hdec.data.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.hdec.common.domain.*;
import com.hdec.common.util.*;
import com.hdec.common.vo.InstDirectAttr;
import com.hdec.data.domain.Datum;
import com.hdec.data.domain.report.PngInfo;
import com.hdec.data.domain.report.Series;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.qo.AttrStatJsonQo;
import com.hdec.data.qo.ColumnarJsonQo;
import com.hdec.data.qo.ProcessLineJsonQo;
import com.hdec.data.qo.TabJsonQo;
import com.hdec.data.vo.ColumnarVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.math3.util.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.NumberFormat;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * 变量业务逻辑类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class VarService {

    /* 连字符 */
    public static final String HYPHEN = "-";
    /* 数学负号/减号 */
    private static final String MATH_MINUS = "−";

    /* echarts-export 命令目录 */
    private static final String ECHARTS_EXPORT_COMMAND_DIR = "/home/<USER>/ec-expoort/";
    /* echarts-export 生成文件目录 */
    private static final String ECHARTS_EXPORT_FILE_DIR = "/data/highcharts-export/";

    @Autowired
    private ChartService chartService;

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private TdService tdService;

    @Autowired
    private TdBaseService tdBaseService;

    @Autowired
    private ReportVarService reportVarService;

    @Lazy
    @Autowired
    private FreqResultService freqResultService;

    /** 文件本地路径 - Linux */
    @Value("${file.linuxPath}")
    private String linuxPath;

    /**
     * 无结果返回
     */
    protected final static String NO_RES = " / ";

    private static String serializeSeries(List<Series> series, Boolean isMark, String chartAttrName2) {
        String[] colors = {"#3070b7", "#c95c2e", "#00ff00", "#9900ff", "#ff00ff", "#0865f7"};
        String[] shapes = {"circle", "rect", "roundRect", "triangle", "diamond", "arrow"};
        if (ObjectUtils.isEmpty(series)) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        int colorIndex = 0;
        for (Series s : series) {
            List<Datum> data = s.getData();

            sb.append("{");

            sb.append("\"type\":\"");
            sb.append(s.getType());
            sb.append("\",");

            sb.append("\"yAxisIndex\":\"");
            sb.append("0");
            sb.append("\",");

            sb.append("\"name\":\"");
            String name;
            if (chartAttrName2 == null) {
                name = s.getName();
            } else {
                name = s.getName() + "-" + s.getLeftAttrNameWithoutUnit();
            }
            sb.append(name);
            sb.append("\",");

            sb.append("\"symbol\":\"");
            sb.append(shapes[colorIndex]);
            sb.append("\",");

            sb.append("\"lineStyle\": {" + "\"color\": \"").append(colors[colorIndex]).append("\",").append("\"width\": 1").append("},");

            sb.append("\"data\":[");

            Map<Long, Float> markTimeMap = getMarkTimes(data);
            boolean flag = false;
            for (Datum datum : data) {
                if (isMark != null && isMark) {
                    Float aFloat = markTimeMap.get(datum.getTime().getTime());
                    if (aFloat != null) {
                        sb.append("{\"value\": [").append(datum.getTime().getTime()).append(", ").append(datum.getAttrVal()).append("], \"symbolSize\": 6},");
                        continue;
                    }
                }
                sb.append("{\"value\": [").append(datum.getTime().getTime()).append(", ").append(datum.getAttrVal()).append("], \"symbolSize\": 0},");
                flag = true;
            }
            if (flag) {
                sb.deleteCharAt(sb.length() - 1);
            }
            sb.append("]},");

            colorIndex++;
            if (colorIndex >= colors.length) {
                colorIndex = 0;
            }

            List<Datum> data2 = s.getData2();
            if (!ObjectUtils.isEmpty(data2)) {
                sb.append("{");

                sb.append("\"type\":\"");
                sb.append(s.getType());
                sb.append("\",");

                sb.append("\"yAxisIndex\":\"");
                sb.append("1");
                sb.append("\",");

                sb.append("\"name\":\"");
                sb.append(s.getName()).append("-").append(s.getRightAttrNameWithoutUnit());
                sb.append("\",");

                sb.append("\"symbol\":\"");
                sb.append(shapes[colorIndex]);
                sb.append("\",");

                sb.append("\"lineStyle\": {" + "\"color\": \"").append(colors[colorIndex]).append("\",").append("\"width\": 1").append("},");

                sb.append("\"data\":[");

                markTimeMap = getMarkTimes(data2);
                flag = false;
                for (Datum datum : data2) {
                    if (isMark != null && isMark) {
                        Float aFloat = markTimeMap.get(datum.getTime().getTime());
                        if (aFloat != null) {
                            sb.append("{\"value\": [").append(datum.getTime().getTime()).append(", ").append(datum.getAttrVal()).append("], \"symbolSize\": 6},");
                            continue;
                        }
                    }
                    sb.append("{\"value\": [").append(datum.getTime().getTime()).append(", ").append(datum.getAttrVal()).append("], \"symbolSize\": 0},");
                    flag = true;
                }
                if (flag) {
                    sb.deleteCharAt(sb.length() - 1);
                }
                sb.append("]},");

                colorIndex++;
                if (colorIndex >= colors.length) {
                    colorIndex = 0;
                }
            }
        }
        if (sb.charAt(sb.length() - 1) == ',') {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    /**
     * 解析字符串变量为实体类
     *
     * @param type 类型（attr:分量统计值  line:过程线  chart:柱状统计图  table:表格）
     */
    public String getDataByVar(String type, String json, String startTime, String endTime, String seaId,
                               List<SeaFacilityCommon> fieldSeaFacility, List<PointCommon> fieldPoints, Map<Integer, AttrCommon> fieldAttrMap, Map<Integer, InstCommon> fieldInstMap, String fieldNum) throws Exception {
        if (ObjectUtils.isEmpty(json)) {
            return NO_RES;
        }

        /* 根据变量获取数据 */
        if ("attr".equals(type)) {
            // 分量统计值
            return getAttrStatData(json, fieldSeaFacility, fieldPoints, fieldAttrMap, startTime, endTime, seaId, fieldNum);
        } else if ("line".equals(type)) {
            // 过程线
            return getProcessLineData2(json, fieldSeaFacility, fieldPoints, fieldAttrMap, startTime, endTime, seaId, fieldInstMap);
        } else if ("spectrum".equals(type)) {
            // 频谱图
            return getFreqData(json, fieldSeaFacility, fieldPoints, fieldAttrMap, startTime, endTime, seaId, fieldInstMap);
        } else if ("chart".equals(type)) {
            // 柱状统计图
            return getColumnarData(json, fieldSeaFacility, fieldPoints, fieldAttrMap, startTime, endTime, seaId);
        } else if ("table".equals(type)) {
            // 表格
            return getTableData(json, fieldPoints, fieldAttrMap, startTime, endTime, seaId, fieldNum);
        }
        return null;
    }

    private String getFreqData(String json, List<SeaFacilityCommon> fieldSeaFacility, List<PointCommon> fieldPoints, Map<Integer, AttrCommon> fieldAttrMap, String startTime, String endTime, String seaId, Map<Integer, InstCommon> fieldInstMap) {
        System.out.println("------");
        System.out.println(startTime);
        System.out.println(endTime);
        System.out.println(json);
        System.out.println(seaId);
        System.out.println("------");
        JSONObject jsonObject = JSON.parseObject(json);
        Integer pointId = jsonObject.getInteger("pointId");
        Integer direct = jsonObject.getInteger("direct");

        if (pointId == null) {
            return fff(fieldPoints, seaId, direct, startTime, endTime);
        }

        String imgUrl = freqResultService.getByPoint(pointId, direct, startTime, endTime);
        System.out.println(pointId + "和" + direct + "的结果是" + imgUrl);
        if (imgUrl != null) {
            return "/data/" + imgUrl;
        }
        // todo 很多
        System.out.println(linuxPath);
        return null;
    }

    private String fff(List<PointCommon> fieldPoints, String seaId, Integer direct, String startTime, String endTime) {
        List<Integer> pointIds = fieldPoints.stream().filter(e -> seaId.equals(e.getSeaFacilityId())).map(PointCommon::getId).collect(Collectors.toList());
        System.out.println("pointIds:"+pointIds);
        List<String> urls = new ArrayList<>();
        for (Integer pointId : pointIds) {
            String imgUrl = freqResultService.getByPoint(pointId, direct, startTime, endTime);
            log.info("pointId:{}, imgUrl: {}", pointId, imgUrl);
            if (imgUrl != null) {
                urls.add("/data/" + imgUrl);
            }
        }
        return Joiner.on("&").join(urls);
    }

    private String getSeaNameById(String sea_id, List<SeaFacilityCommon> fieldSeaFacility) {
        for (SeaFacilityCommon seaFacilityCommon : fieldSeaFacility) {
            if (sea_id.equals(seaFacilityCommon.getId())) {
                return seaFacilityCommon.getName();
            }
        }
        return null;
    }

    /**
     * 获取表格数据
     */
    private String getTableData(String var, List<PointCommon> fieldPoints, Map<Integer, AttrCommon> fieldAttrMap, String startTime, String endTime, String seaId, String fieldNum) throws Exception {
        TabJsonQo tab = JSON.parseObject(var, TabJsonQo.class);
        AttrCommon attr = fieldAttrMap.get(tab.getAttrId());
        if (ObjectUtils.isEmpty(attr)) {
            return NO_RES;
        }

        List<Integer> pointIds = tab.getPointIds();
        /* 处理按周统计表时，根据week值去更新查询的时间范围 */
        String sTime = startTime;
        String eTime = endTime;
        if (tab.getWeek() != null && tab.getWeek() > 0) {
            int week = tab.getWeek();
            Date sDate = DateUtils.addWeeks(Objects.requireNonNull(TimeUtil.parse2Second(endTime)), week - 5);
            Date eDate = DateUtils.addWeeks(Objects.requireNonNull(TimeUtil.parse2Second(eTime)), week - 4);
            sTime = TimeUtil.format2Second(DateUtils.addSeconds(sDate, 1));
            eTime = TimeUtil.format2Second(eDate);
            pointIds = tab.getPointId() == null ? pointIds : Collections.singletonList(tab.getPointId());
        }

        /* 查询涉及到的所有测点并按仪器分组 */
        Integer instId = attr.getInstId();
        List<PointCommon> points = getTabRelatedPointIds(tab.getSeaId(), instId, pointIds, tab.getCustomVar(), fieldPoints, fieldNum);
        List<Map<String, Object>> lines = tdService.getTogetherData(instId, attr.getRate(), points, attr.getId(), getDirect(tab.getDirect(), points), sTime, eTime, tab.getStatItem());
        if (lines.size() == 0) {
            return NO_RES;
        }

        int acc = getAcc(attr.getDecimalAccuracy());
        if ("最小值".equals(tab.getStatItem())) {
            List<Map<String, Object>> minNotNull = lines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("min"))).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(minNotNull)) {
                double min = minNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("min")))).min().orElse(0);
                return formatDoubleFixNum(min, acc);
            }
        } else if ("最大值".equals(tab.getStatItem())) {
            List<Map<String, Object>> maxNotNull = lines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("max"))).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(maxNotNull)) {
                double max = maxNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("max")))).max().orElse(0);
                return formatDoubleFixNum(max, acc);
            }
        } else if ("平均值".equals(tab.getStatItem())) {
            List<Map<String, Object>> avgNotNull = lines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("avg"))).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(avgNotNull)) {
                double avg = avgNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("avg")))).average().orElse(0);
                return formatDoubleFixNum(avg, acc);
            }
        } else if ("相对变化值".equals(tab.getStatItem())) {
            String referStatItem = tab.getReferStatItem();
            /* 获取基准值统计方式指定的数据 */
            String stat = Objects.equals("最大值", referStatItem) ? "max" :
                    Objects.equals("最小值", referStatItem) ? "min" :
                            Objects.equals("平均值", referStatItem) ? "avg" : null;
            if (stat != null) {
                List<Map<String, Object>> referNotNull = lines.stream().filter(x -> !ObjectUtils.isEmpty(x.get(stat))).collect(Collectors.toList());
                if (!ObjectUtils.isEmpty(referNotNull)) {
                    double referValue = referNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get(stat)))).average().orElse(0);
                    /* 获取基准值参数值 */
                    Integer pointId = !points.isEmpty() ? points.get(0).getId() : null;
                    Integer paramId = tab.getReferParamId();
                    Double referParamValue = getReferParamValue(instId, paramId, pointId, getDirect(tab.getDirect(), points));
                    if (referParamValue != null) {
                        double rvv = referValue - referParamValue;
                        return formatDoubleFixNum(rvv, acc);
                    }
                }
            }
        }
        return NO_RES;
    }

    public Double getReferParamValue(Integer instId, Integer paramId, Integer pointId, String direct) {
        Double referParamValue = null;
        if (instId == null || paramId == null || pointId == null) {
            return referParamValue;
        }
        List<ParameterCommon> params = monitorService.getParamsProByInstId(instId);
        if (params == null || params.isEmpty()) {
            return referParamValue;
        }
        ParameterCommon param = params.stream().filter(p -> Objects.equals(paramId, p.getId())).findFirst().orElse(null);
        if (param == null) {
            return referParamValue;
        }
        /* 获取分量基准值 */
        List<PointParamCommon> pointParams = monitorService.getMeasurePointParamByParamId(param.getId());
        if (pointParams == null || pointParams.isEmpty()) {
            return referParamValue;
        }
        PointParamCommon pointParamCommon = pointParams.stream()
                .filter(pp -> Objects.equals(pp.getMeasurePointId(), pointId)
                        && Objects.equals(direct, String.valueOf(pp.getDirection())))
                .findFirst().orElse(null);
        if (pointParamCommon == null) {
            return referParamValue;
        }
        String paramValue = pointParamCommon.getParamValue();
        if (paramValue == null) {
            return referParamValue;
        }
        try {
            referParamValue = Double.parseDouble(paramValue);
        } catch (Exception e) {
            log.error("获取仪器【{}】采集点【{}】分量【{}】基准值【{}】转换异常：{}", instId, pointId, direct, paramValue, e.getMessage());
        }

        return referParamValue;
    }

    /**
     * 获取表格相关测点
     */
    private List<PointCommon> getTabRelatedPointIds(String seaId, Integer instId, List<Integer> pointIds, String customVar, List<PointCommon> fieldPoints, String fieldNum) {
        if (!ObjectUtils.isEmpty(pointIds)) {
            return fieldPoints.stream().filter(p -> pointIds.contains(p.getId())).collect(Collectors.toList());
        }
        if (!ObjectUtils.isEmpty(seaId) && ObjectUtils.isEmpty(customVar)) {
            return fieldPoints.stream().filter(p -> instId.equals(p.getInstId()) && seaId.equals(p.getSeaFacilityId())).collect(Collectors.toList());
        }
        if (!ObjectUtils.isEmpty(seaId)) {
            List<String> ids = reportVarService.getPointIdsByVarNameAndSea(fieldNum, customVar, seaId);
            return fieldPoints.stream().filter(e -> ids.contains(String.valueOf(e.getId()))).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

//    private void filterFieldPointsByCustomVar(String fieldNum, String customVar, String seaId, List<PointCommon> fieldPoints) {
//        if (ObjectUtils.isEmpty(fieldPoints)) {
//            return;
//        }
//        List<String> pointIds = reportVarService.getPointIdsByVarNameAndSea(fieldNum, customVar, seaId);
//        Iterator<PointCommon> it = fieldPoints.iterator();
//        while (it.hasNext()) {
//            PointCommon point = it.next();
//            if (!pointIds.contains(String.valueOf(point.getId()))) {
//                it.remove();
//            }
//        }
//    }

    /**
     * 获取柱状统计图相关测点
     */
    private List<PointCommon> getColRelatedPointIds(String seaId, Integer instId, Integer pointId, List<PointCommon> fieldPoints) {
        if (!ObjectUtils.isEmpty(pointId)) {
            return fieldPoints.stream().filter(p -> pointId.equals(p.getId())).collect(Collectors.toList());
        }
        if (!ObjectUtils.isEmpty(seaId)) {
            return fieldPoints.stream().filter(p -> instId.equals(p.getInstId()) && seaId.equals(p.getSeaFacilityId())).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

//    /**
//     * 获取过程线数据
//     */
//    private String getProcessLineData(String var, List<SeaFacilityCommon> fieldSeaFacility, List<PointCommon> fieldPoints, Map<Integer, AttrCommon> fieldAttrMap, String startTime, String endTime, String seaId) throws Exception {
//        ProcessLineJsonQo line = JSON.parseObject(var, ProcessLineJsonQo.class);
//        AttrCommon leftAttr = fieldAttrMap.get(line.getLeftAttrId());
//        if (ObjectUtils.isEmpty(leftAttr)) {
//            return NO_RES;
//        }
//        AttrCommon rightAttr = null;
//        if (!ObjectUtils.isEmpty(line.getRightAttrId())) {
//            rightAttr = fieldAttrMap.get(line.getRightAttrId());
//            if (ObjectUtils.isEmpty(rightAttr)) {
//                return NO_RES;
//            }
//        }
//
//        /* 获取相关测点 */
//        List<String> seaIds = getSeaFacilityIds(line.getSeaType(), fieldSeaFacility, seaId);
//        List<PointCommon> points = getRelatedPointIds(seaIds, line.getInstId(), fieldPoints);
//        return handleProcessLine(points, line, leftAttr, rightAttr, startTime, endTime);
//    }

    private static String serializeSeries(List<Series> series, Boolean isMark, boolean isMultiAxis, String timePattern) {
        String[] colors = {"#3070b7", "#c95c2e", "#00ff00", "#9900ff", "#ff00ff", "#0865f7"};
        String[] shapes = {"circle", "rect", "roundRect", "triangle", "diamond", "arrow"};
        if (ObjectUtils.isEmpty(series)) {
            return null;
        }
        List<JSONObject> ss = new ArrayList<>();
        AtomicInteger colorIndex = new AtomicInteger();
        series.forEach(s -> {
            Map<Integer, List<Datum>> dataMap = new HashMap<>();
            if (s.getData() != null) {
                dataMap.put(0, s.getData());
            }
            if (s.getData2() != null) {
                dataMap.put(1, s.getData2());
            }
            String seriesType = s.getType();
            String seriesName = s.getName();
            dataMap.forEach((k, ds) -> {
                String attrNameWithoutUnit = k == 0 ? s.getLeftAttrNameWithoutUnit() : s.getRightAttrNameWithoutUnit();
                JSONObject ser = new JSONObject();
                ser.put("type", seriesType);
                ser.put("yAxisIndex", "0");
                String name = isMultiAxis ? seriesName : seriesName + "-" + attrNameWithoutUnit;
                ser.put("name", name);
                String symbol = shapes[colorIndex.get() % shapes.length];
                ser.put("symbol", symbol);
                JSONObject lineStyle = new JSONObject();
                lineStyle.put("color", colors[colorIndex.get() % colors.length]);
                lineStyle.put("width", 1);
                ser.put("lineStyle", lineStyle);

                Map<Long, Float> markTimeMap = getMarkTimes(ds);
                List<JSONObject> data = new ArrayList<>();
                ser.put("data", data);
                ds.forEach(item -> {
                    if (timePattern == null && isMark != null && isMark) {
                        Float vf = markTimeMap.get(item.getTime().getTime());
                        if (vf != null) {
                            JSONObject d = new JSONObject();
                            d.put("value", new Object[]{item.getTime().getTime(), vf});
                            d.put("symbol", symbol);
                            d.put("symbolSize", 6);
                            data.add(d);
                        }
                    } else {
                        JSONObject d = new JSONObject();
                        Object valueTime = timePattern == null ? item.getTime().getTime() : TimeUtil.format(item.getTime(), timePattern);
                        d.put("value", new Object[]{valueTime, item.getAttrVal()});
                        d.put("symbol", symbol);
                        data.add(d);
                    }
                });
                colorIndex.getAndIncrement();
                ss.add(ser);
            });
        });
        return JSONObject.toJSONString(ss);
    }

    public List<PointCommon> getPoints(List<PointCommon> fieldPoints, List<Integer> pointIds) {
        List<PointCommon> list = new ArrayList<>();
        for (PointCommon point : fieldPoints) {
            if (pointIds.contains(point.getId())) {
                list.add(point);
            }
        }
        return list;
    }

    private void fillPointIds(ProcessLineJsonQo line, List<PointCommon> fieldPoints, String seaId) {
        if (!ObjectUtils.isEmpty(line.getPointIds())) {
            return;
        }
        if (seaId == null) {
            line.setPointIds(Collections.emptyList());
            return;
        }

        List<Integer> pointIds = fieldPoints.stream().filter(e -> line.getInstId().equals(e.getInstId()) && seaId.equals(e.getSeaFacilityId())).map(PointCommon::getId).collect(Collectors.toList());
        line.setPointIds(pointIds);
    }

    /**
     * 获取柱状统计图数据
     */
    private String getColumnarData(String var, List<SeaFacilityCommon> fieldSeaFacility, List<PointCommon> fieldPoints, Map<Integer, AttrCommon> fieldAttrMap, String startTime, String endTime, String seaId) {
        ColumnarJsonQo columnar = JSON.parseObject(var, ColumnarJsonQo.class);
        AttrCommon attr = fieldAttrMap.get(columnar.getAttrId());
        if (ObjectUtils.isEmpty(attr)) {
            return NO_RES;
        }

        List<String> seaIds = getColSeaFacilityIds(columnar.getSeaType(), fieldSeaFacility, seaId);
        List<PointCommon> relatedPoints = getRelatedPointIds(seaIds, attr.getInstId(), fieldPoints);
        Map<String, List<PointCommon>> seaPointsMap = relatedPoints.stream().collect(groupingBy(PointCommon::getSeaFacilityId));
        if (columnar.getSeaType().contains("测点")) {
            seaPointsMap = relatedPoints.stream().collect(groupingBy(PointCommon::getNo));
        }

        /* 查询涉及到的所有数据 */
        List<ColumnarVo> vos = new ArrayList<>();
        seaPointsMap.forEach((sea_id, points) -> {
            ColumnarVo vo;
            if (columnar.getSeaType().contains("测点")) {
                vo = new ColumnarVo(sea_id);
            } else {
                vo = new ColumnarVo(getSeaNameById(sea_id, fieldSeaFacility));
            }
            vos.add(vo);

            // 左边数据
            String leftDirect = getDirect(columnar.getLeftDirect(), points);
            List<Map<String, Object>> leftLines = tdService.getTogetherData(attr.getInstId(), attr.getRate(), points, attr.getId(), leftDirect, startTime, endTime, null);

            if (!ObjectUtils.isEmpty(leftLines)) {
                if ("最小值".equals(columnar.getLeftStatItem())) {
                    List<Map<String, Object>> minNotNull = leftLines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("min"))).collect(Collectors.toList());
                    if (!ObjectUtils.isEmpty(minNotNull)) {
                        double min = minNotNull.stream().mapToDouble(e -> Float.parseFloat(String.valueOf(e.get("min")))).min().orElse(0);
                        vo.setA(Float.parseFloat(String.valueOf(min)));
                    }
                } else if ("最大值".equals(columnar.getLeftStatItem())) {
                    List<Map<String, Object>> maxNotNull = leftLines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("max"))).collect(Collectors.toList());
                    if (!ObjectUtils.isEmpty(maxNotNull)) {
                        double max = maxNotNull.stream().mapToDouble(e -> Float.parseFloat(String.valueOf(e.get("max")))).max().orElse(0);
                        vo.setA(Float.parseFloat(String.valueOf(max)));
                    }
                } else if ("平均值".equals(columnar.getLeftStatItem())) {
                    List<Map<String, Object>> avgNotNull = leftLines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("avg"))).collect(Collectors.toList());
                    if (!ObjectUtils.isEmpty(avgNotNull)) {
                        double avg = avgNotNull.stream().mapToDouble(e -> Float.parseFloat(String.valueOf(e.get("avg")))).average().orElse(0);
                        vo.setA(Float.parseFloat(String.valueOf(avg)));
                    }
                }
            }

            // 右边数据
            String rightDirect = getDirect(columnar.getRightDirect(), points);
            List<Map<String, Object>> rightLines = tdService.getTogetherData(attr.getInstId(), attr.getRate(), points, attr.getId(), rightDirect, startTime, endTime, null);

            if (!ObjectUtils.isEmpty(rightLines)) {
                if ("最小值".equals(columnar.getRightStatItem())) {
                    List<Map<String, Object>> minNotNull = rightLines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("min"))).collect(Collectors.toList());
                    if (!ObjectUtils.isEmpty(minNotNull)) {
                        double min = minNotNull.stream().mapToDouble(e -> Float.parseFloat(String.valueOf(e.get("min")))).min().orElse(0);
                        vo.setB(Float.parseFloat(String.valueOf(min)));
                    }
                } else if ("最大值".equals(columnar.getRightStatItem())) {
                    List<Map<String, Object>> maxNotNull = rightLines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("max"))).collect(Collectors.toList());
                    if (!ObjectUtils.isEmpty(maxNotNull)) {
                        double max = maxNotNull.stream().mapToDouble(e -> Float.parseFloat(String.valueOf(e.get("max")))).max().orElse(0);
                        vo.setB(Float.parseFloat(String.valueOf(max)));
                    }
                } else if ("平均值".equals(columnar.getRightStatItem())) {
                    List<Map<String, Object>> avgNotNull = rightLines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("avg"))).collect(Collectors.toList());
                    if (!ObjectUtils.isEmpty(avgNotNull)) {
                        double avg = avgNotNull.stream().mapToDouble(e -> Float.parseFloat(String.valueOf(e.get("avg")))).average().orElse(0);
                        vo.setB(Float.parseFloat(String.valueOf(avg)));
                    }
                }
            }
        });

        if (ObjectUtils.isEmpty(vos)) {
            return "=====";
        }

        System.out.println("输出结果:");
        for (ColumnarVo vo : vos) {
            System.out.println(vo);
        }

        StringBuilder sb = new StringBuilder();
        for (ColumnarVo vo : vos) {
            sb.append("{\n" +
                    "      \"l\": " + formatDoubleFixNum(vo.getA(), 2) + ",\n" +
                    "      \"r\": " + formatDoubleFixNum(vo.getB(), 2) + ",\n" +
                    "      \"label\": \"" + vo.getSeaName() + "\"\n" +
                    "    },");
        }
        if (sb.charAt(sb.length() - 1) == ',') {
            sb.deleteCharAt(sb.length() - 1);
        }

        String json = null;
        try {
            json = CommonUtil.readResources("data/columnar.json");
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (json == null) {
            return null;
        }
        String uuid = CommonUtil.uuid();
        String outPngPath = "/data/highcharts-export/" + uuid + ".png";
        json = json.replace("outputPath", outPngPath);
        json = json.replace("attrName", attr.getName());

        json = json.replace("nameA", columnar.getLeftDirect() + " " + columnar.getLeftStatItem());
        json = json.replace("nameB", columnar.getRightDirect() + " " + columnar.getRightStatItem());
        int height;
        if (vos.size() <= 8) {
            height = 230;
        } else {
            height = 230 + (vos.size() - 8) * 16;
        }

        json = json.replace("customHeight", String.valueOf(height));
        json = json.replace("element", sb.toString());

        String jsonPath = "/data/highcharts-export/" + uuid + ".json";
        try {
            FileUtil.writeFile(jsonPath, json);
        } catch (Exception e) {
            e.printStackTrace();
        }
        executeCMD("node /home/<USER>/ec-expoort/columnar.js --input " + jsonPath);
        return outPngPath;
    }

    public List<PngInfo> getChartInfos(List<PointCommon> points, ProcessLineJsonQo line, AttrCommon leftAttr, AttrCommon rightAttr, String startTime, String endTime, Map<Integer, InstCommon> fieldInstMap) {
        List<PngInfo> chartInfos = new ArrayList<>();
        for (PointCommon point : points) {
            int[] directs = new int[]{0, 1, 2, 3};
            for (int direct : directs) {
                List<Datum> leftDatumList = null;
                List<Datum> rightDatumList = null;
                String leftAttrName = null;
                String rightAttrName = null;
                String leftAttrNameWithoutUnit = null;
                String rightAttrNameWithoutUnit = null;
                if (leftAttr != null && transDirectionNum(line.getLeftDirects(), leftAttr.getInstDirect(), leftAttr.getIsPolar()).contains(direct)) {
                    leftDatumList = selectPointData(leftAttr.getRate(), line.getInstId(), startTime.substring(0, 10), endTime.substring(0, 10), leftAttr.getId(), point.getId(), direct, 1);
                    leftAttrName = ObjectUtils.isEmpty(line.getLeftAttrAlias()) ? leftAttr.getName() : line.getLeftAttrAlias();
                    leftAttrNameWithoutUnit = leftAttrName;
                    if (!ObjectUtils.isEmpty(leftAttr.getUnit())) {
                        leftAttrName += "(" + leftAttr.getUnit() + ")";
                    }
                }
                if (rightAttr != null && transDirectionNum(line.getRightDirects(), rightAttr.getInstDirect(), rightAttr.getIsPolar()).contains(direct)) {
                    rightDatumList = selectPointData(rightAttr.getRate(), line.getInstId(), startTime.substring(0, 10), endTime.substring(0, 10), rightAttr.getId(), point.getId(), direct, 1);
                    rightAttrName = ObjectUtils.isEmpty(line.getRightAttrAlias()) ? rightAttr.getName() : line.getRightAttrAlias();
                    rightAttrNameWithoutUnit = rightAttrName;
                    if (!ObjectUtils.isEmpty(rightAttr.getUnit())) {
                        rightAttrName += "(" + rightAttr.getUnit() + ")";
                    }
                }
                if (leftDatumList != null || rightDatumList != null) {
                    boolean isSingleDirect = fieldInstMap.get(point.getInstId()) != null && "单向".equals(fieldInstMap.get(point.getInstId()).getDirect());
                    PngInfo pngInfo = new PngInfo(point.getId(), point.getNo(), String.valueOf(direct), leftDatumList, rightDatumList, leftAttrName, rightAttrName, isSingleDirect, leftAttrNameWithoutUnit, rightAttrNameWithoutUnit);
                    /* 设置方向别名 */
                    pngInfo.setDirectAlias(transDirectionAlias(direct, point));
                    chartInfos.add(pngInfo);
                }
            }
        }
        return chartInfos;
    }

    /**
     * 将方向描述转换为方向数字
     *
     * @param directs     方向描述
     * @param instDirect  仪器方向
     * @param isPolarAttr 是否极坐标分量
     * @return {@link String }
     */
    private List<Integer> transDirectionNum(String[] directs, String instDirect, boolean isPolarAttr) {
        if (directs==null|| directs.length == 0) {
            return Collections.emptyList();
        }
        Set<Integer> ds = new HashSet<>();
        for (String direct : directs) {
            switch (direct) {
                case "X方向": {
                    ds.add(1);
                    break;
                }
                case "Y方向": {
                    ds.add(2);
                    break;
                }
                case "Z方向": {
                    ds.add(3);
                    break;
                }
                case "极坐标": {
                    ds.add(0);
                    break;
                }
                case "所有方向": {
                    if (isPolarAttr){
                        ds.add(0);
                    }
                    if (Objects.equals("单向", instDirect)) {
                        ds.add(1);
                    } else if (Objects.equals("双向", instDirect)) {
                        ds.add(1);
                        ds.add(2);
                    } else if (Objects.equals("三向", instDirect)) {
                        ds.add(1);
                        ds.add(2);
                        ds.add(3);
                    } else {
                        log.error("方向转换异常：方向描述{}-仪器方向{}-极坐标{}", direct, instDirect, isPolarAttr);
                    }
                }
                default: {
                    log.error("方向转换异常：不支持的方向描述-描述{}-仪器方向{}-极坐标{}", direct, instDirect, isPolarAttr);
                }
            }
        }
        return new ArrayList<>(ds).stream().sorted().collect(Collectors.toList());
    }

    /**
     * 将方向转为测点配置的方向别名
     * @param direct 方向
     * @param point 测点
     * @return {@link String }
     */
    private String transDirectionAlias(Integer direct, PointCommon point) {
        if (direct == null) {
            return null;
        }
        String alias = null;
        switch (direct) {
            /*  TODO 极坐标目前不支持设置别名，后续支持  */
            case 1: {
                alias = point.getXDirectionAlias();
                break;
            }
            case 2: {
                alias = point.getYDirectionAlias();
                break;
            }
            case 3: {
                alias = point.getZDirectionAlias();
                break;
            }
            default: {
                log.error("方向仅支持 X(1) Y(2) Z(3) ,测点{}-未知方向:{}", point.getNo(), direct);
            }
        }
        return alias;
    }

    /**
     * 是否存在改方向
     */
    private boolean hasThisDirect(String direct, String[] directs) {
        List<String> list = Arrays.asList(directs);
        if ("1".equals(direct)) {
            return list.contains("X方向");
        } else if ("2".equals(direct)) {
            return list.contains("Y方向");
        } else {
            return list.contains("Z方向");
        }
    }

    private String handleProcessLine(List<PointCommon> points, ProcessLineJsonQo line, AttrCommon leftAttr, AttrCommon rightAttr, String startTime, String endTime) throws Exception {
        String directs = getDirect(line.getDirect(), points);
        List<PngInfo> chartInfos = new ArrayList<>();
        for (String direct : directs.split(",")) {
            for (PointCommon point : points) {
                List<Datum> datumList = selectPointData(leftAttr.getRate(), line.getInstId(), startTime.substring(0, 10), endTime.substring(0, 10), leftAttr.getId(),
                        point.getId(), Integer.parseInt(direct), line.getNumPerHour());
                PngInfo pngInfo = new PngInfo(point.getId(), point.getNo(), direct, datumList, leftAttr.getName(), leftAttr.getName());
                chartInfos.add(pngInfo);
                if (!ObjectUtils.isEmpty(rightAttr)) {
                    List<Datum> datumList2 = selectPointData(rightAttr.getRate(), line.getInstId(), startTime.substring(0, 10), endTime.substring(0, 10), rightAttr.getId(),
                            point.getId(), Integer.parseInt(direct), line.getNumPerHour());
                    pngInfo.setChartAttrName2(rightAttr.getName());
                    pngInfo.setData2(datumList2);
                }
            }
        }

        String path = genChartByInfo(line, chartInfos, TimeUtil.parse2Second(startTime).getTime(), TimeUtil.parse2Second(endTime).getTime(), CommonUtil.uuid(), true);
        return path;
    }

    public static String sanitizeFilename(String name) {
        if (ObjectUtils.isEmpty(name)) {
            return "异常文件名";
        }
        name = name.replaceAll("[£]", "\u00A3");
        return name.replaceAll("[\\\\/:*?\"<>|]", "-");
    }

    public String genChartByInfo(ProcessLineJsonQo line, List<PngInfo> chartInfos, long sTime, long eTime, String chartDir, Boolean isMark) {
        StringBuilder sb = new StringBuilder();
        double[] wh = chartService.parseWH(line.getImgType());
        int count = 0;

        LinkedHashMap<Integer, List<PngInfo>> pointChartInfoMap = chartInfos.stream().collect(groupingBy(PngInfo::getPointId, LinkedHashMap::new, Collectors.toList()));
        List<Series> series = new ArrayList<>();
        for (Map.Entry<Integer, List<PngInfo>> entry : pointChartInfoMap.entrySet()) {
            List<PngInfo> infos = entry.getValue();
            for (PngInfo info : infos) {
                String name = null;
                /* 根据设置判断是否使用别名替换 */
                if (line.getUseDirectionAlias() != null && line.getUseDirectionAlias()) {
                    String directAlias = info.getDirectAlias();
                    if (StringUtils.isBlank(directAlias)){
                        log.error("测点{}设置使用{}方向别名,但未读取到设置的别名",info.getPointNo(),info.getDirect());
                    }else {
                        name = directAlias;
                    }
                }
                if (name == null){
                    if (info.getIsSingleDirect() != null && info.getIsSingleDirect()) {
                        name = info.getPointNo();
                    } else {
                        String directStr = MonitorUtil.transDirection(Integer.parseInt(info.getDirect()));
                        name = ObjectUtils.isEmpty(directStr) ? info.getPointNo() : info.getPointNo() + "-" + directStr;
                    }
                }

                series.add(new Series(name,
                        info.getMonitorItem(), info.getChartAttrName(), info.getChartAttrName2(),
                        sanitizeFilename(info.getFileAttrName()), sanitizeFilename(info.getFileAttrName2()), info.getData(), info.getType(), info.getData2(), info.getLeftAttrNameWithoutUnit(), info.getRightAttrNameWithoutUnit()));
            }
            count++;

            if (count == line.getPointNum()) {
                if (series.size() == 0) {
                    continue;
                }
                String picUrl = pngData(series, line, sTime, eTime, chartDir, isMark, wh);
                sb.append(picUrl + "&");
                series.clear();
                count = 0;
            }
        }
        if (series.size() > 0) {
            String picUrl = pngData(series, line, sTime, eTime, chartDir, isMark, wh);
            sb.append(picUrl + "&");
        }
        if (ObjectUtils.isEmpty(sb)) {
            return null;
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }

    /**
     * 生成图片
     */
    private String pngData(List<Series> series, ProcessLineJsonQo line, long sTime, long eTime, String chartDir, Boolean isMark, double[] wh) {
        if (ObjectUtils.isEmpty(series)) {
            return null;
        }
        File dir = new File("/data/highcharts-export/" + chartDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        String chartAttrName = series.stream().map(Series::getChartAttrName).filter(Objects::nonNull).findFirst().orElse(null);
        String chartAttrName2 = series.stream().map(Series::getChartAttrName2).filter(Objects::nonNull).findFirst().orElse(null);
        String lFileName = series.stream().map(Series::getFileAttrName).filter(Objects::nonNull).findFirst().orElse(null);
        String elementStr = serializeSeries(series, isMark, chartAttrName2);
        String pointNo = series.stream().map(Series::getName).collect(Collectors.joining(","));
        String json = null;
        try {
            json = CommonUtil.readResources("data/echarts-option-double.json");
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (json == null) {
            return null;
        }
        json = json.replace("customWidth", String.valueOf(wh[0]));
        json = json.replace("customHeight", String.valueOf(wh[1]));
        json = json.replace("millisStart", String.valueOf(sTime));
        json = json.replace("millisEnd", String.valueOf(eTime));
        if (ObjectUtils.isEmpty(line.getInterval())) {
            line.setInterval(5);
        }
        json = json.replace("intervalParam", String.valueOf(line.getInterval()));
        json = json.replace("limitLow", String.valueOf(line.getLimitLow()));
        json = json.replace("limitHigh", String.valueOf(line.getLimitHigh()));
        if (!ObjectUtils.isEmpty(chartAttrName2)) {
            json = json.replace("分量名1", chartAttrName);
            json = json.replace("分量名2", chartAttrName2);
        } else {
            json = json.replace("分量名1", chartAttrName);
            json = json.replace("分量名2", "");
        }

        json = json.replace("element", elementStr);
        if (isMark != null && isMark) {
            json = json.replace("legendFlag", "false");
        } else {
            json = json.replace("legendFlag", "true");
        }

        json = setYAxis(json, line, series);

        String uuid = CommonUtil.uuid();
//        String outPngPath = dir.getAbsolutePath() + File.separator + uuid + ".png";
        String outPngPath = (dir.getAbsolutePath() + File.separator + lFileName + "-" + pointNo + ".png").replace(" ", "");
        json = json.replace("outputPath", outPngPath);

//        String jsonPath = dir.getAbsolutePath() + File.separator + uuid + ".json";

        String jsonPath = (dir.getAbsolutePath() + File.separator + lFileName + "-" + pointNo + ".json").replace(" ", "");

        try {
            FileUtil.writeFile(jsonPath, json);
        } catch (Exception e) {
            e.printStackTrace();
        }
        executeCMD("node /home/<USER>/ec-expoort/index.js --input " + jsonPath);

        /* 删除非png文件 */
        for (File file : dir.listFiles()) {
            if (!file.getName().endsWith(".png")) {
                try {
                    String tarDir = file.getAbsolutePath().substring(33, 38);
                    String targetPath = "/data/history-json/" + tarDir;
                    File tFile = new File(targetPath);
                    if (!tFile.exists()) {
                        tFile.mkdirs();
                    }
                    Files.move(Paths.get(file.getAbsolutePath()), Paths.get(tFile.getAbsolutePath() + File.separator + file.getName()));
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return outPngPath;
    }

    /**
     * 设置Y轴坐标
     */
    private String setYAxis(String jsonStr, ProcessLineJsonQo line, List<Series> series) {
        String json = jsonStr;
        if (line.getLeftIsAuto() != null && line.getLeftIsAuto()) {
            /* 若勾选，看有没有填写上下限，填了就判断是否越限，越限就自动，没有越限就直接使用；没填就自动 */
            if (line.getLeftMin() != null && line.getLeftMax() != null) {
                if (isCrossBorder(series, line.getLeftMin(), line.getLeftMax())) {
                    // 越限
                    json = json.replace("isAuto1", "true");
                    json = json.replace("yMin1", "0");
                    json = json.replace("yMax1", "0");
                    json = json.replace("yInterval1", "0");
                } else {
                    // 没有越限
                    json = json.replace("isAuto1", "false");
                    json = json.replace("yMin1", String.valueOf(line.getLeftMin()));
                    json = json.replace("yMax1", String.valueOf(line.getLeftMax()));
                    json = json.replace("yInterval1", String.format("%.4f", (line.getLeftMax() - line.getLeftMin()) / 4));
                }
            } else {
                json = json.replace("isAuto1", "true");
                json = json.replace("yMin1", "0");
                json = json.replace("yMax1", "0");
                json = json.replace("yInterval1", "0");
            }
        } else {
            /* 若没有勾选，看有没有填写上下限，填了就直接使用；没填就自动 */
            if (line.getLeftMin() != null && line.getLeftMax() != null) {
                json = json.replace("isAuto1", "false");
                json = json.replace("yMin1", String.valueOf(line.getLeftMin()));
                json = json.replace("yMax1", String.valueOf(line.getLeftMax()));
                json = json.replace("yInterval1", String.format("%.4f", (line.getLeftMax() - line.getLeftMin()) / 4));
            } else {
                json = json.replace("isAuto1", "true");
                json = json.replace("yMin1", "0");
                json = json.replace("yMax1", "0");
                json = json.replace("yInterval1", "0");
            }
        }

        if (line.getRightIsAuto() != null && line.getRightIsAuto()) {
            /* 若勾选，看有没有填写上下限，填了就判断是否越限，越限就自动，没有越限就直接使用；没填就自动 */
            if (line.getRightMin() != null && line.getRightMax() != null) {
                if (isCrossBorder(series, line.getRightMin(), line.getRightMax())) {
                    // 越限
                    json = json.replace("isAuto2", "true");
                    json = json.replace("yMin2", "0");
                    json = json.replace("yMax2", "0");
                    json = json.replace("yInterval2", "0");
                } else {
                    // 没有越限
                    json = json.replace("isAuto2", "false");
                    json = json.replace("yMin2", String.valueOf(line.getRightMin()));
                    json = json.replace("yMax2", String.valueOf(line.getRightMax()));
                    json = json.replace("yInterval2", String.format("%.4f", (line.getRightMax() - line.getRightMin()) / 4));
                }
            } else {
                json = json.replace("isAuto2", "true");
                json = json.replace("yMin2", "0");
                json = json.replace("yMax2", "0");
                json = json.replace("yInterval2", "0");
            }
        } else {
            /* 若没有勾选，看有没有填写上下限，填了就直接使用；没填就自动 */
            if (line.getRightMin() != null && line.getRightMax() != null) {
                json = json.replace("isAuto2", "false");
                json = json.replace("yMin2", String.valueOf(line.getRightMin()));
                json = json.replace("yMax2", String.valueOf(line.getRightMax()));
                json = json.replace("yInterval2", String.format("%.4f", (line.getRightMax() - line.getRightMin()) / 4));
            } else {
                json = json.replace("isAuto2", "true");
                json = json.replace("yMin2", "0");
                json = json.replace("yMax2", "0");
                json = json.replace("yInterval2", "0");
            }
        }
        return json;
    }

    /**
     * 判断是否越限
     */
    private boolean isCrossBorder(List<Series> series, Double min, Double max) {
        if (ObjectUtils.isEmpty(series)) {
            return false;
        }

        for (Series s : series) {
            if (s.getData() == null) {
                continue;
            }
            for (Datum datum : s.getData()) {
                Float val = datum.getAttrVal();
                if (val == null) {
                    continue;
                }
                if (val < min || val > max) {
                    return true;
                }
            }
        }
        return false;
    }

    public static void executeCMD(String cmd) {
        System.out.println(cmd);
        Process process = null;
        int exitVal = 0;
        try {
            process = Runtime.getRuntime().exec(cmd);
            exitVal = process.waitFor();
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }

        if (exitVal != 0) {
            throw new RuntimeException("cmd任务执行失败:\n" + cmd);
        }
    }

    public String handleProcessLine2(List<PointCommon> points, ProcessLineJsonQo line, AttrCommon leftAttr, AttrCommon rightAttr, String startTime, String endTime, Map<Integer, InstCommon> fieldInstMap) throws Exception {
        if (ObjectUtils.isEmpty(points)) {
            return NO_RES;
        }
        List<PngInfo> chartInfos = getChartInfos(points, line, leftAttr, rightAttr, startTime, endTime, fieldInstMap);

        /* 剔除没有数据的过程线 */
        chartInfos = chartInfos.stream().filter(info -> (info.getData() != null && !info.getData().isEmpty())
                || (info.getData2() != null && !info.getData2().isEmpty())).collect(Collectors.toList());

        String path = genChartByInfo(line, chartInfos, TimeUtil.parse2Second(startTime).getTime(), TimeUtil.parse2Second(endTime).getTime(), CommonUtil.uuid(), line.getIsMark());
        System.out.println("path::" + path);
        return path;
    }

    private static Map<Long, Float> getMarkTimes(List<Datum> data) {
        if (ObjectUtils.isEmpty(data)) {
            return Collections.emptyMap();
        }

        Map<Long, Float> map = new HashMap<>();
        Long lastTime = data.get(0).getTime().getTime();
        for (Datum datum : data) {
            long time = datum.getTime().getTime();
            Float val = datum.getAttrVal();
            if (time - lastTime > 23 * 3600 * 1000) {
                map.put(time, val);
                lastTime = time;
            }
        }
        return map;
    }


    /**
     * 查询测点数据
     */
    public List<Datum> selectPointData(String rate, Integer instId, String startTime, String endTime,
                                       Integer attrId, Integer pointId, Integer directId, Integer numPerHour) {
//        String tabName = "inst_" + instId;
//        String colName = "c_" + attrId;
//
//        StringBuilder sql = new StringBuilder();
//        if (numPerHour == null) {
//            /* 所有数据 */
//            sql.append("select ts, point, direct, " + colName + " from " + tabName + " where ts between '" + startTime + " 00:00:00' and '" + endTime + " 23:59:59'");
//            sql.append(" and point = " + pointId);
//            sql.append(" and direct IN (" + direct + ")");
//            sql.append(" and approval IN (0, 1)");
//        } else {
//            /* 采样数据 */
//            int interval = 3600 / numPerHour;
//            sql.append("select first(ts) as ts, first(point) as point, first(direct) as direct, first(" + colName + ") as " + colName + " from " + tabName + " where ts between '" + startTime + " 00:00:00' and '" + endTime + " 23:59:59'");
//            sql.append(" and point = " + pointId);
//            sql.append(" and direct IN (" + direct + ")");
//            sql.append(" and approval IN (0, 1)");
//            sql.append(" INTERVAL(" + interval + "s)");
//        }
//
//        System.out.println(sql.toString());
//
//        List<Map<String, Object>> records = tdService.selectMulti(sql.toString());
//        if (records.size() == 0) {
//            return Collections.emptyList();
//        }
//
//        List<Datum> dataList = new ArrayList<>();
//        for (Map<String, Object> record : records) {
//            Date ts = (Date) record.get("ts");
//            Integer point = (Integer) record.get("point");
//            Integer d = (Integer) record.get("direct");
//            Float attrVal = (Float) record.get(colName);
//            if (attrVal != null) {
//                dataList.add(new Datum(point, d, ts, attrId, attrVal));
//            }
//        }
//        return dataList;


        String tabName = DataServiceUtil.buildTableName(instId, rate, pointId);
        String colName = tdService.enColName(attrId, directId);

        StringBuilder sql = new StringBuilder();
        if (numPerHour == null) {
            /* 所有数据 */
            sql.append("select ts, point, " + colName + " from " + tabName + " where ts between '" + startTime + " 00:00:00' and '" + endTime + " 23:59:59'");
            sql.append(" and point = " + pointId);
            sql.append(" and status_" + directId + " IN (0, 1)");
            sql.append(" and del = false");
        } else {
            /* 采样数据 */
            int interval = 3600 / numPerHour;
            sql.append("select first(ts) as ts, first(point) as point, first(" + colName + ") as " + colName + " from " + tabName + " where ts between '" + startTime + " 00:00:00' and '" + endTime + " 23:59:59'");
            sql.append(" and point = " + pointId);
            sql.append(" and status_" + directId + " IN (0, 1)");
            sql.append(" and del = false");
            sql.append(" INTERVAL(" + interval + "s)");
        }

        List<Map<String, Object>> records = tdBaseService.selectMulti(sql.toString(), rate);
        if (records.size() == 0) {
            return Collections.emptyList();
        }

        List<Datum> dataList = new ArrayList<>();
        for (Map<String, Object> record : records) {
            Date ts = (Date) record.get("ts");
            Integer point = (Integer) record.get("point");
            Float attrVal = (Float) record.get(colName);
            if (attrVal != null) {
                dataList.add(new Datum(point, directId, ts, attrId, attrVal));
            }
        }
        return dataList;
    }

    /**
     * 获取过程线数据
     */
    private String getProcessLineData2(String var, List<SeaFacilityCommon> fieldSeaFacility, List<PointCommon> fieldPoints, Map<Integer, AttrCommon> fieldAttrMap,
                                       String startTime, String endTime, String seaId, Map<Integer, InstCommon> fieldInstMap) throws Exception {
        ProcessLineJsonQo line = JSON.parseObject(var, ProcessLineJsonQo.class);
        fillPointIds(line, fieldPoints, seaId);
        AttrCommon leftAttr = fieldAttrMap.get(line.getLeftAttrId());
        AttrCommon rightAttr = fieldAttrMap.get(line.getRightAttrId());

        /* 获取相关测点 */
        List<PointCommon> points = getPoints(fieldPoints, line.getPointIds());
        /* 根据测点的名称对测点排序 */
        points.sort((o1, o2) -> CommonUtil.lastNumberComparator('-').compare(o1.getNo(),o2.getNo()));

        String result = NO_RES;
        if (Objects.equals("month", line.getType())) {
            if (ObjectUtils.isEmpty(leftAttr) && ObjectUtils.isEmpty(rightAttr)) {
                log.error("过程线配置错误,月过程线未配置左侧分量-{}",line);
                return NO_RES;
            }
            result = handleProcessLineMonth(points, line, leftAttr, rightAttr, startTime, endTime, fieldInstMap);
        } else {
            if (Objects.equals("multi", line.getAttrType())) {
                List<Integer> multiAttrs = line.getMultiAttrs();
                if (multiAttrs == null || multiAttrs.isEmpty()) {
                    log.error("过程线配置错误,多分量未配置分量-{}",line);
                    return NO_RES;
                }
                List<AttrCommon> attrs = line.getMultiAttrs().stream()
                        .map(fieldAttrMap::get).collect(Collectors.toList());
                result = handleProcessLineMulti(points, line, attrs, startTime, endTime, fieldInstMap);
            } else {
                if (ObjectUtils.isEmpty(leftAttr) && ObjectUtils.isEmpty(rightAttr)) {
                    log.error("过程线配置错误,未配置左侧分量-{}",line);
                    return NO_RES;
                }
                result = handleProcessLine2(points, line, leftAttr, rightAttr, startTime, endTime, fieldInstMap);
            }
        }
        return result;
    }

    public String handleProcessLineMonth(List<PointCommon> points, ProcessLineJsonQo line, AttrCommon leftAttr, AttrCommon rightAttr, String startTime, String endTime, Map<Integer, InstCommon> fieldInstMap) {
        if (ObjectUtils.isEmpty(points)) {
            return NO_RES;
        }

        Date endDate = TimeUtil.parse2Second(endTime);
        endDate = endDate == null ? new Date() : endDate;
        Date startDate = line.getStartDate();
        startDate = startDate != null ? startDate : DateUtils.addMonths(endDate, -1);
        List<String> xAxisData = TimeUtil.getMonthSegment(startDate, endDate).stream().map(pair -> TimeUtil.format(pair.getSecond(), "yy-MM")).collect(Collectors.toList());
        Collections.reverse(xAxisData);

        List<PngInfo> chartInfos = getMonthChartInfos(points, line, leftAttr, rightAttr, endDate, fieldInstMap);

        /* 剔除没有数据的过程线 */
        chartInfos = chartInfos.stream().filter(info -> (info.getData() != null && !info.getData().isEmpty()) || (info.getData2() != null && !info.getData2().isEmpty())).collect(Collectors.toList());

        /* 根据 pointId 分组数据 */
        LinkedHashMap<Integer, List<PngInfo>> pointChartMap = chartInfos.stream().collect(groupingBy(PngInfo::getPointId, LinkedHashMap::new, Collectors.toList()));

        StringBuilder sb = new StringBuilder();
        pointChartMap.forEach((key, value) -> {
            /* 生成 echart series */
            List<Series> series = generateMonthEchartsSeries(value);
            /* 生成 echart options */
            double[] wh = chartService.parseWH(line.getImgType());
            Boolean isMark = line.getIsMark();
            String options = generateMonthEchartsOptions(xAxisData, series, line, isMark, wh);
            String pointNo = series.stream().map(Series::getName).collect(Collectors.joining(","));
            String filename = series.stream().map(Series::getFileAttrName).filter(Objects::nonNull)
                    .findFirst().orElse(UUID.randomUUID().toString()) + "-" + pointNo;
            /* 渲染 echarts */
            String path = drawEchartsPng("history-eigenvalue.js", filename, options);
            sb.append(sb.length() > 1 ? "&" : "").append(path);
        });
        System.out.println("path::" + sb);
        return sb.toString();
    }

    public String handleProcessLineMulti(List<PointCommon> points, ProcessLineJsonQo line, List<AttrCommon> attrs,
                                            String startTime, String endTime, Map<Integer, InstCommon> fieldInstMap) {
        if (ObjectUtils.isEmpty(points)) {
            return NO_RES;
        }
        List<PngInfo> chartInfos = getMultiChartInfos(points, line, attrs, startTime, endTime, fieldInstMap);
        /* 剔除没有数据的过程线 */
        chartInfos = chartInfos.stream().filter(info -> (info.getData() != null && !info.getData().isEmpty())
                        || (info.getData2() != null && !info.getData2().isEmpty()))
                .collect(Collectors.toList());
        /* 根据 pointId 分组数据 */
        LinkedHashMap<Integer, List<PngInfo>> pointChartMap = chartInfos.stream()
                .collect(groupingBy(PngInfo::getPointId, LinkedHashMap::new, Collectors.toList()));

        StringBuilder sb = new StringBuilder();
        int pointNum = line.getPointNum() == null ? 1 : line.getPointNum();
        AtomicInteger pointIndex = new AtomicInteger(0);
        List<Series> seriesCollect = new ArrayList<>();
        pointChartMap.forEach((key, value) -> {
            /* 生成 echart series */
            List<Series> series = generateMultiEchartsSeries(value,line);
            seriesCollect.addAll(series);
            if (pointIndex.getAndIncrement() % pointNum == 0) {
                /* 生成 echart options */
                double[] wh = chartService.parseWH(line.getImgType());
                Boolean isMark = line.getIsMark();
                String options = generateMultiEchartsOptions(line, seriesCollect, TimeUtil.parse2Second(startTime).getTime(),
                        TimeUtil.parse2Second(endTime).getTime(), isMark, wh);
                String pointNo = seriesCollect.stream().map(Series::getName).collect(Collectors.joining(","));
                String filename = seriesCollect.stream().map(Series::getFileAttrName).findFirst().orElse(UUID.randomUUID().toString()) + "-" + pointNo;
                /* 渲染 echarts */
                String path = drawEchartsPng("index-multi.js", filename, options);
                sb.append(sb.length() > 1 ? "&" : "").append(path);
                seriesCollect.clear();
            }
        });
        System.out.println("path::" + sb);
        return sb.toString();
    }

    public List<PngInfo> getMultiChartInfos(List<PointCommon> points, ProcessLineJsonQo line, List<AttrCommon> attrs,
                                               String startTime, String endTime, Map<Integer, InstCommon> fieldInstMap) {
        List<PngInfo> chartInfos = new ArrayList<>();
        for (PointCommon point : points) {
            attrs.forEach(attr -> {
                if (attr == null || line.getLeftDirects() == null) {
                    log.error("多分量过程线不存在分量或方向-{}", line);
                    return;
                }
                for (String leftDirect : line.getLeftDirects()) {
                    String directs = getDirect(leftDirect, Collections.singletonList(point));
                    for (String direct : directs.split(",")) {
                        List<Datum> leftDatumList = selectPointData(attr.getRate(), line.getInstId(), startTime.substring(0, 10),
                                endTime.substring(0, 10), attr.getId(), point.getId(), Integer.parseInt(direct), 1);
                        String leftAttrName = attr.getName();
                        String leftAttrNameWithoutUnit = leftAttrName;
                        if (!ObjectUtils.isEmpty(attr.getUnit())) {
                            leftAttrName += "(" + attr.getUnit() + ")";
                        }
                        boolean isSingleDirect = fieldInstMap.get(point.getInstId()) != null && "单向".equals(fieldInstMap.get(point.getInstId()).getDirect());
                        PngInfo pngInfo = new PngInfo(point.getId(), point.getNo(), direct, leftDatumList, null, leftAttrName,
                                null, isSingleDirect, leftAttrNameWithoutUnit, null);
                        chartInfos.add(pngInfo);
                    }
                }
            });
        }
        return chartInfos;
    }

    public List<PngInfo> getMonthChartInfos(List<PointCommon> points, ProcessLineJsonQo line, AttrCommon leftAttr,
                                            AttrCommon rightAttr, Date endTime, Map<Integer, InstCommon> fieldInstMap) {
        Date startDate = line.getStartDate();
        startDate = startDate != null ? startDate : DateUtils.addMonths(endTime, -1);
        /* 根据开始时间判断是否需要自然月 */
        int dayOfMonth = startDate.toInstant().atZone(ZoneId.systemDefault()).getDayOfMonth();
        List<Pair<Date, Date>> segment = TimeUtil.getMonthSegment(startDate, endTime, dayOfMonth == 1);
        List<PngInfo> chartInfos = new ArrayList<>();
        Map<String, PngInfo> pngInfoMap = new LinkedHashMap<>();
        for (PointCommon point : points) {
            String leftAttrName = leftAttr == null ? null : ObjectUtils.isEmpty(line.getLeftAttrAlias()) ? leftAttr.getName() : line.getLeftAttrAlias();
            String rightAttrName = rightAttr == null ? null : ObjectUtils.isEmpty(line.getRightAttrAlias()) ? rightAttr.getName() : line.getRightAttrAlias();
            if (leftAttr != null) {
                List<Integer> leftDirects = transDirectionNum(line.getLeftDirects(), leftAttr.getInstDirect(), leftAttr.getIsPolar());
                for (int direct : leftDirects) {
                    List<String> leftAttrStatItems = line.getLeftAttrStatItems();
                    leftAttrStatItems = leftAttrStatItems == null || leftAttrStatItems.isEmpty() ? Collections.singletonList("平均值") : leftAttrStatItems;
                    for (String statType : leftAttrStatItems) {
                        List<Datum> leftDatumList = segment.stream().map(seg ->
                                selectSegmentPointData(statType, leftAttr.getRate(), line.getInstId(),
                                        TimeUtil.format2Second(seg.getFirst()), TimeUtil.format2Second(seg.getSecond()), leftAttr.getId(),
                                        point.getId(), direct)).filter(Objects::nonNull).collect(Collectors.toList());
                        String leftAttrNameWithoutUnit = leftAttrName;
                        if (!ObjectUtils.isEmpty(leftAttr.getUnit())) {
                            leftAttrName += "(" + leftAttr.getUnit() + ")";
                        }
                        /*  这里通过统计类型在判断是否要不把统计类型拼接在单位后面  */
                        leftAttrNameWithoutUnit = (line.getLeftAttrStatItems() == null || line.getLeftAttrStatItems().isEmpty()) ?
                                leftAttrNameWithoutUnit : leftAttrNameWithoutUnit + statType;

                        boolean isSingleDirect = fieldInstMap.get(point.getInstId()) != null && "单向".equals(fieldInstMap.get(point.getInstId()).getDirect());
                        PngInfo pngInfo = new PngInfo(point.getId(), point.getNo(), String.valueOf(direct), isSingleDirect);
                        pngInfo.setLeft(leftDatumList, leftAttrName, leftAttrNameWithoutUnit);
                        String key = point.getId() + "_" + direct + "_" + statType;
                        /*  加这个东西只是为了让后面的逻辑知道这是个双轴  */
                        pngInfo.setChartAttrName2(rightAttrName);
                        pngInfoMap.put(key, pngInfo);
                    }
                }
                if (rightAttr != null) {
                    List<Integer> rightDirects = transDirectionNum(line.getRightDirects(), rightAttr.getInstDirect(), rightAttr.getIsPolar());
                    for (int direct : rightDirects) {
                        List<String> rightAttrStatItems = line.getRightAttrStatItems();
                        rightAttrStatItems = rightAttrStatItems == null || rightAttrStatItems.isEmpty() ? Collections.singletonList("平均值") : rightAttrStatItems;
                        for (String statType : rightAttrStatItems) {
                            List<Datum> rightDatumList = segment.stream().map(seg ->
                                    selectSegmentPointData(statType, rightAttr.getRate(), line.getInstId(),
                                            TimeUtil.format2Second(seg.getFirst()), TimeUtil.format2Second(seg.getSecond()), rightAttr.getId(),
                                            point.getId(), direct)).filter(Objects::nonNull).collect(Collectors.toList());
                            String rightAttrNameWithoutUnit = rightAttrName;
                            if (!ObjectUtils.isEmpty(rightAttr.getUnit())) {
                                rightAttrName += "(" + rightAttr.getUnit() + ")";
                            }
                            /*  这里通过统计类型在判断是否要不把统计类型拼接在单位后面  */
                            rightAttrNameWithoutUnit = (line.getRightAttrStatItems() == null || line.getRightAttrStatItems().isEmpty()) ?
                                    rightAttrNameWithoutUnit : rightAttrNameWithoutUnit + statType;

                            String key = point.getId() + "_" + direct + "_" + statType;
                            PngInfo pngInfo = pngInfoMap.get(key);
                            if (pngInfo != null) {
                                pngInfo.setRight(rightDatumList, rightAttrName, rightAttrNameWithoutUnit);
                            } else {
                                log.error("月历史过程线-右侧选择的统计方式左侧未选择-{}", key);
                                boolean isSingleDirect = fieldInstMap.get(point.getInstId()) != null && "单向".equals(fieldInstMap.get(point.getInstId()).getDirect());
                                pngInfo = new PngInfo(point.getId(), point.getNo(), String.valueOf(direct), isSingleDirect);
                                pngInfo.setLeft(rightDatumList, rightAttrName, rightAttrNameWithoutUnit);
                                /*  加这个东西只是为了让后面的逻辑知道这是个双轴  */
                                pngInfo.setChartAttrName2(rightAttrName);
                                pngInfoMap.put(key, pngInfo);
                            }
                        }
                    }
                }
            }
        }
        /*  Map转未List  */
        pngInfoMap.forEach((k, v) -> {
            chartInfos.add(v);
        });
        return chartInfos;
    }

    public Datum selectSegmentPointData(String statType, String rate, Integer instId, String startTime, String endTime, Integer attrId, Integer pointId, Integer directId) {
        String tabName = DataServiceUtil.buildTableName(instId, rate, pointId);
        String colName = tdService.enColName(attrId, directId);
        /* 时间取时间段结束日期 */
        String pointTime = endTime.substring(0, 8) + "01 00:00:00";

        String colFunc = mappingStatFunction(colName,statType);

        /* 所有数据 */
        String sql = "select last(ts) as ts, first(point) as point, " + colFunc + " as " + colName + " from " + tabName + " where ts between '" + startTime + "' and '" + endTime + "'" + " and point = " + pointId + " and status_" + directId + " IN (0, 1)" + " and del = false";

        List<Map<String, Object>> records = tdBaseService.selectMulti(sql, rate);
        if (records.isEmpty()) {
            return null;
        } else {
            Map<String, Object> record = records.get(0);
            Date ts = TimeUtil.parse2Second(pointTime);
            Integer point = (Integer) record.get("point");
            Number attrVal = (Number) record.get(colName);
            if (attrVal != null) {
                return new Datum(point, directId, ts, attrId, attrVal.floatValue());
            } else {
                return null;
            }
        }
    }

    /**
     * 映射统计方式到SQL函数
     *
     * @param colName  列名
     * @param statType 统计方式
     * @return {@link String }
     */
    private String mappingStatFunction(String colName, String statType) {
        String functionName = "AVG(#{colName})";
        statType = statType == null || statType.trim().isEmpty() ? "平均值" : statType;
        switch (statType) {
            case "最大值": {
                functionName = "MAX(#{colName})";
                break;
            }
            case "最小值": {
                functionName = "MIN(#{colName})";
                break;
            }
            case "平均值": {
                functionName = "AVG(#{colName})";
                break;
            }
            default: {
                log.error("统计方式转SQL函数列-未支持统计方式[{}]-使用平均值", statType);
            }
        }
        return functionName.replace("#{colName}", colName);
    }

    public List<Series> generateMultiEchartsSeries(List<PngInfo> infos,ProcessLineJsonQo line) {
        List<Series> series = new ArrayList<>();
        for (PngInfo info : infos) {
            String name;
            if (info.getIsSingleDirect() != null && info.getIsSingleDirect()) {
                name = info.getPointNo();
            } else {
                name = info.getPointNo() + "-" + MonitorUtil.transDirection(Integer.parseInt(info.getDirect()));
            }
            series.add(new Series(name, info.getMonitorItem(), info.getChartAttrName(), info.getChartAttrName2(),
                    sanitizeFilename(info.getFileAttrName()), sanitizeFilename(info.getFileAttrName2()),
                    info.getData(), info.getType(), info.getData2(), info.getLeftAttrNameWithoutUnit(), info.getRightAttrNameWithoutUnit()));
        }

        return series;
    }

    public List<Series> generateMonthEchartsSeries(List<PngInfo> infos) {
        List<Series> series = new ArrayList<>();
        for (PngInfo info : infos) {
            String name;
            if (info.getIsSingleDirect() != null && info.getIsSingleDirect()) {
                name = info.getPointNo();
            } else {
                name = info.getPointNo() + "-" + MonitorUtil.transDirection(Integer.parseInt(info.getDirect()));
            }

            series.add(new Series(name, info.getMonitorItem(), info.getChartAttrName(), info.getChartAttrName2(),
                    sanitizeFilename(info.getFileAttrName()), sanitizeFilename(info.getFileAttrName2()),
                    info.getData(), info.getType(), info.getData2(), info.getLeftAttrNameWithoutUnit(), info.getRightAttrNameWithoutUnit()));
        }

        return series;
    }

    public String generateMonthEchartsOptions(List<String> xAxisData, List<Series> series, ProcessLineJsonQo line, Boolean isMark, double[] wh) {
        String chartAttrName = series.stream().map(Series::getChartAttrName).filter(Objects::nonNull).findFirst().orElse(null);
        String chartAttrName2 = series.stream().map(Series::getChartAttrName2).filter(Objects::nonNull).findFirst().orElse(null);

        String options = null;
        try {
            options = CommonUtil.readResources("data/echarts-option-month.json");
        } catch (IOException e) {
            log.error("读取echarts-export模版异常：{}", e.getMessage());
        }
        if (options == null) {
            return null;
        }
        options = options.replace("customWidth", String.valueOf(wh[0]));
        options = options.replace("customHeight", String.valueOf(wh[1]));
        options = options.replace("limitLow", String.valueOf(line.getLimitLow()));
        options = options.replace("limitHigh", String.valueOf(line.getLimitHigh()));

        options = options.replace("${xAxisData}", JSON.toJSONString(xAxisData));

        options = options.replace("分量名1", chartAttrName == null ? "" : chartAttrName);
        options = options.replace("分量名2", chartAttrName2 == null ? "" : chartAttrName2);

        String elementStr = serializeSeries(series, isMark, !ObjectUtils.isEmpty(chartAttrName2), "yy-MM");
        options = options.replace("element", elementStr == null ? "[]" : elementStr);

        if (isMark != null && isMark) {
            options = options.replace("legendFlag", "false");
        } else {
            options = options.replace("legendFlag", "true");
        }

        options = setYAxis(options, line, series);
        return options;
    }

    public String generateMultiEchartsOptions(ProcessLineJsonQo line, List<Series> series,
                                                 long sTime, long eTime, Boolean isMark, double[] wh) {
        String options = null;
        try {
            options = CommonUtil.readResources("data/echarts-option-double.json");
        } catch (IOException e) {
            log.error("读取echarts-export模版异常：{}", e.getMessage());
        }
        if (options == null) {
            return null;
        }
        options = options.replace("customWidth", String.valueOf(wh[0]));
        options = options.replace("customHeight", String.valueOf(wh[1]));

        if (isMark != null && isMark) {
            options = options.replace("legendFlag", "false");
        } else {
            options = options.replace("legendFlag", "true");
        }

        options = options.replace("limitLow", String.valueOf(line.getLimitLow()));
        options = options.replace("limitHigh", String.valueOf(line.getLimitHigh()));

        options = options.replace("millisStart", String.valueOf(sTime));
        options = options.replace("millisEnd", String.valueOf(eTime));

        if (ObjectUtils.isEmpty(line.getInterval())) {
            line.setInterval(5);
        }
        options = options.replace("intervalParam", String.valueOf(line.getInterval()));

        String attrAlias = line.getLeftAttrAlias();
        String chartAttrName = ObjectUtils.isEmpty(attrAlias) ?
                series.stream().map(Series::getChartAttrName).filter(Objects::nonNull).findFirst().orElse("") : attrAlias;
        options = setYAxis(options, line, series);
        options = options.replace("分量名1", chartAttrName);
        options = options.replace("分量名2", "");
        String elementStr = serializeSeries(series, isMark, "");
        options = options.replace("element", elementStr == null ? "[]" : elementStr);
        return options;
    }

    public String drawEchartsPng(String script, String filename, String options) {
        if (ObjectUtils.isEmpty(options)) {
            return null;
        }
        String dirStr = ECHARTS_EXPORT_FILE_DIR + CommonUtil.uuid();
        File dir = new File(dirStr);
        if (!(dir.exists() || dir.mkdirs())) {
            return null;
        }
        String outPngPath = (dir.getAbsolutePath() + File.separator + filename + ".png").replace(" ", "");
        options = options.replace("outputPath", outPngPath);

        String optionsPath = (dir.getAbsolutePath() + File.separator + filename + ".json").replace(" ", "");

        try {
            FileUtil.writeFile(optionsPath, options);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        executeCMD("node " + ECHARTS_EXPORT_COMMAND_DIR + script + " --input " + optionsPath);

        return outPngPath;
    }

    /**
     * 获取分量统计值数据
     */
    private String getAttrStatData(String var, List<SeaFacilityCommon> fieldSeaFacility, List<PointCommon> fieldPoints, Map<Integer, AttrCommon> fieldAttrMap, String startTime, String endTime, String seaId, String fieldNum) throws Exception {
        AttrStatJsonQo attrStat = JSON.parseObject(var, AttrStatJsonQo.class);
        log.info("分量统计值：" + attrStat.toString());
        AttrCommon attr = fieldAttrMap.get(attrStat.getAttrId());
        if (ObjectUtils.isEmpty(attr)) {
            return "未知分量，请反馈管理员";
        }

        List<String> seaIds = getSeaFacilityIds(attrStat.getSeaType(), fieldSeaFacility, seaId, fieldNum);
        log.info("seaIds:{}", seaIds.toString());

        if (attrStat.getPointId() == null) {
            /* 选择的为风机设施时 */
            return handleAttrStatVar(seaIds, getDirect(attrStat.getDirect(), null), attrStat.getStatItem(), fieldPoints, attr, startTime, endTime);
        } else {
            /* 选择的为测点时 */
            List<PointCommon> points = fieldPoints.stream().filter(p -> Objects.equals(p.getId(), attrStat.getPointId())).collect(Collectors.toList());
            return points.isEmpty() ? NO_RES : handleAttrStatVarWithPoint(points.get(0), getDirect(attrStat.getDirect(), points), attrStat.getStatItem(), attr, startTime, endTime);
        }
    }

    /**
     * 处理分量统计值
     */
    private String handleAttrStatVar(List<String> seaIds, String direct, String statItem, List<PointCommon> fieldPoints, AttrCommon attr,
                                     String startTime, String endTime) {
        /* 查询涉及到的所有测点并按仪器分组 */
        List<PointCommon> relatedPoints = getRelatedPointIds(seaIds, null, fieldPoints);
        Map<Integer, List<PointCommon>> instPointsMap = relatedPoints.stream().collect(groupingBy(PointCommon::getInstId));

        /* 查询涉及到的所有数据 */
        List<Map<String, Object>> totalLines = new ArrayList<>();
        instPointsMap.forEach((instId, points) -> {
            List<Map<String, Object>> lines = tdService.getTogetherData(instId, attr.getRate(), points, attr.getId(), direct, startTime, endTime, statItem);
            totalLines.addAll(lines);
        });

        if (totalLines.isEmpty()) {
            return NO_RES + attr.getUnit();
        }
        List<Map<String, Object>> minNotNull = totalLines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("min"))).collect(Collectors.toList());
        List<Map<String, Object>> maxNotNull = totalLines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("max"))).collect(Collectors.toList());
        List<Map<String, Object>> avgNotNull = totalLines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("avg"))).collect(Collectors.toList());
        int acc = getAcc(attr.getDecimalAccuracy());
        if ("最小值".equals(statItem)) {
            if (!ObjectUtils.isEmpty(minNotNull)) {
                double min = minNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("min")))).min().orElse(0);
                return formatDoubleFixNum(min, acc) + attr.getUnit();
            }
        } else if ("最大值".equals(statItem)) {
            if (!ObjectUtils.isEmpty(maxNotNull)) {
                double max = maxNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("max")))).max().orElse(0);
                return formatDoubleFixNum(max, acc) + attr.getUnit();
            }
        } else if ("平均值".equals(statItem)) {
            if (!ObjectUtils.isEmpty(avgNotNull)) {
                double avg = avgNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("avg")))).average().orElse(0);
                return formatDoubleFixNum(avg, acc) + attr.getUnit();
            }
        } else if ("最小值~最大值".equals(statItem)) {
            if (!ObjectUtils.isEmpty(minNotNull) && !ObjectUtils.isEmpty(maxNotNull)) {
                double min = minNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("min")))).min().orElse(0);
                double max = maxNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("max")))).max().orElse(0);
                return formatDoubleFixNum(min, acc) + attr.getUnit() + "~" + formatDoubleFixNum(max, acc) + attr.getUnit();
            }
        } else if ("变幅".equals(statItem)) {
            if (!ObjectUtils.isEmpty(minNotNull) && !ObjectUtils.isEmpty(maxNotNull)) {
                double min = minNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("min")))).min().orElse(0);
                double max = maxNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("max")))).max().orElse(0);
                return formatDoubleFixNum(max - min, acc) + attr.getUnit();
            }
        } else if (statItem.contains("各测点最小值的")) {
            if (!ObjectUtils.isEmpty(minNotNull)) {
                double min = minNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("min")))).min().orElse(0);
                double max = minNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("min")))).max().orElse(0);
                return formatDoubleFixNum(min, acc) + attr.getUnit() + "~" + formatDoubleFixNum(max, acc) + attr.getUnit();
            }
        } else if (statItem.contains("各测点最大值的")) {
            if (!ObjectUtils.isEmpty(maxNotNull)) {
                double min = maxNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("max")))).min().orElse(0);
                double max = maxNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("max")))).max().orElse(0);
                return formatDoubleFixNum(min, acc) + attr.getUnit() + "~" + formatDoubleFixNum(max, acc) + attr.getUnit();
            }
        } else if (statItem.equals("各测点平均值的最大值")) {
            if (!ObjectUtils.isEmpty(avgNotNull)) {
                double max = avgNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("avg")))).max().orElse(0);
                return formatDoubleFixNum(max, acc) + attr.getUnit();
            }
        } else if (statItem.contains("各测点平均值的")) {
            if (!ObjectUtils.isEmpty(avgNotNull)) {
                double min = avgNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("avg")))).min().orElse(0);
                double max = avgNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("avg")))).max().orElse(0);
                return formatDoubleFixNum(min, acc) + attr.getUnit() + "~" + formatDoubleFixNum(max, acc) + attr.getUnit();
            }
        } else if (statItem.contains("各海上设施最小值的")) {
            Map<String, List<PointCommon>> seaPoints = relatedPoints.stream().collect(groupingBy(PointCommon::getSeaFacilityId));
            List<Double> minList = new ArrayList<>();
            for (Map.Entry<String, List<PointCommon>> en : seaPoints.entrySet()) {
                Set<Integer> pointIds = en.getValue().stream().map(PointCommon::getId).collect(Collectors.toSet());
                List<Map<String, Object>> seaLines = totalLines.stream().filter(e -> pointIds.contains((Integer) e.get("point"))).collect(Collectors.toList());
                seaLines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("min")))
                        .map(x -> Double.parseDouble(String.valueOf(x.get("min"))))
                        .min(Comparator.comparing(x -> x))
                        .ifPresent(minList::add);
            }
            Double min = minList.stream().min(Comparator.comparing(x -> x)).orElse(null);
            Double max = minList.stream().max(Comparator.comparing(x -> x)).orElse(null);
            if (min != null) {
                return formatDoubleFixNum(min, acc) + attr.getUnit() + "~" + formatDoubleFixNum(max, acc) + attr.getUnit();
            }
        } else if (statItem.contains("各海上设施最大值的")) {
            Map<String, List<PointCommon>> seaPoints = relatedPoints.stream().collect(groupingBy(PointCommon::getSeaFacilityId));
            List<Double> maxList = new ArrayList<>();
            for (Map.Entry<String, List<PointCommon>> en : seaPoints.entrySet()) {
                Set<Integer> pointIds = en.getValue().stream().map(PointCommon::getId).collect(Collectors.toSet());
                List<Map<String, Object>> seaLines = totalLines.stream().filter(e -> pointIds.contains((Integer) e.get("point"))).collect(Collectors.toList());
                seaLines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("max")))
                        .map(x -> Double.parseDouble(String.valueOf(x.get("max"))))
                        .max(Comparator.comparing(x -> x))
                        .ifPresent(maxList::add);
            }
            Double min = maxList.stream().min(Comparator.comparing(x -> x)).orElse(null);
            Double max = maxList.stream().max(Comparator.comparing(x -> x)).orElse(null);
            if (min != null) {
                return formatDoubleFixNum(min, acc) + attr.getUnit() + "~" + formatDoubleFixNum(max, acc) + attr.getUnit();
            }
        } else if (statItem.contains("各海上设施平均值的")) {
            Map<String, List<PointCommon>> seaPoints = relatedPoints.stream().collect(groupingBy(PointCommon::getSeaFacilityId));
            List<Double> avgList = new ArrayList<>();
            for (Map.Entry<String, List<PointCommon>> en : seaPoints.entrySet()) {
                Set<Integer> pointIds = en.getValue().stream().map(PointCommon::getId).collect(Collectors.toSet());
                List<Map<String, Object>> seaLines = totalLines.stream().filter(e -> pointIds.contains((Integer) e.get("point"))).collect(Collectors.toList());
                Double seaAvgVal = calcAvgVal(seaLines);
                if (seaAvgVal != null) {
                    avgList.add(seaAvgVal);
                }
            }
            Double min = avgList.stream().min(Comparator.comparing(x -> x)).orElse(null);
            Double max = avgList.stream().max(Comparator.comparing(x -> x)).orElse(null);
            if (min != null) {
                return formatDoubleFixNum(min, acc) + attr.getUnit() + "~" + formatDoubleFixNum(max, acc) + attr.getUnit();
            }
        }
        return NO_RES + attr.getUnit();
    }

    /**
     * 处理分量统计值
     */
    private String handleAttrStatVarWithPoint(PointCommon point, String direct, String statItem, AttrCommon attr, String startTime, String endTime) {
        Integer instId = point.getInstId();
        List<PointCommon> points = Collections.singletonList(point);
        List<Map<String, Object>> lines = tdService.getTogetherData(instId, attr.getRate(), points, attr.getId(), direct, startTime, endTime, statItem);
        int acc = getAcc(attr.getDecimalAccuracy());
        if ("最小值".equals(statItem)) {
            List<Map<String, Object>> minNotNull = lines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("min"))).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(minNotNull)) {
                double min = minNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("min")))).min().orElse(0);
                return formatDoubleFixNum(min, acc) + attr.getUnit();
            }
        } else if ("最大值".equals(statItem)) {
            List<Map<String, Object>> maxNotNull = lines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("max"))).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(maxNotNull)) {
                double max = maxNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("max")))).max().orElse(0);
                return formatDoubleFixNum(max, acc) + attr.getUnit();
            }
        } else if ("平均值".equals(statItem)) {
            List<Map<String, Object>> avgNotNull = lines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("avg"))).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(avgNotNull)) {
                double avg = avgNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("avg")))).average().orElse(0);
                return formatDoubleFixNum(avg, acc) + attr.getUnit();
            }
        } else if ("最小值~最大值".equals(statItem)) {
            List<Map<String, Object>> minNotNull = lines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("min"))).collect(Collectors.toList());
            List<Map<String, Object>> maxNotNull = lines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("max"))).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(minNotNull) && !ObjectUtils.isEmpty(maxNotNull)) {
                double min = minNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("min")))).min().orElse(0);
                double max = maxNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("max")))).max().orElse(0);
                return formatDoubleFixNum(min, acc) + attr.getUnit() + "~" + formatDoubleFixNum(max, acc) + attr.getUnit();
            }
        } else if ("变幅".equals(statItem)) {
            List<Map<String, Object>> minNotNull = lines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("min"))).collect(Collectors.toList());
            List<Map<String, Object>> maxNotNull = lines.stream().filter(x -> !ObjectUtils.isEmpty(x.get("max"))).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(minNotNull) && !ObjectUtils.isEmpty(maxNotNull)) {
                double min = minNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("min")))).min().orElse(0);
                double max = maxNotNull.stream().mapToDouble(e -> Double.parseDouble(String.valueOf(e.get("max")))).max().orElse(0);
                return formatDoubleFixNum(max - min, acc) + attr.getUnit();
            }
        }
        return "未知统计类型";
    }

    /**
     * 计算平均值
     */
    private Double calcAvgVal(List<Map<String, Object>> seaLines) {
        List<Double> values = seaLines.stream()
                .filter(x -> !ObjectUtils.isEmpty(x.get("avg")))
                .map(x -> Double.parseDouble(String.valueOf(x.get("avg")))).filter(Objects::nonNull).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(values)) {
            return null;
        }
        return Double.parseDouble(String.valueOf(values.stream().mapToDouble(e -> e).average().orElse(0)));
    }

    private int getAcc(String decimalAccuracy) {
        try {
            return Integer.parseInt(decimalAccuracy);
        } catch (Exception e) {
            return 6;
        }
    }

    private static String formatDoubleMaxNum(double num, int n) {
        NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setMaximumFractionDigits(n);
        nf.setGroupingUsed(false);
        return nf.format(num);
    }

    private static String formatDoubleFixNum(double num, int n) {
        BigDecimal originalValue = new BigDecimal(num);
        String numStr = originalValue.setScale(n, RoundingMode.HALF_UP).toString();
        /* 将字符串中的 连字符 替换为数学负号/减号 */
        return numStr.replaceFirst(HYPHEN, MATH_MINUS);
    }

    /**
     * 获取涉及到的所有测点
     */
    protected List<PointCommon> getRelatedPointIds(List<String> seaIds, Integer instId, List<PointCommon> fieldPoints) {
        if (ObjectUtils.isEmpty(seaIds)) {
            return Collections.emptyList();
        }

        if (ObjectUtils.isEmpty(instId)) {
            if (seaIds.get(0).contains("-")) {
                // 海上设施ID
                return fieldPoints.stream().filter(e -> seaIds.contains(e.getSeaFacilityId())).collect(Collectors.toList());
            } else {
                // 测点ID
                return fieldPoints.stream().filter(e -> seaIds.contains(String.valueOf(e.getId()))).collect(Collectors.toList());
            }
        }
        return fieldPoints.stream().filter(e -> seaIds.contains(e.getSeaFacilityId()) && instId.equals(e.getInstId())).collect(Collectors.toList());
    }

    /**
     * 获取方向
     */
    protected String getDirect(String s, List<PointCommon> points) {
        if ("X方向".equals(s)) {
            return "1";
        }
        if ("Y方向".equals(s)) {
            return "2";
        }
        if ("Z方向".equals(s)) {
            return "3";
        }
        if ("极坐标".equals(s)) {
            return "0";
        }
        if (ObjectUtils.isEmpty(points)) {
            return "1,2,3";
        }
        InstDirectAttr instDirectAttr = monitorService.getInstDirectAttr(points.get(0).getId());
        switch (instDirectAttr.getDirects().size()) {
            case 1:
                return "1";
            case 2:
                return "1,2";
            case 3:
                return "1,2,3";
        }
        return "1,2,3";
    }

    /**
     * 由分量名查找分量ID
     */
    private AttrCommon findAttrIdByName(List<AttrCommon> fieldAttrs, String attrName, Integer instId) {
        for (AttrCommon attr : fieldAttrs) {
            if (instId.equals(attr.getInstId()) && attrName.equals(attr.getName())) {
                return attr;
            }
        }
        return null;
    }

    /**
     * 获取目标海上设施
     */
    private List<String> getSeaFacilityIds(String item, List<SeaFacilityCommon> fieldSeaFacility, String seaId, String fieldNum) {
        if (ObjectUtils.isEmpty(fieldSeaFacility)) {
            return Collections.emptyList();
        }

        if ("所有风机".equals(item)) {
            // 获取该项目中所有风机ID
            return fieldSeaFacility.stream().filter(e -> e.getId().startsWith("1-")).map(SeaFacilityCommon::getId).collect(Collectors.toList());
        } else if ("典型风机".equals(item)) {
            // 获取该项目中所有典型风机ID
            return fieldSeaFacility.stream().filter(e -> e.getId().startsWith("1-")).filter(e -> e.getIsTypical() != null && e.getIsTypical()).map(SeaFacilityCommon::getId).collect(Collectors.toList());
        } else if ("非典型风机".equals(item)) {
            // 获取该项目中所有非典型风机ID
            return fieldSeaFacility.stream().filter(e -> e.getId().startsWith("1-")).filter(e -> e.getIsTypical() == null || !e.getIsTypical()).map(SeaFacilityCommon::getId).collect(Collectors.toList());
        } else if ("@{风机编号}".equals(item) || "@{升压站}".equals(item)) {
            // 指定风机或升压站
            return fieldSeaFacility.stream().filter(e -> e.getId().equals(seaId)).map(SeaFacilityCommon::getId).collect(Collectors.toList());
        } else {
            // 自定义变量，返回该变量下所有测点ID
            return reportVarService.getPointIdsByVarNameAndSea(fieldNum, item, seaId);
        }
    }

    /**
     * 获取目标海上设施
     */
    private List<String> getColSeaFacilityIds(String item, List<SeaFacilityCommon> fieldSeaFacility, String seaId) {
        if (ObjectUtils.isEmpty(fieldSeaFacility)) {
            return Collections.emptyList();
        }

        if (item.contains("测点")) {
            return fieldSeaFacility.stream().filter(e -> e.getId().equals(seaId)).map(SeaFacilityCommon::getId).collect(Collectors.toList());
        } else if (item.contains("不同风机")) {
            return fieldSeaFacility.stream().filter(e -> e.getId().startsWith("1-")).map(SeaFacilityCommon::getId).collect(Collectors.toList());
        } else if (item.contains("不同典型风机")) {
            return fieldSeaFacility.stream().filter(e -> e.getId().startsWith("1-")).filter(e -> e.getIsTypical() != null && e.getIsTypical()).map(SeaFacilityCommon::getId).collect(Collectors.toList());
        } else if (item.contains("不同非典型风机")) {
            return fieldSeaFacility.stream().filter(e -> e.getId().startsWith("1-")).filter(e -> e.getIsTypical() == null || !e.getIsTypical()).map(SeaFacilityCommon::getId).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

}
