package com.hdec.data.service;

import com.hdec.common.formula.AttrVal;
import com.hdec.data.domain.RedoJob;
import com.hdec.data.enums.RedoJobType;

import java.util.List;

public interface RedoJobService {

    void submitRedoJob(List<AttrVal> values);

    void commitJob(RedoJobType type, List<RedoJob> jobs);

    List<RedoJob> selectUndoTask(RedoJobType type, Integer createInterval);

    void completeJob(RedoJobType type, List<RedoJob> jobs);
}
