package com.hdec.data.service;

import com.google.common.collect.Lists;
import com.hdec.common.domain.*;
import com.hdec.common.vo.Direct;
import com.hdec.common.vo.InstDirectAttr;
import com.hdec.data.cache.Cache;
import com.hdec.data.domain.*;
import com.hdec.data.feign.MonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 在线数据录入逻辑类
 *
 * <AUTHOR>
 */
@Service
public class OnlineImportService {

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private TdService tdService;

    @Autowired
    private AlarmService alarmService;

    @Autowired
    private FormulaService formulaService;

    @Autowired
    private ExcelImportService excelImportService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private TdBaseService tdBaseService;

    /**
     * 列表
     */
    public R list(String time, Integer[] pointIds, String[] pointNos, String rate) {
        /* 获取所有分量 */
        InstDirectAttr info = monitorService.getInstDirectAttr(pointIds[0]);
        List<AttrCommon> attrs = info.getAttrs();
        if (info == null || ObjectUtils.isEmpty(attrs)) {
            return R.error("所选测点绑定的仪器下没有分量");
        }
        attrs = attrs.stream().filter(e -> rate.contains(e.getRate())).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(attrs)) {
            return R.error("没有" + rate + "分量");
        }

        List<Map<String, Object>> list = new ArrayList<>();
        // 循环测点
        for (int i = 0; i < pointIds.length; i++) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("point-测点编号-" + pointIds[i], pointNos[i]);
            map.put("time-观测时间-t", time);

            // 查询数据
            Map<String, Object> record = null;
            String tableName = tdService.convertRate(rate) + "_" + info.getInstId() + "_" + pointIds[i];
            if (tdService.isTableExist(tableName, 2, rate)) {
                record = tdBaseService.selectOne("select * from " + tableName + " where ts = '" + time + "' and del = false", rate);
            }

            for (AttrCommon attr : attrs) {
                if (attr.getIsPolar()) {
                    map.put("attr-" + attr.getName() + "-" + attr.getId() + "-0", record == null ? null : record.get(tdService.enColName(attr.getId(), 0)));
                } else {
                    for (Direct direct : info.getDirects()) {
                        map.put("attr-" + direct.getDirectName() + "方向" + attr.getName() + "-" + attr.getId() + "-" + direct.getDirectId(), record == null ? null : record.get(tdService.enColName(attr.getId(), direct.getDirectId())));
                    }
                }
            }
            list.add(map);
        }
        return R.success(list);
    }


//    /**
//     * 列表
//     */
//    public R list(String time, Integer[] pointIds, String[] pointNos) {
//        /* 获取所有分量 */
//        InstDirectAttr instInfo = monitorService.getInstDirectAttr(pointIds[0]);
//        List<AttrCommon> attrs = instInfo.getAttrs();
//        if (instInfo == null || ObjectUtils.isEmpty(attrs)) {
//            return R.error("所选测点绑定的仪器下没有分量");
//        }
//
//        List<Map<String, Object>> list = new ArrayList<>();
//        // 循环测点
//        for (int i = 0; i < pointIds.length; i++) {
//            // 循环方向
//            for (Direct direct : instInfo.getDirects()) {
//                Map<String, Object> map = new LinkedHashMap<>();
//                map.put("point-测点编号-" + pointIds[i], pointNos[i]);
//                map.put("direct-方向-" + direct.getDirectId(), direct.getDirectName());
//                map.put("time-观测时间-t", time);
//
//                // 查询数据
//                Map<String, Object> record = null;
//                String tableName  = "inst_" + instInfo.getInstId() + "_" + pointIds[i] + "_" + direct.getDirectId();
//                if (tdService.isTableExist(tableName, 2)) {
//                    record = tdService.selectOne("select * from " + tableName + " where ts = '" + time + "' and approval != 3");
//                }
//
//                // 循环分量
//                for (AttrCommon attr : attrs) {
//                    map.put("attr-" + attr.getName() + "-" + attr.getId(), record == null ? null : record.get("c_" + attr.getId()));
//                }
//                list.add(map);
//            }
//        }
//        return R.success(list);
//    }

    /**
     * 保存
     */
    public R save(String sessionId, String fieldNum, String rate, List<Map<String, String>> list) throws Exception {

        // 检查rate参数，如果为"高频"则抛出异常
        if ("高频".equals(rate)) {
            return R.error("无法添加高频数据");
        }

        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);

        Import imp = new Import();
        imp.setFieldNum(fieldNum);
        imp.setIsOverride(true);

        /* 获取导入需要的信息 */
        ImportInfo info = excelImportService.getImportInfo(imp, null, fieldNum);

        /* 批量数据、批量告警 */
        List<InsertRecord> insertRecords = new ArrayList<>();
        List<Alarm> alarmRecords = new ArrayList<>();
        List<Redo> redoList = new ArrayList<>();
        /* 保存数据 */
        for (Map<String, String> map : list) {
            /* 获取测点ID、时间、方向 */
            Integer pointId = null;
            String time = null;
            for (Map.Entry<String, String> en : map.entrySet()) {
                String key = en.getKey();
                if (key.startsWith("point")) {
                    pointId = Integer.parseInt(key.split("-")[2]);
                } else if (key.startsWith("time")) {
                    time = en.getValue();
                }
            }

            /* 获取分量数据并入库 */
            for (Map.Entry<String, String> en : map.entrySet()) {
                if (!en.getKey().startsWith("attr")) {
                    continue;
                }

                // 获取分量ID和分量值
                Integer attrId = Integer.parseInt(en.getKey().split("-")[2]);
                Integer directId = Integer.parseInt(en.getKey().split("-")[3]);
                Float attrVal = ObjectUtils.isEmpty(en.getValue()) ? null : Float.parseFloat(en.getValue());
                Set<AttrIdVal> attrValues = new HashSet<>(1);
                AttrIdVal attrIdVal = new AttrIdVal(attrId, attrVal);
                attrValues.add(attrIdVal);

                /* 处理告警 */
                alarmService.handleAlarm(info.getFieldIdPoints(), info.getRuleMap(), info.getRuleAdvancedMap(), info.getFieldAttrs(), time, pointId, directId, attrId, attrVal, fieldNum, alarmRecords);

                /* 记录重做信息 */
//                redoList.add(new Redo(TimeUtil.parse2Day(time), pointId, rate, fieldNum));

                /* 记录批入库 */
                insertRecords.add(new InsertRecord(info.getFieldIdPoints().get(pointId).getInstId(), time, pointId, directId, attrIdVal, attrValues, rate));
            }

        }

        /* 剩余数据入库 */
        if (insertRecords.size() > 0) {
            // 查询旧数据
            Map<String, List<Map<String, Object>>> historyData = tdService.getHistoryData(insertRecords, rate, imp.getIsOverride());


            tdService.batchSave(insertRecords, historyData, imp.getIsOverride());
//            excelImportService.statRedo(redoList, insertRecords, fieldNum);

            /* 处理公式 */
//            redoList.addAll(excelImportService.handleFunc(info, fieldNum, insertRecords, info.getFieldFormulas(), info.getFieldAttrs(), info.getFieldParams(), null));
        }

        if (alarmRecords.size() > 0) {
            Lists.partition(alarmRecords, 100).forEach(l -> monitorService.saveAlarms(l));
            Cache.hasAlarmFlag = true;
        }

        /* 重做统计 */
//        excelImportService.redoStat(redoList, "在线数据录入", "fast", resource.getUserId(), resource.getUsername(), fieldNum);
        return R.success("添加成功");
    }

    private void print(List<InsertRecord> insertRecords) {
        for (InsertRecord insertRecord : insertRecords) {
            System.out.println(insertRecord);
        }
    }

//    /**
//     * 保存
//     */
//    public void save(String fieldNum, List<Map<String, String>> list) throws Exception {
//        /* 获取所有告警规则 */
//        Map<String, List<AlarmRuleVo>> ruleMap = monitorService.fieldRules(fieldNum);
//
//        /* 获取所有公式 */
//        List<FormulaCommon> formulas = monitorService.fieldFormulas(fieldNum);
//
//        /* 获取某风场下所有参数 */
//        List<PointParamCommon> pointParams = monitorService.allParamsByField(fieldNum);
//        Map<String, String> pointParamMap = pointParamsToMap(pointParams);
//
//        /* 保存数据 */
//        for (Map<String, String> map : list) {
//            saveOneData(fieldNum, map, ruleMap, formulas, pointParamMap);
//        }
//    }
//
//    /**
//     * 保存一条记录
//     */
//    private void saveOneData(String fieldNum, Map<String, String> map, Map<String, List<AlarmRuleVo>> alarmMap, List<FormulaCommon> formulas, Map<String, String> pointParamMap) throws Exception {
//        /* 获取测点ID、时间、方向 */
//        Integer pointId = null;
//        String time = null;
//        Integer directId = null;
//        for (Map.Entry<String, String> en : map.entrySet()) {
//            String key = en.getKey();
//            if (key.startsWith("point")) {
//                pointId = Integer.parseInt(key.split("-")[2]);
//            } else if (key.startsWith("time")) {
//                time = en.getValue();
//            } else if (key.startsWith("direct")) {
//                directId = Integer.parseInt(key.split("-")[2]);
//            }
//        }
//
//        /* 确保测点超级表存在 */
//        String sTableName = tdService.createSTableIfNotExist(pointId, false);
//
//        /* 获取分量数据并入库 */
//        for (Map.Entry<String, String> en : map.entrySet()) {
//            if (!en.getKey().startsWith("attr")) {
//                continue;
//            }
//            Set<AttrIdVal> attrValues = new HashSet<>();
//
//            // 获取分量ID和分量值
//            Integer attrId = Integer.parseInt(en.getKey().split("-")[2]);
//            Float attrVal = ObjectUtils.isEmpty(en.getValue()) ? null : Float.parseFloat(en.getValue());
//            attrValues.add(new AttrIdVal(attrId, attrVal));
//
//            redoService.insert(new StatRedo(TimeUtil.parse2Day(time), pointId, directId, attrId, fieldNum));
//
//            /* 处理告警 */
//            alarmService.handleAlarm(alarmMap, time, pointId, directId, attrId, attrVal, fieldNum, null);
//
//            /* 数据入库 */
//            tdService.save(sTableName, time, pointId, directId, attrValues);
//
//            /* 处理公式计算（必需要上面先入库） */
////            String res = formulaService.handleFormula(alarmMap, formulas, time, pointId, directId, attrId, attrVal, pointParamMap, fieldNum, false);
////            System.out.println("返回值：" + res);
////            if (res != null) {
////                String[] arr = res.split("@");
////                if (arr.length == 3) {
////                    System.out.println("再次执行：start ");
////                    formulaService.handleFormula(alarmMap, formulas, time, Integer.parseInt(arr[0]), 1, Integer.parseInt(arr[1]), Float.parseFloat(arr[2]), pointParamMap, fieldNum);
////                    System.out.println("再次执行：end ");
////                }
////            }
//        }
//
//    }

    private Map<String, String> pointParamsToMap(List<PointParamCommon> pointParams) {
        if (ObjectUtils.isEmpty(pointParams)) {
            return Collections.emptyMap();
        }

        Map<String, String> map = new HashMap<>(pointParams.size());
        for (PointParamCommon point : pointParams) {
            map.put(point.getMeasurePointId() + "-" + point.getDirection() + "-" + point.getParamId(), point.getParamValue());
        }
        return map;
    }

}
