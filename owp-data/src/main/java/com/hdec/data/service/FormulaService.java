package com.hdec.data.service;

import com.hdec.common.constant.Constant;
import com.hdec.common.domain.*;
import com.hdec.common.util.TimeUtil;
import com.hdec.common.util.func.FuncUtil;
import com.hdec.common.vo.InstDirectAttr;
import com.hdec.data.domain.AttrIdVal;
import com.hdec.data.domain.Redo;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.util.FormulaUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.regex.Pattern.compile;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class FormulaService {

    @Autowired
    private TdService tdService;

    @Autowired
    private AlarmService alarmService;

    @Lazy
    @Autowired
    private ExcelImportService excelImportService;

//    /**
//     * 处理多个公式
//     * @param time 正在录入的时间
//     * @param pointId 正在录入的测点
//     * @param directId 正在录入的方向
//     * @param attrId 正在录入的属性
//     * @param attrVal 正在录入的属性值
//     */
//    public List<Redo> handleFormulas(String rate, Set<AttrIdVal> values, Map<String, List<AlarmRuleVo>> ruleMap, List<FormulaCommon> fieldFormulas,
//                               String time, Integer pointId, Integer directId, Integer attrId, Float attrVal,
//                                Map<String, String> pointParamMap, Boolean isPre, List<Alarm> alarmRecords, String fieldNum, Map<Integer, AttrCommon> fieldAttrs) throws Exception {
//
//        List<FormulaCommon> relateFormulas = findRelateFormulas(fieldFormulas, time, pointId, attrId, isPre);
//        if (ObjectUtils.isEmpty(relateFormulas)) {
//            return Collections.emptyList();
//        }
//
//        List<Redo> redoList = new ArrayList<>();
//        for (FormulaCommon formula : relateFormulas) {
//            String res = handleFormula(rate, values, ruleMap, fieldFormulas, time, pointId, directId, attrId, attrVal, pointParamMap, isPre, alarmRecords, formula, false, fieldAttrs);
//            if (res != null) {
//                String[] arr = res.split("@");
//                if (arr.length == 3) {
//                    List<FormulaCommon> fs = findRelateFormulas(fieldFormulas, time, Integer.parseInt(arr[0]), Integer.parseInt(arr[1]), isPre);
//                    if (ObjectUtils.isEmpty(fs)) {
//                        return Collections.emptyList();
//                    }
//                    for (FormulaCommon f : fs) {
//                        handleFormula(rate, values, ruleMap, fieldFormulas, time, Integer.parseInt(arr[0]), directId, Integer.parseInt(arr[1]), Float.parseFloat(arr[2]), pointParamMap, isPre, alarmRecords, f, false, fieldAttrs);
//                    }
//                } else {
//                    List<FormulaCommon> fs = findRelateFormulas(fieldFormulas, time, Integer.parseInt(arr[0]), Integer.parseInt(arr[1]), isPre);
//                    if (ObjectUtils.isEmpty(fs)) {
//                        return Collections.emptyList();
//                    }
//                    HashSet<AttrIdVal> set = new HashSet<>();
//                    set.add(new AttrIdVal(Integer.parseInt(arr[1]), Float.parseFloat(arr[2])));
//                    redoList.add(new Redo(TimeUtil.parse2Day(time), Integer.parseInt(arr[0]), null, fieldNum));
//                    for (FormulaCommon f : fs) {
//                        handleFormula(rate, set, ruleMap, fieldFormulas, time, Integer.parseInt(arr[0]), directId, Integer.parseInt(arr[1]), Float.parseFloat(arr[2]), pointParamMap, isPre, alarmRecords, f, true, fieldAttrs);
//                    }
//                }
//            }
//        }
//        return redoList;
//    }

//    /**
//     * 处理公式计算
//     */
//    public String handleFormula(String rate, Set<AttrIdVal> values, Map<String, List<AlarmRuleVo>> ruleMap, List<FormulaCommon> fieldFormulas, String time,
//                                Integer pointId, Integer directId, Integer attrId, Float attrVal,
//                                Map<String, String> pointParamMap, Boolean isPre, List<Alarm> alarmRecords, FormulaCommon formula, Boolean flag, Map<Integer, AttrCommon> fieldAttrs) throws Exception {
//        String[] formulaArr = formula.getFormula().split("\\&");
//        if (formulaArr.length == 1) {
//            // 单条件
//            String formulaExpress = replaceVarNew(rate, formula.getFormula(), time, pointId, directId, pointParamMap, values, fieldAttrs);
//            Double calcRes = FormulaUtil.calc(formulaExpress);
//            System.out.println(formula.getFormula());
//            log.info("表达式：{}，结果：{}", formulaExpress, calcRes);
//
//            // 更新值
//            if (calcRes != null) {
//                if (ruleMap != null) {
//                    alarmService.handleAlarm(ruleMap, time, formula.getPointId(), directId, formula.getAttrId(), Float.parseFloat(calcRes + ""), formula.getFieldNum(), alarmRecords);
//                }
//                if (!pointId.equals(formula.getPointId()) || flag) {
//                    // 说明计算的是其它测点的数据
//                    Set<AttrIdVal> vSets = new HashSet<>();
//                    vSets.add(new AttrIdVal(formula.getAttrId(), Float.parseFloat(String.valueOf(calcRes))));
//                    tdService.save(rate, time, formula.getPointId(), directId, vSets);
//                    return formula.getPointId() + "@" + formula.getAttrId() + "@" + calcRes + "@S";
//                }
//
//                AttrIdVal newVal = new AttrIdVal(formula.getAttrId(), Float.parseFloat(String.valueOf(calcRes)));
//                System.out.println("calcRes:" + calcRes);
//                System.out.println("values:" + values);
//                System.out.println("newVal:" + newVal);
//                System.out.println();
//                if (values.contains(newVal)) {
//                    values.remove(newVal);
//                }
//                values.add(newVal);
//                return formula.getPointId() + "@" + formula.getAttrId() + "@" + calcRes;
//            }
//        }
//        return null;
//    }

    /**
     * 替换变量
     *
     * @param pointId  正在录入的测点
     * @param directId 正在录入的方向
     * @param values   这一时刻待录入的所有值
     */
    private String replaceVarNew(String rate, String formula, String time, Integer pointId, Integer directId, Map<String, String> pointParamMap, Set<AttrIdVal> values, Map<Integer, AttrCommon> fieldAttrs) throws ParseException {
        Pattern p = compile("[#@]\\{[\\d+:]{1,}}");
        StringBuffer res = new StringBuffer();

        Matcher m = p.matcher(formula);
        while (m.find()) {
            String matchedStr = m.group();
            String replaceText = "";
            if (matchedStr.startsWith("#")) {
                /* 替换分量 */
                Integer fPointId = parseId(matchedStr, 0);
                Integer fAttrId = parseId(matchedStr, 1);
                Integer type = parseId(matchedStr, 2);
                Integer millis = parseId(matchedStr, 3);

                Float val = findAttrValue(pointId, values, fPointId, fAttrId);
                if (val == null) {
                    val = tdService.selectByTime(rate, time, fPointId, directId, fAttrId, fieldAttrs.get(fAttrId).getIsPolar(), type, millis);
                }

                if (val == null) {
                    replaceText = "X";
                } else {
                    replaceText = String.valueOf(val);
                }
            } else if (matchedStr.startsWith("@")) {
                /* 替换参数 */
                Integer fPointId = parseId(matchedStr, 0);
                Integer paramId = parseId(matchedStr, 1);
                if (paramId != null) {
                    String val = pointParamMap.get(fPointId + "-" + directId + "-" + paramId);
                    if (ObjectUtils.isEmpty(val)) {
                        replaceText = "X";
                    } else {
                        float v = Float.parseFloat(val);
                        replaceText = String.valueOf(v);
                    }
                }
            }
            m.appendReplacement(res, replaceText);
        }

        m.appendTail(res);
        return res.toString().replace(" ", "");
    }

    private Float findAttrValue(Integer pointId, Set<AttrIdVal> values, Integer fPointId, Integer fAttrId) {
        if (ObjectUtils.isEmpty(values)) {
            return null;
        }
        if (!pointId.equals(fPointId)) {
            return null;
        }
        for (AttrIdVal value : values) {
            if (fAttrId.equals(value.getAttrId())) {
                return value.getAttrVal();
            }
        }
        return null;
    }


    /**
     * 解析ID
     */
    private Integer parseId(String s, int index) {
        /* 去掉两边的花括号后按冒号分割 */
        String[] arr = s.substring(s.indexOf("{") + 1, s.indexOf("}")).split(":");
        if (arr == null || arr.length < 2) {
            return null;
        }
        if (index >= arr.length) {
            return null;
        }
        return Integer.parseInt(arr[index]);
    }

    /**
     * 替换变量
     */
    private String replaceVar(String formula, Integer pointId, Integer directId, Map<String, String> pointParamMap, Integer attrId, Float attrVal) {
        Pattern p = compile("[#@]\\{[\\d+:]{1,}}");
        StringBuffer res = new StringBuffer();

        Matcher m = p.matcher(formula);
        while (m.find()) {
            String matchedStr = m.group();
            String replaceText = "";
            if (matchedStr.startsWith("#")) {
                // 分量
                String s = matchedStr.substring(matchedStr.indexOf("{") + 1, matchedStr.indexOf("}"));
                String[] arr = s.split(":");
                Integer formulaAttrId = Integer.parseInt(arr[1]);
                if (attrId.equals(formulaAttrId)) {
                    replaceText = String.valueOf(attrVal);
                } else {

                }
//                if (attrId != null) {
//                    if (attrVal == null) {
//                        replaceText = "X";
//                    } else {
//                        replaceText = String.valueOf(attrVal);
//                    }
//                }
            } else if (matchedStr.startsWith("@")) {
                // 参数
                Integer paramId;
                String s = matchedStr.substring(matchedStr.indexOf("{") + 1, matchedStr.indexOf("}"));
                String[] arr = s.split(":");
                paramId = Integer.parseInt(arr[1]);
                if (paramId != null) {
                    String val = pointParamMap.get(pointId + "-" + directId + "-" + paramId);
                    if (ObjectUtils.isEmpty(val)) {
                        replaceText = "X";
                    } else {
                        float v = Float.parseFloat(val);
                        replaceText = v + "";
                    }
                }
            }
            m.appendReplacement(res, replaceText);
        }

        m.appendTail(res);
        return res.toString().replace(" ", "");
    }

    private String replaceVarSep(String formula, Integer pointId, Integer directId, Map<String, String> pointParamMap, Map<String, Object> record) {
        Pattern p = compile("[#@]\\{[\\d+:]{1,}}");
        StringBuffer res = new StringBuffer();

        Matcher m = p.matcher(formula);
        while (m.find()) {
            String matchedStr = m.group();
            String replaceText = "";
            if (matchedStr.startsWith("#")) {
                // 分量
                Integer attrId;
                String s = matchedStr.substring(matchedStr.indexOf("{") + 1, matchedStr.indexOf("}"));
                String[] arr = s.split(":");
                attrId = Integer.parseInt(arr[1]);
                if (attrId != null) {
                    Float attrVal = (Float) record.get("c_" + attrId);
                    if (attrVal == null) {
                        replaceText = "X";
                    } else {
                        replaceText = String.valueOf(attrVal);
                    }
                }
            } else if (matchedStr.startsWith("@")) {
                // 参数
                Integer paramId;
                String s = matchedStr.substring(matchedStr.indexOf("{") + 1, matchedStr.indexOf("}"));
                String[] arr = s.split(":");
                paramId = Integer.parseInt(arr[1]);
                if (paramId != null) {
                    String val = pointParamMap.get(pointId + "-" + directId + "-" + paramId);
                    if (ObjectUtils.isEmpty(val)) {
                        replaceText = "X";
                    } else {
                        float v = Float.parseFloat(val);
                        replaceText = v + "";
                    }
                }
            }
            m.appendReplacement(res, replaceText);
        }

        m.appendTail(res);
        return res.toString().replace(" ", "");
    }

    /**
     * 查找相关公式
     *
     * @param pointId 正在录入的测点
     * @param attrId  正在录入的分量
     */
    public List<FormulaCommon> findRelateFormulas(List<FormulaCommon> fieldFormulas, String time, Integer pointId, Integer attrId, Boolean isPre) throws ParseException {
        List<FormulaCommon> resList = new ArrayList<>();
        for (FormulaCommon formula : fieldFormulas) {
            if (formula.getFormula().contains(pointId + ":" + attrId) && timeBetween(time, formula.getStartTime(), formula.getEndTime(), isPre)) {
                resList.add(formula);
            }
        }
        return resList;
    }

    /**
     * 判断是否是否在某个范围内
     */
    private static boolean timeBetween(String time, Date startTime, Date endTime, Boolean isPre) throws ParseException {
        SimpleDateFormat format;
        if (isPre) {
            format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        } else {
            format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        }
        Date date = format.parse(time);
        return TimeUtil.isTimeBetween(date, startTime, endTime);
    }

//    public void reCalc(ResourceCommon resource, String rate, FormulaCommon formula, List<Map<String, Object>> records,
//                       Map<String, String> pointParamMap, Map<Integer, AttrCommon> fieldAttrMap, String fieldNum) throws ParseException {
//        String[] formulaArr = formula.getFormula().split("\\&");
//
//        List<Redo> redoList = new ArrayList<>();
//        for (Map<String, Object> record : records) {
//            Date ts = (Date) record.get("ts");
//            record.put("point", formula.getPointId());
//
//            record.forEach((k, v) -> {
//                if (k != null && k.startsWith("a_") && v != null) {
//                    // a_12_1
//                    String[] arr = k.split("_");
//                    Integer attrId = Integer.parseInt(arr[1]);
//                    Integer directId = Integer.parseInt(arr[2]);
//                    Float attrVal = Float.parseFloat(String.valueOf(v));
//                    if (directId == 0) {
//                        return;
//                    }
//
//                    Set<AttrIdVal> values = new HashSet<>(1);
//                    AttrIdVal attrIdVal = new AttrIdVal(attrId, attrVal);
//                    values.add(attrIdVal);
//
//                    if (formulaArr.length > 1) {
//                        // todo
//                    } else {
//                        // 单条件
//                        String formulaExpress = null;
//                        try {
//                            formulaExpress = replaceVarNew(rate, formula.getFormula(), TimeUtil.format2Second(ts), formula.getPointId(), directId, pointParamMap, values, fieldAttrMap);
//                        } catch (ParseException e) {
//                            e.printStackTrace();
//                        }
//                        Double calcRes = (Double) FuncUtil.calc(fieldNum, formulaExpress, Collections.emptyMap());
//                        log.info("{}表达式：{}，结果：{}", fieldNum, formulaExpress, calcRes);
//
//                        // 更新值
//                        if (calcRes != null) {
//                            record.put("a_" + formula.getAttrId() + "_" + directId, calcRes);
//                        }
//                    }
//
//                    redoList.add(new Redo(TimeUtil.parse2Day(TimeUtil.format2Day(ts)), formula.getPointId(), rate, formula.getFieldNum()));
//
////                    try {
////
////                        values.add(new AttrIdVal(Integer.parseInt(k.substring(2)), Float.parseFloat(String.valueOf(v))));
////                    } catch (Exception e) {
////                    }
//                }
//            });
//        }
//
//        tdService.insertRecords(formula.getInstId(), rate, records);
//
//        /* 重做统计 */
//        excelImportService.redoStat(redoList, "公式计算", "slow", resource.getUserId(), resource.getUsername(), fieldNum);
//    }

    /**
     * 将公式中的单位统一为m/t
     */
    public static String unifyUnit(String express) {
        if (express == null) {
            return null;
        }

        StringBuffer sb = new StringBuffer();
        Pattern pattern = Pattern.compile("\\d+m/t|\\d+h/t");
        Matcher matcher = pattern.matcher(express);
        while (matcher.find()) {
            String matchedStr = matcher.group();
            int num = Integer.parseInt(matchedStr.substring(0, matchedStr.indexOf("/t") - 1));
            if (matchedStr.endsWith("m/t")) {
                matcher.appendReplacement(sb, String.valueOf(num));
            } else {
                matcher.appendReplacement(sb, String.valueOf(num * 60));
            }
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 修正公式
     */
    public String correctFormula(String formula) {
        if (ObjectUtils.isEmpty(formula)) {
            return formula;
        }

        StringBuffer sb = new StringBuffer();
        Matcher m = compile("[&|$]\\{[\\w]+}").matcher(formula);
        while (m.find()) {
            String matchedStr = m.group();
            m.appendReplacement(sb, matchedStr.replace("&{", "").replace("${", "").replace("}", ""));
        }
        m.appendTail(sb);
        return sb.toString().replace(" ", "");
    }

    @Resource
    private MonitorService monitorService;
    @Resource
    private TdBaseService tdBaseService;

    public void rateFormulaCalc(String rate, String fieldNum, Integer pointId, Date startTime, Date endTime, Integer userId, String username) {
        /*  获取所有分钟分量  */
        InstDirectAttr instAttrs = monitorService.getInstAttrsByRate(pointId, rate);
        if (instAttrs == null || instAttrs.getInstId() == null || instAttrs.getAttrs() == null || instAttrs.getDirects() == null) {
            log.error("公式计算-测点仪器信息获取失败:{}[{}-{}]  [attrs:{}]", pointId, startTime, endTime, instAttrs);
            return;
        }
        List<AttrCommon> attrs = instAttrs.getAttrs();
        List<Integer> attrIds = attrs.stream().map(AttrCommon::getId).collect(Collectors.toList());
        /*  获取所有分钟公式  */
        List<FormulaCommon> formulas = monitorService.fieldFormulas(fieldNum).stream()
                .filter(f -> Objects.equals(pointId, f.getPointId()) && attrIds.contains(f.getAttrId())).collect(Collectors.toList());
        if (formulas.isEmpty()) {
            log.info("公式计算-分钟-无公式:{}[{}-{}]", pointId, startTime, endTime);
            return;
        }
        /*  按依赖关系排序  */
        List<FormulaCommon> sortedFormulas = new ArrayList<>(formulas.size());
        for (FormulaCommon item : formulas) {
            if (sortedFormulas.contains(item)) {
                continue;
            }
            String formula = item.getFormula();
            List<String> attrInclude = FormulaUtil.collectAttr(formula);
            List<FormulaCommon> fs = formulas.stream().filter(f -> attrInclude.stream().filter(a ->
                            a.startsWith("a_" + f.getPointId() + "_" + f.getAttrId())).findAny().orElse(null) != null)
                    .collect(Collectors.toList());
            sortedFormulas.addAll(fs);
            sortedFormulas.add(item);
        }

        List<PointParamCommon> pointParams = monitorService.allParamsByField(fieldNum);
        Map<String, String> paramMap = pointParams.stream().filter(e -> !Objects.isNull(e.getParamValue())).collect(
                Collectors.toMap(e -> e.getMeasurePointId() + "-" + e.getDirection() + "-" + e.getParamId(),
                        PointParamCommon::getParamValue,
                        (oldVal, newVal) -> newVal));

        List<AttrCommon> fieldAttrs = monitorService.allAttrByField(fieldNum);
        Map<Integer, AttrCommon> attrMap = fieldAttrs.stream().collect(Collectors.toMap(AttrCommon::getId, Function.identity(), (key1, key2) -> key2));

        /*  计算公式  */
        for (FormulaCommon f : sortedFormulas) {
            formulaReCalc(rate, f, formulas, startTime, endTime, attrMap, paramMap, userId, username);
        }
    }

    public void formulaReCalc(String rate, FormulaCommon formula, List<FormulaCommon> formulas, Date startTime, Date endTime,
                              Map<Integer, AttrCommon> attrMap, Map<String, String> paramMap,
                              Integer userId, String username) {
        Integer instId = formula.getInstId();
        String fieldNum = formula.getFieldNum();
        String tablePrefix = Objects.equals(rate, Constant.RATE_HIGH) ? "high" :
                Objects.equals(rate, Constant.RATE_MIN) ? "high" :
                        Objects.equals(rate, Constant.RATE_HOUR) ? "min" : null;
        String tableRate = Objects.equals(rate, Constant.RATE_HIGH) ? Constant.RATE_HIGH :
                Objects.equals(rate, Constant.RATE_MIN) ? Constant.RATE_HIGH :
                        Objects.equals(rate, Constant.RATE_HOUR) ? Constant.RATE_MIN : null;
        if (tablePrefix == null || tableRate == null) {
            log.error("公式计算-未知频率:{}", rate);
        }
        String tableName = tablePrefix + "_" + instId;
        String start = TimeUtil.format2Second(formula.getStartTime().before(startTime) ? startTime : formula.getStartTime());
        String end = TimeUtil.format2Second(formula.getEndTime().before(endTime) ? formula.getStartTime() : endTime);
        String sql = "select * from " + tableName + " where point = " + formula.getPointId() + " and ts between '" + start + "' and '" + end + "' ";
        log.info("重计算sql:{}",sql);
        List<Map<String, Object>> records = tdBaseService.selectMulti(sql, tableRate);
        if (!records.isEmpty()) {
            List<Redo> redoList = excelImportService.handleFunc(null, fieldNum, excelImportService.toRecords(records, formula.getPointId()), formulas, attrMap, paramMap, formula.getId());
            /*  上位机部分不需要提交重新统计任务  */
            //            excelImportService.redoStat(redoList, "数据导出-重计算", "slow", userId, username, fieldNum);
        }
    }
}
