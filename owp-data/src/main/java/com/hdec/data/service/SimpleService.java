package com.hdec.data.service;

import com.hdec.common.domain.AttrCommon;

import java.util.Date;
import java.util.List;

public interface SimpleService {

    /**
     * 高频采样
     *
     * @param points    测点
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    void doHighSample(List<Integer> points, Date startTime, Date endTime);


    /**
     * 插入抽吸数据
     *
     * @param instId    仪器ID
     * @param pointId   测点ID
     * @param attrs     分量信息
     * @param directs   方向
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    void insertSampleRecord(Integer instId, Integer pointId, List<AttrCommon> attrs, Integer[] directs,
                            Date startTime, Date endTime);

}
