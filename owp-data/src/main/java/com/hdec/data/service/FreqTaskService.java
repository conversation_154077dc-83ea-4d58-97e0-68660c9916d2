package com.hdec.data.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hdec.common.constant.Constant;
import com.hdec.common.domain.ResourceCommon;
import com.hdec.common.util.*;
import com.hdec.common.vo.Direct;
import com.hdec.data.domain.FreqPic;
import com.hdec.data.domain.FreqResult;
import com.hdec.data.domain.FreqTask;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.mapper.FreqTaskMapper;
import com.hdec.data.qo.AccQo;
import com.hdec.data.qo.FreqTaskQo;
import com.hdec.data.vo.FrequencyVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.text.DecimalFormat;
import java.util.List;

/**
 * 频谱分析任务业务逻辑类
 */
@Slf4j
@Service
public class FreqTaskService {

    @Autowired
    private FreqTaskMapper freqTaskMapper;

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private FrequencyService freqService;

    @Lazy
    @Autowired
    private FreqResultService freqResultService;


    @Autowired
    private FreqResultService resultService;

    @Autowired
    private FrequencyValueFilter valueFilter;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String FREQUENCY_FILE_DIR = "frequency/";

    /** 访问映射 */
    @Value("${file.visitMapping}")
    private String visitMapping;

    /** 文件本地路径 - Windows */
    @Value("${file.windowPath}")
    private String windowPath;

    /** 文件本地路径 - Linux */
    @Value("${file.linuxPath}")
    private String linuxPath;

    /** 格式化小数，最多保留2位小数 */
    private DecimalFormat df = new DecimalFormat("0.##");

    /**
     * 任务列表
     */
    public List<FreqTask> list(String fieldNum, FreqTaskQo qo) {
        List<FreqTask> tasks = freqTaskMapper.list(fieldNum, qo);
        return tasks;
    }

    /**
     * 任务列表
     */
    public List<FreqPic> picList(String fieldNum, FreqTaskQo qo) {
        List<FreqPic> tasks = freqTaskMapper.picList(fieldNum, qo);
        return tasks;
    }

    /**
     * 保存
     */
    public void save(FreqTask task, String sessionId) {
        /* 设置用户信息 */
        setUserInfo(task, sessionId);

        /* 保存任务 */
        task.setProcess(0);
        task.setFilterParam(df.format(task.getRateLow()) + (ObjectUtils.isEmpty(task.getRateHigh()) ? "Hz" : "~" + df.format(task.getRateHigh()) + "Hz"));
        freqTaskMapper.save(task);

        /* 另启一个线程做频谱分析 */
        new Thread(() -> handleFreqTask(task)).start();
    }

    /**
     * 保存
     */
    public Integer picSave(FreqPic pic, String sessionId) {
        /* 设置用户信息 */
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        if (resource != null) {
            pic.setUserId(resource.getUserId());
            pic.setUsername(resource.getUsername());
        }

        /* 保存任务 */
        freqTaskMapper.picSave(pic);
        return pic.getId();

//        /* 将文件保存到本地路径 */
//        String dir = FREQUENCY_FILE_DIR + pic.getId() + "/";
//        String filePath = CommonUtil.isLinux() ? linuxPath + dir : windowPath + dir;
//        FileUtil.makeSureDirExist(filePath);
//        File file1 = new File(filePath + file.getOriginalFilename());
//        System.out.println("文件保存路径："+file1.getAbsolutePath());
//        file.transferTo(file1);
//        FileCompressUtil.unzip(file1.getAbsolutePath());

//        /* 另启一个线程保存结果 */
//        savePic(pic.getFieldNum(), pic.getUrl(), pic.getFieldNum());
    }

    /**
     * 上传图片
     */
    public void picUpload(MultipartFile file, Integer id) throws IOException {
        /* 将文件保存到本地路径 */
        String dir = FREQUENCY_FILE_DIR + id + "/";
        String filePath = CommonUtil.isLinux() ? linuxPath + dir : windowPath + dir;
        FileUtil.makeSureDirExist(filePath);

        System.out.println(file.getOriginalFilename());
        File localFile = new File(filePath + file.getOriginalFilename());
        System.out.println("文件保存路径：" + localFile.getAbsolutePath());
        file.transferTo(localFile);

        /* 如果全部上传完毕，则修改记录标志位为“已完成” */
        FreqPic pic = freqTaskMapper.getPicById(id);
        if (pic != null) {
            File[] files = new File(filePath).listFiles();
            if (files.length >= pic.getPicSize()) {
                freqTaskMapper.updatePicStatus(id, 1);

                /* 保存结果 */
                savePic(pic.getFieldNum(), id, files);
            }
        }
    }

    private void savePic(String fieldNum, Integer id, File[] files) {
        if (ObjectUtils.isEmpty(files)) {
            return;
        }

        for (File file : files) {
            String name = file.getName();
            try {
                FreqResult res = parseFileName(fieldNum, id, name);
                if (res == null || res.getPointId() == null || res.getDirectId() == null) {
                    continue;
                }
                res.setReferNum(0);
                res.setDuration(60);
                res.setDurationUnit("分钟");
                freqResultService.save(res);
            } catch (Exception e) {}
        }
    }

    /**
     * 解析文件名
     */
    public FreqResult parseFileName(String fieldNum, Integer id, String fileName) {
        if (ObjectUtils.isEmpty(fileName)) {
            return null;
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return null;
        }
        String baseName = fileName.substring(0, lastDotIndex);

        String[] arr = baseName.split("_");
        if (arr.length != 4) {
            return null;
        }

        String pointNo = arr[0];
        String direct = arr[1];
        String time = arr[2];
        Double val = Double.parseDouble(arr[3]);
        Integer pointId = monitorService.searchPointIdByNo(fieldNum, pointNo);

        String imgUrl = visitMapping + "/" + FREQUENCY_FILE_DIR + id + "/" + fileName;
        FreqResult res = new FreqResult(pointId, pointNo, parseDirect(direct), time + "0000", imgUrl, fieldNum, val);

//        // 6. 输出解析结果
//        System.out.println("测点ID: " + pointId);
//        System.out.println("测点名称: " + pointNo);
//        System.out.println("方向: " + direct);
//        System.out.println("时间: " + time);
//        System.out.println("频谱值: " + val);
        return res;
    }

    private static Integer parseDirect(String direct) {
        if ("X".equals(direct)) {
            return 1;
        }
        if ("Y".equals(direct)) {
            return 2;
        }
        if ("Z".equals(direct)) {
            return 3;
        }
        return null;
    }

    /**
     * 为任务设置用户信息
     */
    private void setUserInfo(FreqTask task, String sessionId) {
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        if (resource != null) {
            task.setUserId(resource.getUserId());
            task.setUsername(resource.getUsername());
        }
    }

    /**
     * 处理频谱分析
     */
    private void handleFreqTask(FreqTask task) {
        /* 获取时间段 */
        List<String> timeSpans = TimeUtil.splitBySpan(TimeUtil.completeStart(task.getStartTime()), TimeUtil.completeEnd(task.getEndTime()), task.getTimeSpan());
        if (ObjectUtils.isEmpty(timeSpans)) {
            task.setProcess(100);
            freqTaskMapper.update(task);
            return;
        }

        String dir = FREQUENCY_FILE_DIR + task.getId() + "/";
        String filePath = CommonUtil.isLinux() ? linuxPath + dir : windowPath + dir;
        FileUtil.makeSureDirExist(filePath);

        /* 获取方向 */
        int total = task.getPointIds().length * timeSpans.size();
        int current = 0;
        for (int i = 0; i < task.getPointIds().length; i++) {
            Integer pointId = task.getPointIds()[i];
            List<Direct> directs = monitorService.getInstDirectAttr(pointId).getDirects();
            for (String timeSpan : timeSpans) {
                for (Direct direct : directs) {
                    try {
                        handleSingleFreq(task, filePath, i, pointId, timeSpan, direct);
                    } catch (Exception e) {
                        log.error("频谱分析出错：", e);
                    }
                }

                /* 更新进度 */
                int process = ++current * 100 / total;
                task.setProcess(Math.min(process, 100));
                freqTaskMapper.update(task);
            }
        }
    }

    /**
     * 处理一个频谱图
     */
    private void handleSingleFreq(FreqTask task, String filePath, int pointIndex, Integer pointId, String timeSpan, Direct direct) throws Exception {
        /* 频谱分析 */
        String[] arr = timeSpan.split(",");
        String imgUrl = getFrequencyImgUrl(task, pointId, direct.getDirectId(), arr[0], arr[1], filePath);
        if (ObjectUtils.isEmpty(imgUrl)) {
            return;
        }
        FreqResult freqResult = new FreqResult(pointId, task.getPointNos()[pointIndex], direct.getDirectId(), task.getWaveFilter() + "-" + task.getFilterType(), arr[0], task.getTimeSpan(), task.getTimeSpanUnit(),
                imgUrl, JSON.toJSONString(task), task.getFieldNum());

        /* 保存结果 */
        resultService.delByCondition(freqResult);
        resultService.save(freqResult);
    }

    /**
     * 获取频谱图URL
     */
    private String getFrequencyImgUrl(FreqTask task, Integer pointId, Integer directId, String startTime, String endTime, String filePath) throws Exception {
        FrequencyVo freqVo = freqService.analysisFrequency(task, new AccQo(pointId, directId, startTime, endTime));
        if (ObjectUtils.isEmpty(freqVo.getXArr()) || ObjectUtils.isEmpty(freqVo.getYArr())) {
            return null;
        }

        String picName = CommonUtil.uuid() + ".jpg";
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(freqVo, valueFilter));
        jsonObject.put("output", filePath + picName);

        File tempFile = File.createTempFile(CommonUtil.uuid(), ".json");
        FileUtil.writeFile(tempFile.getAbsolutePath(), jsonObject.toJSONString());
        CommonUtil.executeCmd("node /home/<USER>/ec-expoort/frequency.js --input " + tempFile.getAbsolutePath());
        tempFile.delete();
        return visitMapping + "/" + FREQUENCY_FILE_DIR + task.getId() + "/" + picName;
    }

    /**
     * 批量删除
     */
    public void delete(Integer[] ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return;
        }
        freqTaskMapper.delete(ids);
    }

    /**
     * 批量删除
     */
    public void picDelete(Integer[] ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return;
        }
        freqTaskMapper.picDelete(ids);
    }

}
