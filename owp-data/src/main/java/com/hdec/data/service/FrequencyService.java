package com.hdec.data.service;

import biz.source_code.dsp.filter.FilterCharacteristicsType;
import com.alibaba.fastjson.JSON;
import com.hdec.common.domain.AttrCommon;
import com.hdec.common.domain.GraphAnalysis;
import com.hdec.common.util.*;
import com.hdec.common.vo.InstDirectAttr;
import com.hdec.data.domain.FreqTask;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.qo.AccQo;
import com.hdec.data.vo.AccSmallVo;
import com.hdec.data.vo.AccVo;
import com.hdec.data.vo.FrequencyVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.complex.Complex;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 频谱分析业务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FrequencyService {

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private TdService tdService;

    @Autowired
    private TdBaseService tdBaseService;

    /**
     * 加速度数据查询
     */
    public List<AccVo> queryAcc(String fieldNum, AccQo qo) {
        /* 获取频谱分析所用分量名 */
        String attrName = getFrequencyAnalysisAttr(fieldNum);
        log.info("加速度数据所用分量名：{}", attrName);

        return getAccData(qo, attrName);
    }

    /**
     * 获取加速度数据
     */
    public List<AccVo> getAccData(AccQo qo, String attrName) {
        /* 获取仪器信息 */
        InstDirectAttr instInfo = monitorService.getInstDirectAttr(qo.getPointId());

        /* 按名称寻找分析分量 */
        AttrCommon attr = findAnalysisAttrByName(instInfo.getAttrs(), attrName);
        if (ObjectUtils.isEmpty(attr)) {
            return Collections.emptyList();
        }

        /* 查询分析分量数据 */
        int interval = tdService.getSampleInterval(qo.getStartTime(), qo.getEndTime(), 1000);
        String tableName = DataServiceUtil.buildTableName(instInfo.getInstId(), attr.getRate(), qo.getPointId());
        String colName = tdService.enColName(attr.getId(), qo.getDirect());
        String sql = "select last(ts) AS ts, last(" + colName + ") AS " + colName + " from " + tableName + " where ts between '" + qo.getStartTime() + "' and '" + qo.getEndTime() + "' and " + colName + " is not null and del = false INTERVAL(" + interval + "s) limit 1000";
        log.info("获取加速度数据SQL：{}", sql);
        List<Map<String, Object>> records = tdBaseService.selectMulti(sql, attr.getRate());
        if (ObjectUtils.isEmpty(records)) {
            return Collections.emptyList();
        }

        /* 封装数据 */
        List<AccVo> vos = new ArrayList<>();
        for (Map<String, Object> record : records) {
            vos.add(new AccVo((Date) record.get("ts"), (Float) record.get(colName)));
        }
        return vos;
    }

    /**
     * 按名称寻找分析分量
     */
    private AttrCommon findAnalysisAttrByName(List<AttrCommon> attrs, String attrName) {
        if (ObjectUtils.isEmpty(attrs)) {
            return null;
        }

        for (AttrCommon attr : attrs) {
            if (attrName.equals(attr.getName())) {
                return attr;
            }
        }
        return null;
    }

    /**
     * 按名称寻找分析分量
     */
    private AttrCommon findOriginalHighAttr(List<AttrCommon> attrs) {
        if (ObjectUtils.isEmpty(attrs)) {
            return null;
        }

        for (AttrCommon attr : attrs) {
            if ("高频".equals(attr.getRate()) && "原始量".equals(attr.getType())) {
                return attr;
            }
        }
        return null;
    }

    /**
     * 获取该风场频谱分析所用的分量名
     */
    private String getFrequencyAnalysisAttr(String fieldNum) {
        /* 项目级配置 */
        Map<Integer, GraphAnalysis> entMap = monitorService.proGraphAnalysisMap(fieldNum);
        if (entMap != null && entMap.get(1) != null) {
            return entMap.get(1).getAttrName();
        }

        /* 企业级配置 */
        Map<Integer, GraphAnalysis> proMap = monitorService.graphAnalysisMap();
        if (proMap != null && proMap.get(1) != null) {
            return proMap.get(1).getAttrName();
        }
        return null;
    }

    /**
     * 查询小范围加速度数据
     */
    public List<AccSmallVo> querySmallRangeAcc(AccQo qo, String attrName) {
        /* 获取仪器信息 */
        InstDirectAttr instInfo = monitorService.getInstDirectAttr(qo.getPointId());

        /* 按寻找原始高频分量 */
        AttrCommon attr;
        if (attrName != null) {
            attr = findAnalysisAttrByName(instInfo.getAttrs(), attrName);
        } else {
            attr = findOriginalHighAttr(instInfo.getAttrs());
        }
        log.info("小范围加速度所用分量名：{}", attr.getName());
        if (ObjectUtils.isEmpty(attr)) {
            return Collections.emptyList();
        }

        /* 查询分析分量数据 */
        String tableName = DataServiceUtil.buildTableName(instInfo.getInstId(), attr.getRate(), qo.getPointId());
        String colName = tdService.enColName(attr.getId(), qo.getDirect());
        String sql = "select ts, " + colName + " from " + tableName + " where ts between '" + qo.getStartTime() + "' and '" + qo.getEndTime() + "' and " + colName + " is not null and del = false";
        log.info("获取小范围加速度数据SQL：{}", sql);
        List<Map<String, Object>> records = tdBaseService.selectMulti(sql, attr.getRate());
        log.info("records.size：{}", records.size());
        if (ObjectUtils.isEmpty(records)) {
            return Collections.emptyList();
        }

        /* 封装数据 */
        List<AccSmallVo> vos = new ArrayList<>();
        for (Map<String, Object> record : records) {
            vos.add(new AccSmallVo((Date) record.get("ts"), (Float) record.get(colName)));
        }
        return vos;
    }

    /**
     * 分析频谱
     */
    public FrequencyVo analysisFrequency(AccQo qo) {
        /* 查询小范围加速度数据 */
        List<AccSmallVo> vos = this.querySmallRangeAcc(qo, null);
        if (ObjectUtils.isEmpty(vos)) {
            return new FrequencyVo();
        }

        valueSubAvg(vos);

        /* 获取频谱X轴、Y轴数据 */
        List<Double> yArr = getYArr(vos);
        List<Double> xArr = getXArr(vos.size());
        return new FrequencyVo(xArr, yArr.subList(0, yArr.size() / 2));
    }

    /**
     * 分析频谱
     */
    public FrequencyVo analysisFrequency(List<AccSmallVo> vos) {
        /* 查询小范围加速度数据 */
        if (ObjectUtils.isEmpty(vos)) {
            return new FrequencyVo();
        }

        valueSubAvg(vos);

        /* 获取频谱X轴、Y轴数据 */
        List<Double> yArr = getYArr(vos);
        List<Double> xArr = getXArr(vos.size());
        return new FrequencyVo(xArr, yArr.subList(0, yArr.size() / 2));
    }

//    /**
//     * 获取频谱X轴数据
//     */
//    private List<Double> getXArr(int size) {
//        List<Double> xArr = new ArrayList<>();
//        double sRate = 50;
//        double step = sRate / size;
//        for (double i = 0; i <= sRate; i += step) {
//            xArr.add(i);
//        }
//        xArr = xArr.subList(0, xArr.size() / 2);
//        xArr.add(sRate / 2);
//        return xArr;
//    }

    /**
     * 获取频谱X轴数据
     */
    private List<Double> getXArr(int size) {
        List<Double> xArr = new ArrayList<>();
        double sRate = 50;
        BigDecimal bigStep = new BigDecimal(sRate).divide(new BigDecimal(size), 8, RoundingMode.HALF_UP);

        BigDecimal i = new BigDecimal(0d);
        while (i.doubleValue() <= sRate) {
            xArr.add(i.doubleValue());
            i = i.add(bigStep);
        }
        xArr = xArr.subList(0, xArr.size() / 2);
        xArr.add(sRate / 2);
        return xArr;
    }


    /**
     * 获取频谱Y轴数据
     */
    private List<Double> getYArr(List<AccSmallVo> vos) {
        List<Double> yArr = new ArrayList<>();
        Complex[] arr = new Complex[vos.size()];
        for (int i = 0; i < vos.size(); i++) {
            arr[i] = new Complex(vos.get(i).getVal(), 0);
        }
        Complex[] fft = FFTUtil.fft(arr);
        for (int i = 0; i < fft.length; i++) {
            yArr.add(fft[i].abs() / (vos.size() / 2));
        }
        return yArr;
    }

    /**
     * 频谱分析
     */
    public FrequencyVo analysisFrequency(FreqTask task, AccQo qo) {
        // 获取目标时间段内某测点某方向的监测数据
        List<AccSmallVo> vos = querySmallRangeAcc(qo, task.getAttrName());
        if (ObjectUtils.isEmpty(vos)) {
            return new FrequencyVo();
        }

        String dataArrStr = vos.stream().map(e -> String.valueOf(e.getVal())).collect(Collectors.joining(","));

        List<Double> yArr;
        try {
//            // todo 临时
//            FileUtil.writeFile("/home/<USER>/ora_" + System.currentTimeMillis() + ".txt", dataArrStr);

            File tempFile = File.createTempFile(CommonUtil.uuid(), ".txt");
            FileUtil.writeFile(tempFile.getAbsolutePath(), dataArrStr);
            yArr = calcFrequencyByPython(tempFile.getAbsolutePath(), task.getWaveFilter(), getType(task.getFilterType()), String.valueOf(task.getRateLow()), task.getRateHigh() == null ? String.valueOf(task.getRateLow()) : String.valueOf(task.getRateHigh()));
            tempFile.delete();

//            // todo 临时
//            FileUtil.writeFile("/home/<USER>/ora_" + System.currentTimeMillis() + "_out.txt", yArr.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return new FrequencyVo();
        }

        if (ObjectUtils.isEmpty(yArr)) {
            return new FrequencyVo();
        }
        List<Double> xArr = getXArr(yArr.size());
        return new FrequencyVo(xArr, yArr.subList(0, yArr.size() / 2));
    }

    /**
     * 通过python计算频谱
     */
    private static List<Double> calcFrequencyByPython(String inputDataPath, String algType, String filterType, String rateLow, String rateHigh) throws Exception {
        String[] arguments = new String[] {"python3", "/home/<USER>/filter.py", inputDataPath, filterType, rateLow, rateHigh, algType};
        Process proc = Runtime.getRuntime().exec(arguments);
        BufferedReader in = new BufferedReader(new InputStreamReader(proc.getInputStream()));
        String line;
        StringBuilder sb = new StringBuilder();
        while ((line = in.readLine()) != null) {
            sb.append(line);
        }
        in.close();
        proc.waitFor();
        String[] arr = sb.toString().split(",");
        List<Double> res = new ArrayList<>();
        for (String s : arr) {
            if (!ObjectUtils.isEmpty(s) && !"nan".equals(s)) {
                res.add(Double.parseDouble(s));
            }
        }
        return res;
    }

    /**
     * 获取类型
     */
    private String getType(String filterType) {
        if ("高通".equals(filterType)) {
            return "highpass";
        }
        if ("低通".equals(filterType)) {
            return "lowpass";
        }
        return "bandpass";
    }

    /**
     * 数据减去均值
     */
    private void valueSubAvg(List<AccSmallVo> vos) {
        /* 求均值 */
        BigDecimal sum = new BigDecimal(0d);
        int count = 0;
        for (AccSmallVo accVo : vos) {
            if (accVo.getVal() != null) {
                sum = sum.add(new BigDecimal(accVo.getVal()));
                count++;
            }
        }
        float avg = sum.floatValue() / count;

        for (int i = 0; i < vos.size(); i++) {
            vos.get(i).setVal(vos.get(i).getVal() - avg);
        }
    }

}
