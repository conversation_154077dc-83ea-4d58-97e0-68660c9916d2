package com.hdec.data.service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hdec.common.constant.Constant;
import com.hdec.common.constant.RedisKey;
import com.hdec.common.domain.CollectInstMountCommon;
import com.hdec.common.domain.msg.Inst;
import com.hdec.common.util.CommonUtil;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.config.LocalCache;
import com.hdec.data.domain.AttrIdVal;
import com.hdec.data.domain.InsertRecord;
import com.hdec.data.util.RateLimitedQueue;
import com.hdec.data.vo.Val;
import lombok.extern.slf4j.Slf4j;
//import org.apache.kafka.clients.producer.KafkaProducer;
//import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Kafka逻辑类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class KafkaService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ExcelImportService importService;

    @Autowired
    private LocalCache localCache;

    @org.springframework.beans.factory.annotation.Value("${deploy.fieldNum}")
    private String fieldNum;

    /* 自动化采集仪编号固定前缀 */
    private static final String AUTO_INST_PREFIX = "AUTO_";

    /** 入库批次大小 */
    private static final Integer BATCH_SIZE = 5000;

    /** 批次入库数据 */
    private static List<InsertRecord> batchRecords = new ArrayList<>(BATCH_SIZE);

    /** 线程池等待队列大小 */
    private static final Integer QUEUE_SIZE = 10;

    private static final Integer THREAD_NUM = 3;

    /** 线程池 */
    private final static ThreadPoolExecutor threadPool = new ThreadPoolExecutor(THREAD_NUM, THREAD_NUM, 5, TimeUnit.MINUTES, new ArrayBlockingQueue<>(QUEUE_SIZE));

    /**
     * 从主题消费数据
     */
    public synchronized void consume(List<Inst> instList, String type) {
        long ss = System.currentTimeMillis();
        /* 解析数据并获取通道挂载信息 */
        Map<String, CollectInstMountCommon> channelMountsMap = getChannelMountsMapFromCache(RedisKey.AUTO_MOUNT + type + "-" + fieldNum);

        /* 封装数据集 */
        Map<String, Long> lastUpdateTimeMap = new HashMap<>();
        Long nowTs = new Date().getTime();
        List<InsertRecord> records = new ArrayList<>(instList.size());
        for (Inst inst : instList) {
            inst.getChannelData().forEach((channel, values) -> {
                if (channel == null || ObjectUtils.isEmpty(values)) {
                    return;
                }

                /* 获取该采集仪通道下挂载 */
                CollectInstMountCommon mount = channelMountsMap.get(CommonUtil.join(Constant.SPLIT_STR, AUTO_INST_PREFIX + inst.getDeviceNo(), channel + 1));
                if (mount == null) {
//                    System.out.println("mount== null");
                    return;
                }
//                System.out.println("mount:"+mount);

                /* 更新channel_last_update_time */
                lastUpdateTimeMap.put(RedisKey.CHANNEL_LAST_UPDATE_TIME + mount.getCollectInstId() + "_" + (channel + 1), nowTs);

                /* 按挂载信息封装数据 */
                if ("dynamic".equals(type)) {
                    // 动态设备
                    Set<AttrIdVal> attrValues = new HashSet<>(1);
                    AttrIdVal attrIdVal = new AttrIdVal(mount.getAttrIds()[0], values[0]);
                    attrValues.add(attrIdVal);
                    records.add(new InsertRecord(mount.getInstId(), TimeUtil.format2Ms(inst.getTime()), mount.getPointId(), mount.getDirect(), attrIdVal, attrValues, mount.getAttrRates()[0]));
                } else if ("static".equals(type)) {
                    // 静态设备
                    for (int i = 0; i < mount.getAttrIds().length; i++) {
                        Integer[] attrIds = mount.getAttrIds();
                        if (attrIds[i] != null) {
                            Set<AttrIdVal> attrValues = new HashSet<>(1);
                            AttrIdVal attrIdVal = new AttrIdVal(attrIds[i], values[i]);
                            attrValues.add(attrIdVal);
                            InsertRecord record = new InsertRecord(mount.getInstId(), TimeUtil.format2Ms(inst.getTime()), mount.getPointId(), mount.getDirect(), attrIdVal, attrValues, mount.getAttrRates()[i]);
                            log.info(record.toString());
                            records.add(record);
                        }
                    }
                } else {
                    // 雷达设备
                    Integer idx = inst.getDataIndex();
                    if (mount.getAttrIds().length > idx && mount.getAttrIds()[idx] != null) {
                        Set<AttrIdVal> attrValues = new HashSet<>(1);
                        AttrIdVal attrIdVal = new AttrIdVal(mount.getAttrIds()[idx], values[0]);
                        attrValues.add(attrIdVal);
                        InsertRecord record = new InsertRecord(mount.getInstId(), TimeUtil.format2Ms(inst.getTime()), mount.getPointId(), mount.getDirect(), attrIdVal, attrValues, mount.getAttrRates()[idx]);
                        records.add(record);
                    }
                }
            });
        }
        redisTemplate.opsForValue().multiSet(lastUpdateTimeMap);
        if (ObjectUtils.isEmpty(records)) {
            return;
        }

        /* 缓存最新数据 */
        cacheLatestData(records, fieldNum);

        /* 低频数据直接处理 */
        if ("static".equals(type) || "radar".equals(type)) {
            importService.batchSave(records, fieldNum, false);
            return;
        }

        /* 数据批量入库 */
        batchRecords.addAll(records);
        if (batchRecords.size() > BATCH_SIZE) {
            List<InsertRecord> copyBatchRecords = CommonUtil.deepCopy(batchRecords);
            log.info("队列大小：{}", threadPool.getQueue().size());
            try {
                threadPool.submit(() -> {
                    long s = System.currentTimeMillis();
                    importService.batchSave(copyBatchRecords, fieldNum,true);
                    long e = System.currentTimeMillis();
                    log.info("批处理总耗时：{}s\n", (e - s) / 1000);
                });
            } catch (Exception e) {
                log.info("队列已满，提交失败！");
                log.error("队列已满，提交失败！");
            }
            batchRecords.clear();
        }

        long ee = System.currentTimeMillis();
        if (Math.random() > 0.999) {
            log.info("消息时间：{}, 消息数量：{}, 批次耗时：{}ms", TimeUtil.format2Second(instList.get(instList.size() - 1).getTime()), records.size(), (ee - ss));
        }
    }

    /**
     * 从缓存中获取通道挂载信息
     */
    private Map<String, CollectInstMountCommon> getChannelMountsMapFromCache(String key) {
        /* 从本地缓存中获取通道挂载信息 */
        Object localObj = localCache.get(key);
        if (localObj != null) {
            return (Map<String, CollectInstMountCommon>) localObj;
        }

        /* 从Redis中获取通道挂载信息，并将通道挂载信息按采集仪编号和通道分组 */
        Object redisObj = redisTemplate.opsForValue().get(key);
        if (redisObj != null) {
            List<CollectInstMountCommon> instChannelMounts = JSON.parseArray((String) redisObj, CollectInstMountCommon.class);
            Map<String, CollectInstMountCommon> channelMountsMap = instChannelMounts.stream().collect(Collectors.toMap(e -> CommonUtil.join(Constant.SPLIT_STR, e.getCollectInstNo(), e.getChannel()), e -> e));
            localCache.set(key, channelMountsMap, 30, TimeUnit.SECONDS);
            return channelMountsMap;
        }
        return null;
    }

    /**
     * 缓存最新数据(localCache版本)
     */
    private void cacheLatestData(List<InsertRecord> records, String fieldNum) {
        for (InsertRecord record : records) {
            cacheRecord(record);
        }

        List<InsertRecord> resRecords = importService.handleFuncForAuto(fieldNum, records);

        if (!ObjectUtils.isEmpty(resRecords)) {
            for (InsertRecord record : resRecords) {
                cacheRecord(record);
            }
        }
    }

    /**
     * 缓存最新数据
     */
    private void cacheRecord(InsertRecord record) {
        int sizeShort = 2500;
        int sizeLong = 1200;

        String keyShort = RedisKey.LAST_AUTO_DATA + CommonUtil.join("-", record.getPointId(), record.getDirectId(), record.getValue().getAttrId());
        String keyLong = keyShort + "-long";

        /* 缓存短数据 */
        LinkedList<Val> queueShort = localCache.get(keyShort);
        if (queueShort == null) {
            queueShort = new LinkedList<>();
        }
        while (queueShort.size() > sizeShort) {
            queueShort.pollFirst();
        }

        Date curTimeMs = TimeUtil.parse2Ms2(record.getTime());
        if (isNewVal(queueShort.peekLast(), curTimeMs)) {
            queueShort.offerLast(new Val(curTimeMs, record.getValue().getAttrVal()));
        }
        localCache.set(keyShort, queueShort, 1, TimeUnit.MINUTES);

//        // todo 这里分量和方向是反着的
//        if (keyShort.contains("17630-1-3628")) {
//            printQueue(queueShort);
//        }

        /* 缓存长数据 */
        LinkedList<Val> queueLong = localCache.get(keyLong);
        if (queueLong == null) {
            queueLong = new LinkedList<>();
        }
        while (queueLong.size() > sizeLong) {
            queueLong.pollFirst();
        }

        Date curTimes = TimeUtil.parse2Second(record.getTime());
        if (isNewVal(queueLong.peekLast(), curTimes)) {
            queueLong.offerLast(new Val(curTimes, record.getValue().getAttrVal()));
        }
        localCache.set(keyLong, queueLong, 1, TimeUnit.HOURS);
    }

    private void printQueue(LinkedList<Val> queueShort) {
        String keyFlag = "printQueue";
        String flag = localCache.get(keyFlag);
        if (flag != null) {
            return;
        }

        formatAndPrintT(queueShort);
        localCache.set(keyFlag, "printQueue", 10, TimeUnit.SECONDS);
    }

    public static void formatAndPrintT(LinkedList<Val> queueShort) {
        if (queueShort.size() < 10) {
            return;
        }

        log.error("放-------");
        SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm:ss.SSS");
        for (Val val : queueShort) {
            if (val != null && val.getT() != null) {
                log.error(dateFormat.format(val.getT()));
            } else {
                log.error("null");
            }
        }
        log.error("\n");
    }

    /**
     * 判断是否存在于末尾
     */
    private boolean isNewVal(Val lastVal, Date curTime) {
        if (lastVal == null || lastVal.getT() == null || curTime == null) {
            return true;
        }

        if (curTime.getTime() > lastVal.getT().getTime()) {
            return true;
        }
        return false;
    }

//    /**
//     * 缓存最新数据(Redis版本)
//     */
//    private void cacheLatestData(List<InsertRecord> records, long nowTime, String fieldNum) {
//        List<InsertRecord> resRecords = importService.handleFunc2222(fieldNum, records);
//        if (ObjectUtils.isEmpty(resRecords)) {
//            return;
//        }
//
//        long begin = nowTime - 50 * 1000;
//        for (InsertRecord record : resRecords) {
//            String key = RedisKey.LAST_AUTO_DATA + CommonUtil.join("-", record.getPointId(), record.getDirectId(), record.getValue().getAttrId());
//
//            // 添加数据
//            redisTemplate.opsForZSet().add(key, record.getValue().getAttrVal(), TimeUtil.parse2Ms(record.getTime()).getTime());
//
//            // 只存储50s的数据
//            redisTemplate.opsForZSet().removeRangeByScore(key, 0, begin);
//        }
//    }
//    // 查询
//    String dataKey = RedisKey.LAST_AUTO_DATA + CommonUtil.join("-", pointId, directId, attr.getId());
//    Set<ZSetOperations.TypedTuple<Object>> data = redisTemplate.opsForZSet().rangeWithScores(dataKey, 0, -1);
//    List<Val> vs = new ArrayList<>();
//            for (ZSetOperations.TypedTuple<Object> datum : data) {
//        vs.add(new Val(new Date(datum.getScore().longValue()), Float.parseFloat(String.valueOf(datum.getValue()))));
//    }

//    @Autowired
//    private KafkaProducer<String, String> producer;
//
//    /**
//     * 异步发送消息(每秒发送一次)
//     */
//    public void sendMsg(String topicName, String msg) {
//        producer.send(new ProducerRecord<>(topicName, msg));
//    }

}

