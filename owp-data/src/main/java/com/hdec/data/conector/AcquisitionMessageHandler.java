package com.hdec.data.conector;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hdec.common.domain.msg.Inst;
import com.hdec.data.service.DataAccessService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
public class AcquisitionMessageHandler implements MqttMessageHandler {

    private static final Logger logger = LoggerFactory.getLogger(AcquisitionMessageHandler.class);

    private final ObjectMapper mapper = new ObjectMapper();

    @Resource
    private DataAccessService accessService;

    @Override
    public void handleMessage(Message<?> message, Map<String, String> topicMapping) {
        String payload = (String) message.getPayload();
        Object to = message.getHeaders().get(MqttHeaders.RECEIVED_TOPIC);
        if (to == null) {
            logger.warn("河北振闯-MQTT数据-消息中未查询到topic: {}", payload);
            return;
        }
        String topic = to.toString();
        try {
            HbzcMessage hbzcMessage = mapper.readValue(payload, HbzcMessage.class);
            String deviceNo = topicMapping.get(topic);
            if (deviceNo == null) {
                logger.warn("河北振闯-MQTT数据-未找到设备编号: {}", topic);
                return;
            }
            List<Inst> instList = parseToInst(deviceNo, hbzcMessage);
            if (instList != null && !instList.isEmpty()) {
                accessService.input(instList, "dynamic");
            }
        } catch (JsonProcessingException e) {
            logger.error("河北振闯-转换MQTT数据异常: {}", payload, e);
        }
    }


    /**
     * 转换为采集仪数据
     *
     * @param deviceNo 设备编号
     * @param message  数据
     * @return {@link List }<{@link Inst }>
     */
    private List<Inst> parseToInst(String deviceNo, HbzcMessage message) {
        Integer dataLong = message.getDataLong();
        Integer channelSize = message.getChannelSize();
        Date timestamp = message.getTimestamp();
        Integer frequence = message.getFrequence();
        List<HbzcMessage.Channel> channel = message.getChannel();
        if (dataLong == null || channelSize == null ||
                timestamp == null || frequence == null ||
                channel == null || channel.isEmpty()) {
            logger.warn("河北振闯-MQTT数据-数据异常: {}", message);
            return null;
        }
        if (channelSize != channel.size()) {
            logger.warn("河北振闯-MQTT数据-通道数不匹配: {}", message);
            return null;
        }
        List<Inst> instList = new ArrayList<>();
        long startTime = timestamp.getTime();
        long interval = 1000L / frequence;
        for (int di = 0; di < dataLong; di++) {
            long time = startTime + di * interval;
            Inst inst = new Inst();
            inst.setDeviceNo(deviceNo);
            inst.setTime(new Date(time));
            Map<Integer, Float[]> channelMap = new HashMap<>();
            for (int ci = 0; ci < channelSize; ci++) {
                HbzcMessage.Channel ch = channel.get(ci);
                List<Double> data = ch.getData();
                Double value = data.get(di);
                channelMap.put(ci, new Float[]{value.floatValue()});
            }
            inst.setChannelData(channelMap);
            instList.add(inst);
        }
        return instList;
    }
}
