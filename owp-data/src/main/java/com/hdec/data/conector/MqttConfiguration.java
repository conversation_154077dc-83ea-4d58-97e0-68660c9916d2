package com.hdec.data.conector;

import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.beans.BeansException;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.GenericApplicationContext;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.dsl.context.IntegrationFlowContext;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.lang.Nullable;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Set;


@Configuration
@EnableIntegration
@EnableConfigurationProperties({MqttProperties.class})
public class MqttConfiguration implements ApplicationContextAware {

    @Resource
    private MqttProperties properties;

    @Resource
    private MqttMessageHandler messageHandler;

    @Resource
    private IntegrationFlowContext flowContext;

    private GenericApplicationContext context;

    @Override
    public void setApplicationContext(@Nullable ApplicationContext context) throws BeansException {
        this.context = (GenericApplicationContext) context;
    }

    @PostConstruct
    public void initConnector() {
        if (!properties.isEnable()) {
            return;
        }
        List<MqttProperties.Connector> connectors = properties.getConnectors();
        if (connectors != null && !connectors.isEmpty()) {
            for (int i = 0; i < connectors.size(); i++) {
                MqttProperties.Connector connector = connectors.get(i);
                registerMqttBroker(connector, i);
            }
        }
    }

    private void registerMqttBroker(MqttProperties.Connector connector, int index) {
        String factoryBeanName = "ClientFactory" + index;
        String channelBeanName = "InputChannel" + index;
        String adapterBeanName = "Adapter" + index;
        String messageHandlerBeanName = "MessageHandler" + index;
        String flowBeanName = "Flow" + index;

        // 1. 注册 ClientFactory
        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        MqttConnectOptions options = new MqttConnectOptions();
        options.setServerURIs(new String[]{connector.getBroker()});
        options.setUserName(connector.getUsername());
        options.setPassword(connector.getPassword().toCharArray());
        options.setCleanSession(connector.isCleanSession());
        options.setAutomaticReconnect(connector.isAutomaticReconnect());
        options.setConnectionTimeout(connector.getConnectionTimeout());
        options.setKeepAliveInterval(connector.getKeepAliveInterval());
        factory.setConnectionOptions(options);
        context.registerBean(factoryBeanName, MqttPahoClientFactory.class, () -> factory);

        // 2. 注册 InputChannel
        MessageChannel channel = new DirectChannel();
        context.registerBean(channelBeanName, MessageChannel.class, () -> channel);

        // 3. 注册 Adapter
        Set<String> topics = connector.getTopicMapping().keySet();
        MqttPahoMessageDrivenChannelAdapter adapter = new MqttPahoMessageDrivenChannelAdapter(
                connector.getClientId(), factory, topics.toArray(new String[0]));
        adapter.setOutputChannel(channel);
        context.registerBean(adapterBeanName, MqttPahoMessageDrivenChannelAdapter.class, () -> adapter);

        // 4. 注册 MessageHandler
        context.registerBean(messageHandlerBeanName, MessageHandler.class, () ->
                message -> {
                    messageHandler.handleMessage(message, connector.getTopicMapping());
                });

        // 5. 注册 ServiceActivator（编程式）
        IntegrationFlow flow = IntegrationFlows
                .from(channel)
                .handle(context.getBean(messageHandlerBeanName, MessageHandler.class))
                .get();

        flowContext.registration(flow).id(flowBeanName).register();
    }
}
