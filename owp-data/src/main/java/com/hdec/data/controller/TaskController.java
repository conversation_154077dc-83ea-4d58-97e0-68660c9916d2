package com.hdec.data.controller;

import com.github.pagehelper.PageInfo;
import com.hdec.common.domain.ProResourceCommon;
import com.hdec.common.domain.R;
import com.hdec.common.domain.ResourceCommon;
import com.hdec.data.domain.Task;
import com.hdec.data.feign.UserService;
import com.hdec.data.feign.WindService;
import com.hdec.data.qo.CheckQo;
import com.hdec.data.qo.TaskQo;
import com.hdec.data.service.TaskService;
import com.hdec.data.vo.TaskVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 任务管理控制器
 *
 * <AUTHOR>
 */
@Api(tags = "任务管理")
@Slf4j
@Validated
@RestController
@RequestMapping("api/data/task")
public class TaskController {

    @Autowired
    private TaskService taskService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private UserService userService;

    @Autowired
    private WindService windService;

    /**
     * 任务列表
     */
    @ApiOperation("任务列表")
    @PostMapping("list")
    public R list(@RequestHeader("sessionId") String sessionId, @RequestBody TaskQo qo) {
        /* 查询权限范围内风场 */
        List<String> fieldNums = new ArrayList<>(1);
        if (!ObjectUtils.isEmpty(qo.getFieldNum())) {
            fieldNums.add(qo.getFieldNum());
        } else {
            ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
            fieldNums = userService.getFieldResource(resource == null ? 0 : resource.getUnitId());
        }
        if (ObjectUtils.isEmpty(fieldNums)) {
            return R.success(Collections.emptyList());
        }

        /* 查询风场名称 */
        List<ProResourceCommon> fieldNames = windService.getFieldNameByFieldNums(fieldNums);
        Map<String, String> fieldNamesMap = fieldNames.stream().collect(Collectors.toMap(ProResourceCommon::getFieldNum, ProResourceCommon::getName));

        PageInfo<TaskVo> page = new PageInfo<>();
        page.setList(taskService.list(qo, fieldNums, fieldNamesMap, page));
        return R.success(page);
    }

    /**
     * 校验相关任务是否执行完成
     */
    @ApiOperation("校验相关任务是否执行完成")
    @PostMapping("check")
    public R checkIsFinished(@RequestHeader("fieldNum") String fieldNum, @RequestBody CheckQo qo) {
        boolean flag = taskService.checkIsFinished(fieldNum, qo);
        if (flag) {
            return R.success("当前结果可能不准确，请等待任务列表中所有任务均结束后再次查询");
        }
        return R.success();
    }

}
