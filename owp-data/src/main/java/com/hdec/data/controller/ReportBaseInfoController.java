package com.hdec.data.controller;

import com.hdec.common.domain.OperaLog;
import com.hdec.common.domain.R;
import com.hdec.data.domain.ReportBaseInfo;
import com.hdec.data.domain.ReportPerson;
import com.hdec.data.feign.UserService;
import com.hdec.data.service.ReportBaseInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 报告基本信息控制器
 *
 * <AUTHOR>
 */
@Api(tags = "报告基本信息")
@Validated
@RestController
@RequestMapping("api/data/report")
public class ReportBaseInfoController {

    @Autowired
    private ReportBaseInfoService baseInfoService;

    @Autowired
    private UserService userService;

    /**
     * 获取某项目报告基本信息
     */
    @ApiOperation("获取某项目报告基本信息")
    @GetMapping("getBaseInfo")
    public R getBaseInfo(@RequestHeader("fieldNum") String fieldNum) {
        ReportBaseInfo baseInfo = baseInfoService.getBaseInfo(fieldNum);
        return R.success(baseInfo);
    }

    /**
     * 更新某项目报告基本信息
     */
    @ApiOperation("更新某项目报告基本信息")
    @PostMapping("updateBaseInfo")
    public R updateBaseInfo(@RequestBody ReportBaseInfo baseInfo) {
        baseInfoService.updateBaseInfo(baseInfo);
        userService.saveOperaLog(new OperaLog("报告基本信息", "修改", 1));
        return R.success("更新成功");
    }

    /**
     * 获取某项目人员信息
     */
    @ApiOperation("获取某项目人员信息")
    @GetMapping("getPersonConf")
    public R getPersonConf(@RequestHeader("fieldNum") String fieldNum) {
        ReportPerson reportPerson = baseInfoService.getPersonConf(fieldNum);
        return R.success(reportPerson);
    }

    /**
     * 修改某项目人员信息
     */
    @ApiOperation("修改某项目人员信息")
    @PostMapping("updatePersonConf")
    public R updatePersonConf(@RequestHeader("fieldNum") String fieldNum,
                              @RequestBody ReportPerson reportPerson) {
        baseInfoService.updatePersonConf(fieldNum, reportPerson);
        return R.success("修改成功");
    }

}
