package com.hdec.data.controller;

import com.hdec.common.domain.R;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.qo.DataQueryQo;
import com.hdec.data.qo.SubsideConf;
import com.hdec.data.service.SubsideService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Api(tags = "沉降数据")
@Validated
@Slf4j
@RestController
@RequestMapping("api/subside")
public class SubsideController {
    @Resource
    private SubsideService subsideService;


    @ApiOperation("晃点图配置")
    @GetMapping("conf")
    public R getSloshConf(@RequestHeader("fieldNum") String fieldNum) {
        List<SubsideConf> subsideConf = subsideService.getSubsideConf(fieldNum);
        return R.success(subsideConf);
    }

    @ApiOperation("晃点图配置")
    @PostMapping("conf")
    public R setSlosh(@RequestHeader("fieldNum") String fieldNum,
                      @RequestBody List<SubsideConf> conf) {
        subsideService.settingSubsideConf(fieldNum, conf);
        return R.success();
    }

    /**
     * 数据查询(图)
     */
    @ApiOperation("计算沉降")
    @PostMapping("calc")
    public R queryChart(@RequestBody DataQueryQo qo) {
        String start = qo.getStartTime();
        String end = qo.getEndTime();
        Date startTime = TimeUtil.parse2Second(start);
        Date endTime = TimeUtil.parse2Second(end);
        if (startTime == null || endTime == null) {
            return R.error("开始/结束时间不能为空");
        }
        subsideService.calcSubside(startTime, endTime);
        return R.success();
    }
}
