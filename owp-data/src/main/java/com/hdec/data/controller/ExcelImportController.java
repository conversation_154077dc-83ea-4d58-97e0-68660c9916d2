package com.hdec.data.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hdec.common.domain.FormulaCommon;
import com.hdec.common.domain.R;
import com.hdec.data.domain.BatchFilePath;
import com.hdec.data.domain.Import;
import com.hdec.data.domain.ImportUrl;
import com.hdec.data.qo.DataImportQo;
import com.hdec.data.qo.ExcelImportQo;
import com.hdec.data.service.ExcelImportService;
import com.hdec.data.vo.ImportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 数据录入控制器
 *
 * <AUTHOR>
 */
@Api(tags = "数据录入")
@Validated
@RestController
@RequestMapping("api/data/import")
public class ExcelImportController {

    @Autowired
    private ExcelImportService importService;

    /**
     * 导入记录列表
     */
    @ApiOperation("导入记录列表")
    @PostMapping("importList")
    public R importList(@NotBlank(message = "风场编码不允许为空") @RequestHeader("fieldNum") String fieldNum,
                        @RequestHeader(value = "sessionId") String sessionId,
                        @RequestBody DataImportQo qo) {
        PageHelper.startPage(qo.getPageNum(), qo.getPageSize());
        String userId = sessionId.split("_")[2];
        List<Import> imports = importService.importList(fieldNum, userId, qo);
        return R.success(new PageInfo<>(imports));
    }

    /**
     * Excel录入
     */
    @ApiOperation("Excel录入")
    @PostMapping("excel")
    public R excelImport(@RequestHeader("fieldNum") String fieldNum, @RequestHeader(value = "sessionId") String sessionId,
                         @RequestBody ExcelImportQo qo) {
        List<ImportVo> vos = importService.excelImport(fieldNum, sessionId, qo);
        return R.success(vos);
    }

    /**
     * Excel批量录入
     */
    @ApiOperation("Excel批量录入")
    @PostMapping("excelBatchImport")
    public R excelBatchImport(@RequestHeader("fieldNum") String fieldNum, @RequestHeader(value = "sessionId") String sessionId,
                         @RequestBody ExcelImportQo qo) {
        ImportVo vo = importService.excelBatchImport(fieldNum, sessionId, qo);
        return R.success(vo);
    }

    /**
     * 上传Excel数据
     */
    @ApiOperation("上传Excel数据")
    @PostMapping("upload")
    public R upload(@RequestHeader("fieldNum") String fieldNum, @RequestHeader(value = "sessionId") String sessionId,
                    MultipartFile file, Integer id, Integer total, Integer index, String excelName) throws Exception {
        /* 上传文件 */
        importService.upload(fieldNum, file, id, total, index, excelName, sessionId);
        return R.success(index);
    }

    /**
     * 批量删除记录
     */
    @ApiOperation("批量删除记录")
    @DeleteMapping("{ids}")
    public R deleteBatch(@NotNull(message = "请勾选待删除项") @PathVariable Integer[] ids) {
        importService.delete(ids);
        return R.success("删除成功");
    }

    /**
     * 上传Excel数据
     */
    @ApiOperation("上传Excel数据")
    @PostMapping("batchUpload")
    public R batchUpload(@RequestHeader(value = "sessionId") String sessionId, MultipartFile file, Integer importId, String excelName) throws Exception {
        /* 上传文件 */
        importService.batchUpload(file, importId, excelName, sessionId);
        return R.success("上次成功");
    }

    /**
     * 上传Excel数据
     */
    @ApiOperation("上传Excel数据测试")
    @PostMapping("batchUploadTest")
    public R batchUploadTest(MultipartFile file) throws Exception {
        /* 上传文件 */
        importService.batchUploadTest(file);
        return R.success("上次成功");
    }

    /**
     * 判断文件录入是否完成
     */
    @ApiOperation("判断文件录入是否完成")
    @PostMapping("isImportFinished")
    public R isImportFinished(@RequestBody ImportUrl importUrl) {
        Boolean isFinished = importService.isImportFinished(importUrl);
        return R.success(isFinished);
    }

    /**
     * 校验批量录入文件路径
     */
    @ApiOperation("校验批量录入文件路径")
    @PostMapping("checkFilePaths")
    public R checkFilePaths(@RequestHeader("fieldNum") String fieldNum, @RequestBody List<BatchFilePath> filePaths) {
        List<BatchFilePath> paths = importService.checkFilePaths(fieldNum, filePaths);
        return R.success(paths);
    }

    /**
     * 重新计算公式
     */
    @ApiOperation("重新计算公式")
    @PostMapping("reCalc")
    public R reCalc(@RequestHeader("fieldNum") String fieldNum, @RequestHeader("sessionId") String sessionId, @RequestBody List<FormulaCommon> formulas) {
        importService.reCalc(fieldNum, sessionId, formulas);
        return R.success("success");
    }

}
