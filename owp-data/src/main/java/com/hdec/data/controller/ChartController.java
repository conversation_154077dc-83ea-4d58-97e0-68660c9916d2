package com.hdec.data.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hdec.common.domain.R;
import com.hdec.data.domain.Chart;
import com.hdec.data.domain.ChartAttr;
import com.hdec.data.qo.ChartQo;
import com.hdec.data.service.ChartService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 生成过程线控制器
 *
 * <AUTHOR>
 */
@Api(tags = "生成过程线")
@Validated
@RestController
@RequestMapping("api/data/chart")
public class ChartController {

    @Autowired
    private ChartService chartService;

    /**
     * 列表
     */
    @ApiOperation("列表")
    @PostMapping("list")
    public R list(@RequestHeader("fieldNum") String fieldNum, @RequestBody ChartQo qo) {
        PageHelper.startPage(qo.getPageNum(), qo.getPageSize());
        List<Chart> charts = chartService.list(fieldNum);
        return R.success(new PageInfo<>(charts));
    }

    /**
     * 新增
     */
    @ApiOperation("新增")
    @PostMapping("add")
    public R add(@RequestHeader("fieldNum") String fieldNum, @RequestBody @Valid Chart chart) {
        chart.setFieldNum(fieldNum);
        if (!ObjectUtils.isEmpty(chart.getChartAttrs())) {
            chart.setAttrNames(chart.getChartAttrs().stream().map(ChartAttr::getAttrName).collect(Collectors.joining(",")));
        }
        chartService.add(chart);
        return R.success("新增成功");
    }

    /**
     * 预览
     */
    @ApiOperation("预览")
    @PostMapping("preview")
    public R preview(@RequestHeader("fieldNum") String fieldNum, @RequestHeader(value = "sessionId") String sessionId, @RequestBody @Valid Chart chart) {
        chart.setFieldNum(fieldNum);
        if (!ObjectUtils.isEmpty(chart.getChartAttrs())) {
            chart.setAttrNames(chart.getChartAttrs().stream().map(ChartAttr::getAttrName).collect(Collectors.joining(",")));
        }
        chartService.preview(chart, sessionId.split("_")[2]);
        return R.success("预览成功");
    }

    /**
     * 取消预览
     */
    @ApiOperation("取消预览")
    @PostMapping("cancelPreview")
    public R cancelPreview(@RequestHeader(value = "sessionId") String sessionId) {
        chartService.cancelPreview(sessionId.split("_")[2]);
        return R.success("成功取消预览");
    }

    /**
     * 批量删除
     */
    @ApiOperation("批量删除")
    @DeleteMapping("{ids}")
    public R deleteBatch(@PathVariable Integer[] ids) {
        chartService.delete(ids);
        return R.success("删除成功");
    }

}
