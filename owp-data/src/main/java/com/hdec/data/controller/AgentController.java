package com.hdec.data.controller;


import com.hdec.common.domain.R;
import com.hdec.data.service.AgentService;
import com.hdec.data.vo.BkgLogger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 本地采集代理控制器
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Api(tags = "本地采集代理")
@RestController
@RequestMapping("api/agent")
public class AgentController {
    private static final Logger logger = LoggerFactory.getLogger(AgentController.class);

    @Resource
    private AgentService agentService;

    /**
     * 基康仪器BKGLogger
     */
    @ApiOperation("基康仪器BKGLogger")
    @PostMapping("bkg-logger")
    public R bkgLogger( @RequestBody List<BkgLogger> loggers) {
        try {
            agentService.bkgLogger(loggers);
            return R.success();
        } catch (Exception e) {
            logger.error("基康数据Access数据处理异常", e);
            return R.error(e.getMessage());
        }
    }
}
