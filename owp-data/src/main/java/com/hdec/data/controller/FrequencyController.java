package com.hdec.data.controller;

import com.hdec.common.domain.R;
import com.hdec.data.qo.AccQo;
import com.hdec.data.service.FrequencyService;
import com.hdec.data.vo.AccSmallVo;
import com.hdec.data.vo.AccVo;
import com.hdec.data.vo.FrequencyVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 频谱分析控制器
 *
 * <AUTHOR>
 */
@Api(tags = "频谱分析")
@Validated
@RestController
@RequestMapping("api/data/freq")
public class FrequencyController {

    @Autowired
    private FrequencyService frequencyService;

    /**
     * 加速度数据查询
     */
    @ApiOperation("加速度数据查询")
    @PostMapping("queryAcc")
    public R queryAcc(@RequestHeader("fieldNum") String fieldNum, @RequestBody AccQo qo) {
        List<AccVo> vos = frequencyService.queryAcc(fieldNum, qo);
        return R.success(vos);
    }

    /**
     * 小范围加速度数据查询
     */
    @ApiOperation("小范围加速度数据查询")
    @PostMapping("querySmallRangeAcc")
    public R querySmallRangeAcc(@RequestBody AccQo qo) {
        List<AccSmallVo> vos = frequencyService.querySmallRangeAcc(qo, null);
        return R.success(vos);
    }

    /**
     * 频谱查询
     */
    @ApiOperation("频谱查询")
    @PostMapping("queryFreq")
    public R queryFrequency(@RequestBody AccQo qo) {
        FrequencyVo vo = frequencyService.analysisFrequency(qo);
        return R.success(vo);
    }

}
