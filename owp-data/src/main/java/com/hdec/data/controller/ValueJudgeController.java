package com.hdec.data.controller;

import com.alibaba.fastjson.JSON;
import com.hdec.common.constant.Constant;
import com.hdec.common.domain.R;
import com.hdec.common.util.CommonUtil;
import com.hdec.data.qo.ValueJudgeQo;
import com.hdec.data.service.ValueJudgeService;
import com.hdec.data.util.ExcelUtil;
import com.hdec.data.vo.ValueJudgeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 测值评判控制器
 *
 * <AUTHOR>
 */
@Api(tags = "测值评判")
@RestController
@RequestMapping("api/data/valueJudge")
public class ValueJudgeController {

    @Autowired
    private ValueJudgeService judgeService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 测值评判列表
     */
    @ApiOperation("测值评判列表")
    @PostMapping("list")
    public R list(@RequestHeader("fieldNum") String fieldNum, @RequestBody ValueJudgeQo qo) throws Exception {
        List<ValueJudgeVo> list = judgeService.list(fieldNum, qo);
        return R.success(list);
    }

    /**
     * 导出Excel(提交参数)
     */
    @ApiOperation("导出Excel(提交参数)")
    @PostMapping("export")
    public R exportSubmitParam(@RequestHeader("fieldNum") String fieldNum, @RequestBody ValueJudgeQo qo) {
        qo.setFieldNum(fieldNum);

        /* 参数保存至缓存，并设置失效时间 */
        String uuid = CommonUtil.uuid();
        redisTemplate.opsForValue().set(Constant.CACHE_EXPORT_PRE + uuid, JSON.toJSONString(qo), Constant.CACHE_EXPORT_EXPIRE_HOURS, TimeUnit.HOURS);
        return R.success(uuid);
    }

    /**
     * 导出Excel(启动下载)
     */
    @ApiOperation("导出Excel(启动下载)")
    @GetMapping("export/{uuid}")
    public void exportStartDownload(@PathVariable String uuid, HttpServletResponse response) throws Exception {
        /* 从缓存中取出参数 */
        String paramJson = (String) redisTemplate.opsForValue().get(Constant.CACHE_EXPORT_PRE + uuid);
        if (ObjectUtils.isEmpty(paramJson)) {
            response.setContentType("text/html;charset=utf-8");
            response.getWriter().println("链接已失效（" + Constant.CACHE_EXPORT_EXPIRE_HOURS + "小时），请重新导出");
            return;
        }

        ExcelUtil.exportByte(response, "缺测统计表");
        judgeService.exportExcel(JSON.parseObject(paramJson, ValueJudgeQo.class), response);
    }

}
