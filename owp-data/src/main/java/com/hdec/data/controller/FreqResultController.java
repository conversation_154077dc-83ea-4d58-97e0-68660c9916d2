package com.hdec.data.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hdec.common.domain.R;
import com.hdec.data.domain.FreqResult;
import com.hdec.data.qo.FreqChartQo;
import com.hdec.data.qo.FreqResultQo;
import com.hdec.data.service.FreqResultService;
import com.hdec.data.vo.FreqChartVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 频谱分析结果控制器
 *
 * <AUTHOR>
 */
@Api(tags = "频谱分析结果")
@RestController
@RequestMapping("api/data/freqResult")
public class FreqResultController {

    @Autowired
    private FreqResultService freqResultService;

    /**
     * 结果列表
     */
    @ApiOperation("结果列表")
    @PostMapping("list")
    public R list(@RequestBody FreqResultQo qo) {
        PageHelper.startPage(qo.getPageNum(), qo.getPageSize());
        List<FreqResult> results = freqResultService.list(qo);
        return R.success(new PageInfo<>(results));
    }

    /**
     * 结果列表
     */
    @ApiOperation("结果列表")
    @PostMapping("pic/list")
    public R picList(@RequestBody FreqResultQo qo) {
        PageHelper.startPage(qo.getPageNum(), qo.getPageSize());
        List<FreqResult> results = freqResultService.picList(qo);
        return R.success(new PageInfo<>(results));
    }

    /**
     * 删除
     */
    @ApiOperation("删除")
    @DeleteMapping("delete/{id}")
    public R delete(@PathVariable Integer id) {
        freqResultService.delete(id);
        return R.success("删除成功");
    }

    /**
     * 批量删除
     */
    @ApiOperation("批量删除")
    @DeleteMapping("deleteBatch/{ids}")
    public R deleteBatch(@PathVariable Integer[] ids) {
        freqResultService.deleteBatch(ids);
        return R.success("删除成功");
    }

    /**
     * 根据id返回对象
     */
    @GetMapping("/getById")
    public R getById(Integer id) {
        FreqResult result = freqResultService.getById(id);
        return R.success(result);
    }

    /**
     * 甘特图
     */
    @ApiOperation("甘特图")
    @PostMapping("chart")
    public R chart(@RequestHeader("fieldNum") String fieldNum, @RequestBody FreqChartQo qo) {
        List<FreqChartVo> vos = freqResultService.chart(fieldNum, qo.getMonitorId(), qo.getStartTime(), qo.getEndTime());
        return R.success(vos);
    }

    /**
     * 标记
     */
    @ApiOperation("标记")
    @GetMapping("mark/{id}")
    public R mark(@PathVariable Integer id) {
        freqResultService.mark(id);
        return R.success("操作成功");
    }

}
