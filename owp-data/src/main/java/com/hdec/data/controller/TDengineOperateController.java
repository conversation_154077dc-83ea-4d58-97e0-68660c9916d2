package com.hdec.data.controller;


import com.hdec.common.domain.TDengineQuery;
import com.hdec.data.service.TdBaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Api(tags = "TDengine操作")
@Slf4j
@Validated
@RestController
@RequestMapping("api/data/tdengine")
public class TDengineOperateController {
    @Resource
    private TdBaseService tdBaseService;

    @ApiOperation("查询")
    @PostMapping("select")
    public List<Map<String, Object>> select(@RequestBody @NotNull TDengineQuery query) throws Exception {
        String sql = query.getSql();
        String rate = query.getRate();
        if (sql == null || sql.isEmpty() || rate == null || rate.isEmpty()) {
            throw new Exception("sql或rate为空");
        }
        return tdBaseService.selectMulti(sql, rate);
    }
}
