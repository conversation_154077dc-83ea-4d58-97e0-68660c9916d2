package com.hdec.data.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hdec.common.constant.Constant;
import com.hdec.common.domain.AttrCommon;
import com.hdec.common.domain.R;
import com.hdec.common.util.CommonUtil;
import com.hdec.common.util.TimeUtil;
import com.hdec.common.vo.InstDirectAttr;
import com.hdec.common.vo.ProcessLineVo;
import com.hdec.data.domain.Datum;
import com.hdec.data.domain.ExportTask;
import com.hdec.data.qo.*;
import com.hdec.data.service.DataService;
import com.hdec.data.util.ExcelUtil;
import com.hdec.data.vo.DataChartVo;
import com.hdec.data.vo.DataTableVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.text.ParseException;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 数据查询控制器
 *
 * <AUTHOR>
 */
@Api(tags = "数据查询")
@Validated
@Slf4j
@RestController
@RequestMapping("api/data/dataManager")
public class DataController {

    @Autowired
    private DataService dataService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 数据查询(表)
     */
    @ApiOperation("数据查询(表)")
    @PostMapping("query/table")
    public R queryTable(@RequestHeader("fieldNum") String fieldNum, @RequestBody DataQueryQo qo) {
        qo.setFieldNum(fieldNum);
        DataTableVo vo = dataService.queryTable(qo);
        return R.success(vo);
    }

    /**
     * 数据查询(小表)
     */
    @ApiOperation("数据查询(小表)")
    @PostMapping("query/minTable")
    public R queryMinTable(@RequestHeader("fieldNum") String fieldNum, @RequestBody DataQueryMinQo qo) {
        qo.setFieldNum(fieldNum);
        R r = dataService.queryMinTable(qo);
        return r;
    }

    /**
     * 数据查询(图)
     */
    @ApiOperation("数据查询(图)")
    @PostMapping("query/chart")
    public R queryChart(@NotBlank(message = "风场编码不允许为空") @RequestHeader("fieldNum") String fieldNum,
                        @RequestBody DataQueryQo qo) throws Exception {
        List<DataChartVo> dataChartVos = dataService.queryChart(qo.getPointIds(), qo.getPointNos(),
                qo.getAttrIds(), qo.getAttrNames(), qo.getStartTime(), qo.getEndTime(), qo.getApproval(), qo.getMin(), qo.getMax(), qo.getRate());
        return R.success(dataChartVos);
    }

    /**
     * 查询自动化设备最新数据
     */
    @ApiOperation("查询自动化设备最新数据")
    @PostMapping("query/latestData/{userId}")
    public R queryAutoLatestData(@PathVariable("userId") String userId, @RequestBody LatestDataQo qo) {
        dataService.queryAutoLatestData(userId, qo);
        return R.success("操作成功");
    }

    /**
     * 导出Excel(提交参数)
     */
    @ApiOperation("导出Excel")
    @PostMapping("export/table")
    public R exportTable(@RequestHeader("fieldNum") String fieldNum, @RequestBody DataQueryQo qo) {
        qo.setFieldNum(fieldNum);

        /* 参数保存至缓存，并设置失效时间 */
        String uuid = CommonUtil.uuid();
        redisTemplate.opsForValue().set(Constant.CACHE_EXPORT_PRE + uuid, JSON.toJSONString(qo), Constant.CACHE_EXPORT_EXPIRE_HOURS, TimeUnit.HOURS);
        return R.success(uuid);
    }

    /**
     * 导出Excel(启动下载)
     */
    @ApiOperation("导出Excel")
    @GetMapping("export/table/{uuid}")
    public void exportTableDown(@PathVariable String uuid, HttpServletResponse response) throws Exception {
        /* 从缓存中取出参数 */
        String paramJson = (String) redisTemplate.opsForValue().get(Constant.CACHE_EXPORT_PRE + uuid);
        if (ObjectUtils.isEmpty(paramJson)) {
            response.setContentType("text/html;charset=utf-8");
            response.getWriter().println("链接已失效（" + Constant.CACHE_EXPORT_EXPIRE_HOURS + "小时），请重新导出");
            return;
        }

        ExcelUtil.exportByte(response, "数据查询");
        dataService.exportTable(JSON.parseObject(paramJson, DataQueryQo.class), response);
    }

    /**
     * 导出任务列表
     */
    @ApiOperation("导出任务列表")
    @GetMapping("exportTask/list/{pageNum}/{pageSize}")
    public R exportTaskList(@RequestHeader("fieldNum") String fieldNum, @PathVariable("pageNum") Integer pageNum, @PathVariable("pageSize") Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<ExportTask> tasks = dataService.exportTaskList(fieldNum);
        return R.success(new PageInfo<>(tasks));
    }

    /**
     * 新增导出任务
     */
    @ApiOperation("新增导出任务")
    @PostMapping("exportTask/add")
    public R exportTaskList( @RequestHeader(value = "sessionId") String sessionId, @RequestHeader("fieldNum") String fieldNum, @RequestBody ExportTask task) {
        try {
            long days = TimeUtil.daysBetween(TimeUtil.completeStart(task.getStartTime()), TimeUtil.completeEnd(task.getEndTime()));
            if (Constant.RATE_HIGH.equals(task.getRate())) {
                if (Math.abs(days) > 32) {
                    return R.error("高频数据时间跨度不能超过一个月");
                }
                if (task.getPointIds().size() > 10) {
                    return R.error("高频数据单次测点个数不能超过10个");
                }
            } else {
                if (Math.abs(days) > 93) {
                    return R.error("小时级和分钟级数据时间跨度不能超过3个月");
                }
                if (task.getPointIds().size() > 100) {
                    return R.error("小时级和分钟级单次测点个数不能超过100个");
                }
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        task.setFieldNum(fieldNum);
        dataService.exportTaskAdd(sessionId, task);
        return R.success("添加成功");
    }

    /**
     * 删除导出任务
     */
    @ApiOperation("删除导出任务")
    @DeleteMapping("exportTask/del/{id}")
    public R exportTaskList(@PathVariable Integer id) {
        dataService.exportTaskDel(id);
        return R.success("删除成功");
    }

    /**
     * 告警过程线
     */
    @ApiOperation("告警过程线")
    @PostMapping("alarm/processLine")
    public R alarmProcessLine(@RequestHeader("fieldNum") String fieldNum, @RequestBody ProcessLineVo vo) throws ParseException {
        vo.setFieldNum(fieldNum);
        List<Datum> data = dataService.alarmProcessLine(vo);
        List<Datum> sortedData = data.stream().sorted((a, b) -> (int) (a.getTime().getTime() - b.getTime().getTime())).collect(Collectors.toList());
        return R.success(sortedData);
    }

    /**
     * 批量审核
     */
    @ApiOperation("批量审核")
    @PostMapping("verifyBatch/{rate}")
    public R verifyBatch(@RequestHeader("fieldNum") String fieldNum, @RequestHeader(value = "sessionId") String sessionId, @RequestBody VerifyQo[] qo, @PathVariable String rate) throws ParseException {
        R r = dataService.verifyBatch(sessionId, fieldNum, qo, rate);
        return r;
    }

    /**
     * 删除数据
     */
    @ApiOperation("删除数据")
    @DeleteMapping("del/{direct}")
    public R del(@RequestHeader("fieldNum") String fieldNum, @RequestHeader(value = "sessionId") String sessionId, @RequestBody List<VerifyQo> qos, @PathVariable Integer direct, HttpServletRequest request) throws ParseException {
        R r = dataService.del(sessionId, fieldNum, qos, direct);
        return r;
    }

    /**
     * 查询当前项目是否有批量删除任务
     */
    @ApiOperation("查询当前项目是否有批量删除任务")
    @DeleteMapping("getCurBatchDelTask")
    public R getCurBatchDelTask(@RequestHeader("fieldNum") String fieldNum) {
        BatchDelQo qo = dataService.getCurBatchDelTask(fieldNum);
        return R.success(qo);
    }

    /**
     * 批量删除
     */
    @ApiOperation("批量删除")
    @DeleteMapping("batchDel")
    public R batchDel(@RequestHeader("fieldNum") String fieldNum, @RequestHeader(value = "sessionId") String sessionId, @RequestBody BatchDelQo qo) throws InterruptedException, ParseException {
        long days = TimeUtil.daysBetween(qo.getStartTime(), qo.getEndTime());
        if (days > 90) {
            return R.error("一次性删除请不要超过90天");
        }
        R r = dataService.batchDelNew(sessionId, fieldNum, qo);
        return r;
    }

    /**
     * 编辑数据
     */
    @ApiOperation("编辑数据")
    @PostMapping("update")
    public R update(@RequestHeader("fieldNum") String fieldNum, @RequestHeader(value = "sessionId") String sessionId, HttpServletRequest request, @RequestBody VerifyQo qo) throws Exception {
        if (qo.getNewVal() == null || Math.abs(qo.getNewVal()) > Integer.MAX_VALUE) {
            return R.error("值" + qo.getNewVal() + "越界");
        }

        R r = dataService.update(sessionId, fieldNum, qo, request);
        return r;
    }

    /**
     * 还原数据
     */
    @ApiOperation("还原数据")
    @PostMapping("returnBack")
    public R returnBack(@RequestHeader("fieldNum") String fieldNum, @RequestHeader(value = "sessionId") String sessionId, @RequestBody VerifyQo qo, HttpServletRequest request) throws Exception {
        R r = dataService.returnBack(sessionId, fieldNum, qo, request);
        return r;
    }

    /**
     * 获取测点最新成果量数据
     */
    @ApiOperation("获取测点最新成果量数据")
    @GetMapping("pointLastAchieveData/{pointId}")
    public R pointLastAchieveData(@PathVariable Integer pointId) {
        R r = dataService.pointLastAchieveData(pointId);
        return r;
    }

    /**
     * 由测点ID获取测点绑定仪器的ID、方向、属性
     */
    @ApiOperation("由测点ID获取测点绑定仪器的ID方向属性")
    @GetMapping("getInstDirectAttrByRate/{pointId}/{rate}")
    public R getInstDirectAttr(@PathVariable Integer pointId, @PathVariable String rate) {
        InstDirectAttr attr = dataService.getInstDirectAttr(pointId, rate);
        return R.success(attr);
    }

    /**
     * 创建超级表
     */
    @ApiOperation("创建超级表")
    @PostMapping("createOrModifySTable/{instId}/{instDirect}")
    public void createOrModifySTable(@RequestHeader("fieldNum") String fieldNum, @PathVariable Integer instId, @PathVariable String instDirect, @RequestBody List<AttrCommon> attrs) {
        dataService.createOrModifySTable(fieldNum, instId, instDirect, attrs);
    }

    /**
     * 由测点ID获取支持查询的分量信息
     */
    @ApiOperation("由测点ID获取支持查询的分量信息")
    @GetMapping("getQueryAttrByPoint/{pointId}")
    public R getQueryAttrByPoint(@RequestHeader("fieldNum") String fieldNum, @PathVariable Integer pointId) {
        InstDirectAttr attr = dataService.getQueryAttrByPoint(fieldNum, pointId);
        return R.success(attr);
    }

}
