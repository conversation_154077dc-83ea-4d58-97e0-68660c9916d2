package com.hdec.data.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hdec.common.domain.R;
import com.hdec.data.domain.template.ReportCover;
import com.hdec.data.qo.ReportCoverQo;
import com.hdec.data.service.ReportCoverService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 报告封面控制器
 *
 * <AUTHOR>
 */
@Api(tags = "报告封面管理")
@Validated
@Slf4j
@RestController
@RequestMapping("api/data/cover")
public class ReportCoverController {

    @Autowired
    private ReportCoverService coverService;

    /**
     * 报告封面列表
     */
    @ApiOperation("报告封面列表")
    @PostMapping("list")
    public R list(@RequestHeader("fieldNum") String fieldNum, @RequestBody ReportCoverQo qo) {
        PageHelper.startPage(qo.getPageNum(), qo.getPageSize());
        List<ReportCover> covers = coverService.list(fieldNum, qo);
        return R.success(new PageInfo<>(covers));
    }

    /**
     * 报告获取某个封面详情
     */
    @ApiOperation("获取某个封面详情")
    @GetMapping("detail/{id}")
    public R detail(@PathVariable Integer id) {
        ReportCover cover = coverService.detail(id);
        return R.success(cover);
    }

    /**
     * 判断封面是否需要填写期数
     */
    @ApiOperation("判断封面是否需要填写期数")
    @GetMapping("isNeedIssue/{id}")
    public R isNeedIssue(@PathVariable Integer id) {
        List<String> msgs = coverService.isNeedIssue(id);
        return R.success(msgs);
    }

    /**
     * 更新报告封面
     */
    @ApiOperation("更新封面")
    @PostMapping("update")
    public R update(@RequestHeader("fieldNum") String fieldNum, @RequestBody ReportCover cover) {
        cover.setFieldNum(fieldNum);
        Integer id = coverService.update(cover);
        if (id == -1) {
            return R.error("封面名称已存在");
        }
        return R.success("修改成功");
    }

    /**
     * 新增报告封面
     */
    @ApiOperation("新增封面")
    @PostMapping("add")
    public R add(@RequestHeader("fieldNum") String fieldNum, @RequestBody ReportCover cover, @RequestHeader("sessionId") String sessionId) {
        cover.setFieldNum(fieldNum);
        Integer id = coverService.add(cover, sessionId);
        if (id == -1) {
            return R.error("封面名称已存在");
        }
        return R.success(id);
    }

    /**
     * 批量删除报告封面
     */
    @ApiOperation("批量删除封面")
    @DeleteMapping("{ids}")
    public R deleteBatch(@RequestHeader("fieldNum") String fieldNum, @PathVariable Integer[] ids) {
        return coverService.delete(fieldNum, ids);
    }

    /**
     * 封面预览
     */
    @ApiOperation("封面预览")
    @GetMapping("coverPreview/{coverId}")
    public String coverPreview(@PathVariable Integer coverId) throws Exception {
        return coverService.coverPreview(coverId);
    }

}
