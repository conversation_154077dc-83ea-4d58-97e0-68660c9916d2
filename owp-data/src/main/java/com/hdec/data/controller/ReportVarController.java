package com.hdec.data.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hdec.common.domain.R;
import com.hdec.data.domain.template.ReportVar;
import com.hdec.data.domain.template.ReportVarVo;
import com.hdec.data.qo.CustomVarQo;
import com.hdec.data.service.ReportVarService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 报告变量控制器
 *
 * <AUTHOR>
 */
@Api(tags = "报告变量")
@Slf4j
@Validated
@RestController
@RequestMapping("api/data/var")
public class ReportVarController {

    @Autowired
    private ReportVarService varService;

    /**
     * 报告变量列表
     */
    @ApiOperation("报告变量列表")
    @GetMapping("list")
    public R list() {
        ReportVarVo vo = varService.list();
        return R.success(vo);
    }

    /**
     * 某风场下自定义变量列表
     */
    @ApiOperation("某风场下自定义变量列表")
    @PostMapping("customList")
    public R customList(@RequestHeader("fieldNum") String fieldNum, @RequestBody CustomVarQo qo) {
        PageHelper.startPage(qo.getPageNum(), qo.getPageSize());
        List<ReportVar> vars = varService.customList(fieldNum);
        return R.success(new PageInfo<>(vars));
    }

    /**
     * 保存自定义变量
     */
    @ApiOperation("保存自定义变量")
    @PostMapping("save")
    public R save(@RequestHeader("fieldNum") String fieldNum, @RequestBody ReportVar var) {
        if ("所有风机".equals(var.getName()) || "典型风机".equals(var.getName()) || "非典型风机".equals(var.getName()) ||  "风机编号".equals(var.getName()) ||  "升压站".equals(var.getName())) {
            return R.error("该变量名为系统关键字");
        }
        if (var.getName().contains("@") || var.getName().contains("{") || var.getName().contains("}")) {
            return R.error("自定义变量名不允许包含 @{} 等系统保留符号");
        }

        String type = StringUtils.isEmpty(var.getType()) ? "custom" : var.getType();
        var.setType(type);
        var.setFieldNum(fieldNum);
        if (!ObjectUtils.isEmpty(varService.select(var))) {
            return R.error("变量名称已存在");
        }

        varService.save(var);
        return R.success("保存成功");
    }

    /**
     * 修改自定义变量
     */
    @ApiOperation("修改自定义变量")
    @PostMapping("update")
    public R update(@RequestHeader("fieldNum") String fieldNum, @RequestBody ReportVar var) {
        var.setFieldNum(fieldNum);
        varService.update(var);
        return R.success("修改成功");
    }

    /**
     * 删除
     */
    @ApiOperation("删除")
    @DeleteMapping("{ids}")
    public R deleteBatch(@PathVariable Integer[] ids) {
        varService.delete(ids);
        return R.success("删除成功");
    }

}
