package com.hdec.data.relay;

import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.core.MessageProducer;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.support.DefaultPahoMessageConverter;
import org.springframework.integration.mqtt.support.MqttMessageConverter;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.converter.ByteArrayMessageConverter;

import javax.annotation.Resource;


@Configuration
@EnableIntegration
@EnableConfigurationProperties({RelayMqttProperties.class})
@ConditionalOnProperty(value = "relay.mqtt.enable", havingValue = "true", matchIfMissing = false)
public class RelayMqttConfiguration {

    @Resource
    private RelayMqttProperties properties;

    /**
     * 连接选项
     */
    @Bean("relayMqttConnectOptions")
    public MqttConnectOptions relayMqttConnectOptions() {
        MqttConnectOptions options = new MqttConnectOptions();
        options.setServerURIs(new String[]{properties.getBroker()});
        options.setUserName(properties.getUsername());
        options.setPassword(properties.getPassword().toCharArray());
        options.setCleanSession(properties.isCleanSession());
        options.setAutomaticReconnect(properties.isAutomaticReconnect());
        options.setConnectionTimeout(properties.getConnectionTimeout());
        options.setKeepAliveInterval(properties.getKeepAliveInterval());
        return options;
    }

    /**
     * 客户端工厂
     */
    @Bean("relayMqttClientFactory")
    public MqttPahoClientFactory relayMqttClientFactory(@Qualifier("relayMqttConnectOptions")MqttConnectOptions options) {
        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        factory.setConnectionOptions(options);
        return factory;
    }

    @Bean("relayMqttOutboundChannel")
    public MessageChannel relayMqttOutboundChannel() {
        return new DirectChannel();
    }

    /**
     * 入站通道（接收消息）
     *
     * @param factory        factory
     * @param inboundChannel inbound channel
     * @return {@link MessageProducer }
     */
    @Bean("relayInboundMessageProducer")
    public MessageProducer relayInboundMessageProducer(@Qualifier("relayMqttClientFactory") MqttPahoClientFactory factory,
                                                       @Qualifier("relayMqttOutboundChannel") MessageChannel inboundChannel,
                                                       @Qualifier("relayMqttMessageConverter") MqttMessageConverter converter) {
        MqttPahoMessageDrivenChannelAdapter adapter =
                new MqttPahoMessageDrivenChannelAdapter(properties.getClientId() + "_in", factory, "sensor/#");
        adapter.setConverter(converter);
        adapter.setCompletionTimeout(5000);
        adapter.setQos(1);
        adapter.setOutputChannel(inboundChannel);
        return adapter;
    }


    @Bean("relayMqttMessageConverter")
    public MqttMessageConverter relayMqttMessageConverter() {
        DefaultPahoMessageConverter converter = new DefaultPahoMessageConverter();
        converter.setPayloadAsBytes(true);
        return converter;
    }
}
