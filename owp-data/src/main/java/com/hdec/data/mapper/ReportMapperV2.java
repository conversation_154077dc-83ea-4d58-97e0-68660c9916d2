package com.hdec.data.mapper;

import com.hdec.data.domain.report2.Report;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报告管理Dao版本2
 *
 * <AUTHOR>
 */
@Mapper
public interface ReportMapperV2 {

    /**
     * 报告列表
     */
    List<Report> list(@Param("fieldNum") String fieldNum);

    /**
     * 新增报告
     */
    void add(@Param("report") Report report);

    /**
     * 批量删除报告
     */
    void delete(@Param("ids") Integer[] ids);

    /**
     * 更新报告
     */
    void update(@Param("report") Report report);

    /**
     * 查询月报期数
     */
    String selectMonthReportIssue(@Param("fieldNum") String fieldNum, @Param("year") String year);

    /**
     * 更新进度
     */
    void updateStatus(@Param("reportId") Integer reportId, @Param("process") String process);
}
