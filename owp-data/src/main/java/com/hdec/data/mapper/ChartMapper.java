package com.hdec.data.mapper;

import com.hdec.data.domain.Chart;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/7
 */
@Mapper
public interface ChartMapper {

    List<Chart> list(@Param("fieldNum") String fieldNum);

    void add(@Param("chart") Chart chart);

    void updateStatus(@Param("id") Integer id, @Param("status") String status, @Param("url") String url);

    /**
     * 批量删除
     */
    void delete(@Param("ids") Integer[] ids);
}
