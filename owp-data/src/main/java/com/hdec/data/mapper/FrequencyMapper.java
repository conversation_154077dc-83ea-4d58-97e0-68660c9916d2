package com.hdec.data.mapper;

import com.hdec.data.qo.AccQo;
import com.hdec.data.vo.AccVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 频谱分析Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface FrequencyMapper {

    /**
     * 加速度数据查询
     */
    List<AccVo> queryAcc(@Param("fieldNum") String fieldNum, @Param("qo") AccQo qo);

}
