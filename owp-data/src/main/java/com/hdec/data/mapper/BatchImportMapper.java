package com.hdec.data.mapper;

import com.hdec.data.domain.BatchImportCfg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 批量导入Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BatchImportMapper {

    /**
     * 查找配置
     */
    BatchImportCfg selectCfg(@Param("type") Integer type, @Param("fieldNum") String fieldNum);

    /**
     * 查找配置
     */
    List<BatchImportCfg> fieldCfg(@Param("fieldNum") String fieldNum);

    /**
     * 保存配置
     */
    void saveCfg(BatchImportCfg cfg);

    /**
     * 删除配置
     */
    void deleteCfg(BatchImportCfg cfg);

    /**
     * 批量更新
     */
    void updateList(@Param("configs") List<BatchImportCfg> configs);
}