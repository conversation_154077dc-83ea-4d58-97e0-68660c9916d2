package com.hdec.data.mapper;

import com.hdec.data.domain.Datum;
import com.hdec.data.domain.ExportTask;
import com.hdec.data.qo.VerifyQo;
import com.hdec.data.vo.DatumVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.joda.time.DateTime;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface DataMapper {

    /**
     * 判断表是否存在
     */
    Integer isTabExists(@Param("tabName") String tabName);

    /**
     * 创建数据表
     */
    void createTabIfNotExist(@Param("tabName") String tabName);

    /**
     * 插入数据
     */
    void save(@Param("tabName") String tabName, @Param("data") Datum data);

    /**
     * 批量插入数据
     */
    void saveBatch(@Param("tabName") String tabName, @Param("data") List<Datum> data);

    /**
     * 中间结果量数据查询
     */
    List<Datum> queryTableByPoint(@Param("fieldNum") String fieldNum,
                                  @Param("pointId") Integer pointId,
                                  @Param("limitSize") Integer limitSize,
                                  @Param("startTime") String startTime,
                                  @Param("endTime") String endTime,
                                  @Param("orderBy") Integer orderBy);

    List<String> getTimes(@Param("fieldNum") String fieldNum,
                          @Param("pointId") Integer pointId,
                          @Param("limitSize") Integer limitSize,
                          @Param("startTime") String startTime,
                          @Param("endTime") String endTime);

    List<Datum> getValByTime(@Param("fieldNum") String fieldNum,
                             @Param("pointId") Integer pointId,
                             @Param("time") Date time);

    /**
     * 告警过程线
     */
    List<Datum> alarmProcessLine(@Param("fieldNum") String fieldNum, @Param("pointId") Integer pointId,
                                 @Param("direction") Integer direction, @Param("attrId") Integer attrId,
                                 @Param("time") String time, @Param("sTime") String sTime, @Param("eTime") String eTime);

    List<Datum> getData(@Param("fieldNum") String fieldNum,
                        @Param("pointId") Integer pointId,
                        @Param("time") String time);

    /**
     * 按表名和分量ID查询数据
     */
    List<Datum> selectDataByAttr(@Param("tabName") String tabName,
                    @Param("attrIds") Integer[] attrIds,
                    @Param("startTime") String startTime,
                    @Param("endTime") String endTime);

    /**
     * 审核
     */
    void verify(@Param("fieldNum") String fieldNum, @Param("qo") VerifyQo qo);

    /**
     * 删除
     */
    void del(@Param("fieldNum") String fieldNum, @Param("qo") VerifyQo qo);

    /**
     * 编辑
     */
    void update(@Param("fieldNum") String fieldNum, @Param("qo") VerifyQo qo);

    /**
     * 还原
     */
    void returnBack(@Param("fieldNum") String fieldNum, @Param("qo") VerifyQo qo);


    List<DatumVo> queryChart();

    /**
     * 导出任务列表
     */
    List<ExportTask> exportTaskList(@Param("fieldNum") String fieldNum);

    /**
     * 新增导出任务
     */
    void exportTaskAdd(@Param("task") ExportTask task);

    /**
     * 新增导出任务
     */
    void updateExportTaskProgress(@Param("taskId") Integer taskId, @Param("progress") String progress);

    /**
     * 删除导出任务
     */
    void exportTaskDel(@Param("id") Integer id);
}
