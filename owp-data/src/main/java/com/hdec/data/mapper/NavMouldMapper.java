package com.hdec.data.mapper;

import com.hdec.data.qo.MouldVal;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 大纲导航和模板关联Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface NavMouldMapper {

    /**
     * 删除某导航下所有关联关系
     */
    void deleteByNavId(@Param("navId") Integer navId);

    /**
     * 由模板ID获取绑定的大纲ID
     */
    List<Integer> getOutlineByMould(@Param("mouldId") Integer MouldId);

    /**
     * 查看模板是否已经绑定到导航
     */
    Integer getByNavMould(@Param("navId") Integer navId, @Param("mouldId") Integer mouldId);

    /**
     * 模板绑定到导航
     */
    void addMould2navId(@Param("navId") Integer navId, @Param("mouldId") Integer mouldId, @Param("fanVal") String fanVal);

    /**
     * 模板移除导航
     */
    void removeMould2Nav(@Param("navId") Integer navId, @Param("mouldId") Integer mouldId);

    /**
     * 获取大纲内模板的复用变量值
     */
    MouldVal getNavMouldValue(@Param("navId") Integer navId, @Param("mouldId") Integer mouldId);

    /**
     * 为大纲内的模板设置值
     */
    void setMouldValue(@Param("mouldVal") MouldVal mouldVal);

    MouldVal selectRefInfo(@Param("navId") Integer navId, @Param("mouldId") Integer mouldId);

    /**
     * 删除所有引用了某模板的变量
     */
    void delByRefId(@Param("mouldId") Integer mouldId);

    List<MouldVal> getNavMouldByRefId(@Param("mouldId") Integer mouldId);

    List<MouldVal> list(@Param("outlineId") Integer outlineId);

    List<MouldVal> getNavMouldValueByMouldId(@Param("mouldId") Integer mouldId);

    void addNavMould(@Param("mould") MouldVal mould);

    void updateRef(@Param("id") Integer id, @Param("ref") String ref);
}
