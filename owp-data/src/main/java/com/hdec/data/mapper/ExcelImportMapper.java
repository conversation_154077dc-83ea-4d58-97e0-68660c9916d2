package com.hdec.data.mapper;

import com.hdec.data.domain.Import;
import com.hdec.data.domain.ImportUrl;
import com.hdec.data.qo.DataImportQo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ExcelImportMapper {

    /**
     * 导入记录列表
     */
    List<Import> importList(@Param("fieldNum") String fieldNum,
                            @Param("userId") String userId,
                            @Param("qo") DataImportQo qo);

    /**
     * 保存导入记录
     */
    void saveImport(@Param("imp") Import imp);

    /**
     * 更新导入记录
     */
    void updateFilepath(@Param("imp") Import imp);

    /**
     * 更新导入记录
     */
    void updateFilePaths(@Param("id") Integer id, @Param("mappings") String mappings);

    /**
     * 由ID获取导入记录
     */
    Import getImportById(@Param("id") Integer id);

    /**
     * 设置完成
     */
    void setFinish(@Param("id") Integer id, @Param("errFilepath") String errFilepath);

    /**
     * 批量删除记录
     */
    void delete(@Param("ids") Integer[] ids);

    /**
     * 保存批量录入文件路径
     */
    void saveImportUrls(@Param("urls") List<ImportUrl> urls);

    /**
     * 判断文件录入是否完成
     */
    Boolean isImportFinished(@Param("importId") Integer importId, @Param("url") String url);

    List<ImportUrl> getImportUrlById(@Param("importId") Integer importId);

    List<ImportUrl> getImportUrlsByIds(@Param("importIds") List<Integer> importIds);

    void updateBatchUrl(@Param("importUrl") ImportUrl importUrl);

    Boolean getNotFinishedImports(@Param("importId") Integer importId);

}
