package com.hdec.data.mapper;

import com.hdec.data.domain.Outline;
import com.hdec.data.domain.OutlineNav;
import com.hdec.data.domain.template.Mould;
import com.hdec.data.qo.MouldVal;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报告大纲Dao层
 *
 * <AUTHOR>
 */
@Mapper
public interface ReportOutlineMapper {

    /**
     * 大纲导航列表
     */
    List<OutlineNav> navList(@Param("outlineId") Integer outlineId);

    /**
     * 按名称和父节点查询
     */
    List<OutlineNav> selectNavByNameAndPid(@Param("name") String name,
                                           @Param("pid") Integer pid,
                                           @Param("outlineId") Integer outlineId,
                                           @Param("fieldNum") String fieldNum,
                                           @Param("id") Integer id);

    /**
     * 按ID获取
     */
    OutlineNav getNavById(@Param("id") Integer id);

    /**
     * 新增大纲导航
     */
    void addNav(@Param("fieldNum") String fieldNum, @Param("nav") OutlineNav nav);

    /**
     * 批量新增大纲导航
     */
    void addNavs(@Param("fieldNum") String fieldNum, @Param("navs") List<OutlineNav> navs);

    /**
     * 修改大纲导航
     */
    void updateNav(@Param("nav") OutlineNav nav);

    /**
     * 删除大纲导航
     */
    void navDelete(@Param("id") Integer id);

    /**
     * 获取某节点的子节点
     */
    List<OutlineNav> getChildrenById(@Param("id") Integer id);

    /**
     * 大纲列表
     */
    List<Outline> list(@Param("fieldNum") String fieldNum, @Param("name") String name,
                       @Param("type") String type, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 新增大纲
     */
    void add(@Param("outline") Outline outline);

    /**
     * 按ID获取大纲
     */
    Outline getById(@Param("id") Integer id);

    /**
     * 修改大纲
     */
    void update(@Param("outline") Outline outline);

    /**
     * 批量删除大纲
     */
    void delete(@Param("ids") Integer[] ids);

    /**
     * 模板添加到大纲
     */
    void addMould2Outline(@Param("outlineId") Integer outlineId, @Param("mouldId") Integer mouldId);

    /**
     * 模板移除大纲
     */
    void removeMould2Outline(@Param("outlineId") Integer outlineId, @Param("mouldId") Integer mouldId);

    /**
     * 为大纲内的模板设置值
     */
    void setMouldValue(@Param("mouldVal") MouldVal mouldVal);

    /**
     * 获取大纲内模板的复用变量值
     */
    String getMouldValue(@Param("outlineId") Integer outlineId, @Param("mouldId") Integer mouldId);

    /**
     * 获取导航下所有模板
     */
    List<Mould> getMouldByNavId(@Param("navId") Object navId);

    Integer getMouldByOutlineMould(@Param("outlineId") Integer outlineId, @Param("mouldId") Integer mouldId);

    void deleteNavByOutline(@Param("outlineId") Integer outlineId);

    /**
     * 获取导航树表最大ID
     */
    Integer getMaxNavId();

    /**
     * 获取该大纲下的所有一级导航
     */
    List<OutlineNav> getRootNavsByOutlineId(@Param("outlineId") Integer outlineId);

    /**
     * 风场下所有大纲
     */
    List<Outline> listByField(@Param("fieldNum") String fieldNum);

    List<Mould> getNavMouldContentsByOutline(@Param("outlineId") Integer outlineId);

    void updateOrder(@Param("pid") Integer pid, @Param("order") Integer order);

    Integer getMaxOrder(@Param("pid") Integer pid);

    List<Integer> getSameMouldNavIds(@Param("navId") Integer navId);

    /**
     * 通过标题ID获取所属大纲下的所有标题
     */
    List<OutlineNav> navListByNavId(@Param("navId") Integer navId);
}
