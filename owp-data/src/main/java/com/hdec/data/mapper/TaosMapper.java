package com.hdec.data.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hdec.common.formula.AttrVal;
import com.hdec.data.domain.DailyCount;
import com.hdec.data.domain.RedoJob;
import com.hdec.data.domain.taos.Record;
import com.hdec.data.domain.taos.TableColumn;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
@DS("tdengine")
public interface TaosMapper {
    /**
     * 插入sql
     *
     * @param sql sql
     */
    void insertSql(@Param("sql") String sql);

    /**
     * 查询sql
     *
     * @param sql sql
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     */
    List<Record> selectSql(@Param("sql") String sql);

    /**
     * 创建数据库
     */
    void createDatabase(@Param("database") String database);

    /**
     * 查看表
     *
     * @param database 数据库
     * @param table    表名
     */
    List<String> showStables(@Param("database") String database, @Param("table") String table);

    /**
     * 查看表
     *
     * @param database 数据库
     * @param table    表名
     */
    List<TableColumn> tablesDescribe(@Param("database") String database, @Param("table") String table);

    /**
     * 创建表
     *
     * @param database 数据库
     * @param table    表名
     * @param columns  列
     */
    void createStable(@Param("database") String database, @Param("table") String table,
                      @Param("columns") List<TableColumn> columns, @Param("tags") List<TableColumn> tags);

    /**
     * 添加字段
     *
     * @param database 数据库
     * @param table    表
     * @param column   列明
     * @param type     类型
     */
    void addColumn(@Param("database") String database, @Param("table") String table,
                   @Param("column") String column, @Param("type") String type);

    /**
     * 添加字段
     *
     * @param database 数据库
     * @param table    表
     * @param column   列明
     */
    void deleteColumn(@Param("database") String database, @Param("table") String table, @Param("column") String column);

    /**
     * 创建日统计任务超级表
     */
    void createDailyCountJobStable(@Param("database") String database);

    /**
     * 创建重做任务超级表
     */
    void createRedoJobStable(@Param("database") String database);

    /**
     * 提交重做任务
     *
     * @param jobs jobs
     */
    void commitRedoJob(@Param("database") String database, @Param("type") Integer type, @Param("jobs") List<RedoJob> jobs);

    /**
     * 查询未重做任务
     *
     * @param type           类型
     * @param createInterval 创建间隔（单位：秒）
     * @return {@link List }<{@link RedoJob }>
     */
    List<RedoJob> selectUndoTask(@Param("database") String database, @Param("type") Integer type, @Param("createInterval") Integer createInterval);

    /**
     * 完成重做任务
     *
     * @param type type
     * @param jobs jobs
     */
    void completeRedoJob(@Param("database") String database, @Param("type") Integer type, @Param("jobs") List<RedoJob> jobs);

    /**
     * 查询日统计数量
     *
     * @param inst      仪器
     * @param point     测点
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link Long }
     */
    Long selectDailyCount(@Param("database") String database,
                          @Param("inst") Integer inst, @Param("point") Integer point,
                          @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 插入日统计信息
     *
     * @param dailyCounts 日数量
     */
    void insectDailyCount(@Param("database") String database, @Param("dailyCounts") List<DailyCount> dailyCounts);

    /**
     * 查询数据量
     *
     * @param points    测点ids
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return long
     */
    Long selectDataCount(@Param("database") String database, @Param("points") List<Integer> points,
                         @Param("startTime") Date startTime, @Param("endTime") Date endTime);


    /**
     * 查询采样数据
     *
     * @param database  数据库
     * @param table     表
     * @param point     测点
     * @param columns   列名
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param interval  interval
     * @return {@link List }<{@link Map }<{@link String },{@link Object }>>
     */
    List<Record> selectSampleData(@Param("database") String database, @Param("table") String table,
                                  @Param("point") Integer point, @Param("columns") List<String> columns,
                                  @Param("startTime") Date startTime, @Param("endTime") Date endTime,
                                  @Param("interval") String interval);

    /**
     * 查询分量值
     *
     * @param table     表名
     * @param point     测点
     * @param column    列
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link List }<{@link AttrVal }>
     */
    List<AttrVal> selectAttrVal(@Param("table") String table, @Param("point") Integer point,
                                @Param("column") String column,
                                @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询同时刻分量值
     *
     * @param table  表名
     * @param point  测点
     * @param column 列
     * @param ts     时间戳
     * @return {@link AttrVal }
     */
    AttrVal selectAttrValAtSameTime(@Param("table") String table, @Param("point") Integer point,
                                    @Param("column") String column, @Param("ts") Date ts);

    /**
     * 查询时间范围内最近值
     *
     * @param table     表名
     * @param point     测点
     * @param column    列
     * @param ts        时间戳
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link AttrVal }
     */
    AttrVal selectAttrValAtScopeTime(@Param("table") String table, @Param("point") Integer point,
                                     @Param("column") String column, @Param("ts") Date ts,
                                     @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
