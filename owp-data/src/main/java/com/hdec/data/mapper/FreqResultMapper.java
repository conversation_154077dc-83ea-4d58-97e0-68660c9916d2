package com.hdec.data.mapper;

import com.hdec.data.domain.FreqResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FreqResultMapper {

    /**
     * 结果列表
     */
    List<FreqResult> list(@Param("pointId") Integer pointId,
                          @Param("directId") Integer directId,
                          @Param("startTime") String startTime,
                          @Param("endTime") String endTime);

    /**
     * 结果列表
     */
    List<FreqResult> picList(@Param("pointId") Integer pointId,
                          @Param("directId") Integer directId,
                          @Param("startTime") String startTime,
                          @Param("endTime") String endTime);

    /**
     * 根据id返回对象
     */
    FreqResult getById(@Param("id") Integer id);

    List<FreqResult> getByIds(int[] ids);

    /**
     * 保存频谱结果
     */
    int save(FreqResult freqResult);

    int update(FreqResult owpFreqResult);

    int delete(Integer id);

    /**
     * 按条件删除频谱分析结果
     */
    void delByCondition(FreqResult freqResult);

    /**
     * 按测点ID查询存在频谱结果的时段
     */
    List<FreqResult> selectTimesByPoints(@Param("pointIds") List<Integer> pointIds, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 标记
     */
    void mark(@Param("id") Integer id);

    String getByPoint(@Param("pointId") Integer pointId, @Param("direct") Integer direct, @Param("startTime") String startTime, @Param("endTime") String endTime);
}