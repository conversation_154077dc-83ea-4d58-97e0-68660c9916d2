package com.hdec.data.mapper;

import com.hdec.data.domain.v2.ExportTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ExportTaskMapper {

    List<ExportTask> page(@Param("task") ExportTask task);

    ExportTask selectNextTask();


    ExportTask selectById(@Param("id") Integer id);

    int insert(ExportTask task);

    int delete(@Param("id") Integer id);

    int updateProgress(@Param("id") Integer id, @Param("progress") Double progress);

    int updateUrl(@Param("id") Integer id, @Param("url") String url);

    int updateStatus(@Param("id") Integer id, @Param("status") Integer status, @Param("description") String description);

    int exportStartUpdate(@Param("id") Integer id);

    int exportEndUpdate(@Param("id") Integer id);
}
