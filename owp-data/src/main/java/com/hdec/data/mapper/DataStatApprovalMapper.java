package com.hdec.data.mapper;

import com.hdec.data.domain.Stat;
import com.hdec.data.qo.StatQo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据审批统计Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DataStatApprovalMapper {

    /**
     * 按时间、测点清空统计数据
     */
    void deleteByPointDirect(@Param("fieldNum") String fieldNum,
                             @Param("day") String day,
                             @Param("pointId") Integer pointId);

    /**
     * 批量保存
     */
    void saveBatch(@Param("fieldNum") String fieldNum,
                   @Param("statList") List<Stat> statList);

    /**
     * 查询统计数据
     */
    List<Stat> select(@Param("qo") StatQo qo);

}
