package com.hdec.data.mapper;

import com.hdec.data.domain.Report;
import com.hdec.data.qo.ReportQo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报告Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ReportMapper {

    /**
     * 获取某项目报告基本信息
     */
    List<Report> proList(@Param("fieldNum") String fieldNum, @Param("qo") ReportQo qo);

    /**
     * 企业级报告列表
     */
    List<Report> entList(@Param("qo") ReportQo qo, @Param("fieldNums") List<String> fieldNums);

    /**
     * 保存报告
     */
    void save(@Param("report") Report report);

    /**
     * 更新报告状态
     */
    void updatePath(@Param("reportId") Integer reportId, @Param("status") String status,
                      @Param("filepath") String filepath, @Param("pdfPath") String pdfPath);

    void updateStatus(@Param("reportId") Integer reportId, @Param("status") String status);

    /**
     * 上传最终稿月报
     */
    void finalReport(@Param("reportId") Integer reportId, @Param("filepath") String filepath);

    /**
     * 批量删除报告
     */
    void delete(@Param("ids") Integer[] ids);

    /**
     * 获取所有期数
     */
    List<String> entAllIssueNums(@Param("fieldNums") List<String> fieldNums);

    Integer proIssueNumByYear(@Param("fieldNum") String fieldNum, @Param("issueYear") Integer issueYear);
}
