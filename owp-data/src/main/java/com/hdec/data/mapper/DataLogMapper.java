package com.hdec.data.mapper;

import com.hdec.data.domain.DataLog;
import com.hdec.data.qo.DataLogQo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据日志Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DataLogMapper {

    /**
     * 日志列表
     */
    List<DataLog> list(@Param("fieldNum") String fieldNum, @Param("qo") DataLogQo qo);

    /**
     * 保存日志
     */
    void save(@Param("log") DataLog log);

    /**
     * 删除日志
     */
    void delById(@Param("id") Integer id);

    /**
     * 主键查询日志
     */
    DataLog getById(@Param("id") Integer id);

}