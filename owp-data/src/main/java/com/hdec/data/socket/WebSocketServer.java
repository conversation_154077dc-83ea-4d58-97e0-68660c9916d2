package com.hdec.data.socket;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocket推动类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ServerEndpoint("/websocket/{sessionId}")
public class WebSocketServer {

    /** 记录当前在线连接数（静态变量，且应线程安全） */
    private static int onlineCount = 0;

    /** 记录当前WebSocket对象 */
    public static ConcurrentHashMap<String, Session> webSocketMap = new ConcurrentHashMap<>();

    /**
     * 建立连接
     */
    @OnOpen
    public void onOpen(@PathParam("sessionId") String sessionId, Session session) {
        String userId = sessionId.split("_")[2];
        if (!webSocketMap.containsKey(userId)) {
            addOnlineCount();
        }
        webSocketMap.put(userId, session);
//        log.info("用户{}建立连接，当前连接数为:{}", userId, getOnlineCount());
        this.sendMessage(session, "连接成功");
    }

    /**
     * 关闭连接
     */
    @OnClose
    public void onClose(@PathParam("sessionId") String sessionId) {
        String userId = sessionId.split("_")[2];
        if (webSocketMap.containsKey(userId)) {
            webSocketMap.remove(userId);
            subOnlineCount();
        }
//        log.info("用户{}关闭连接，当前连接数为:{}", userId, getOnlineCount());
    }

    /**
     * 收到客户端消息
     */
    @OnMessage
    public void onMessage(@PathParam("sessionId") String sessionId, String message, Session session) {
        // 网络ping-pong
        if ("hello".equals(message)) {
            this.sendMessage(session, "world");
            return;
        }

    }

    @OnError
    public void onError(Session session, Throwable error) {
//        log.error("WebSocket发生错误，错误信息为：" + error.getMessage());
//        error.printStackTrace();
    }

    /**
     * 向客户端推送消息
     */
    public boolean sendMessage(Session session, String message) {
        try {
            session.getBasicRemote().sendText(message);
            return true;
        } catch (IOException e) {
            log.error("消息 {} 发送失败" + e.getCause());
            return false;
        }
    }

    /**
     * 获得此时在线人数
     */
    public static synchronized int getOnlineCount() {
        return onlineCount;
    }

    /**
     * 在线人数加1
     */
    public static synchronized void addOnlineCount() {
        onlineCount++;
    }

    /**
     * 在线人数减1
     */
    public static synchronized void subOnlineCount() {
        onlineCount--;
    }

}
