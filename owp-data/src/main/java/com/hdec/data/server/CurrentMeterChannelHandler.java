package com.hdec.data.server;

import com.hdec.data.util.AadiUtil;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.SimpleChannelInboundHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.SocketAddress;

@ChannelHandler.Sharable
public class CurrentMeterChannelHandler extends ChannelInboundHandlerAdapter {
    private static final Logger logger = LoggerFactory.getLogger(CurrentMeterChannelHandler.class);

    private static final String mistaken = new String(new byte[]{(byte) 0x13, (byte) 0x11});

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        ByteBuf buf = (ByteBuf) msg;
        byte[] bytes = new byte[buf.readableBytes()];
        buf.readBytes(bytes);
        try {
            String message = new String(bytes).replace(mistaken, "");
            logger.info("海流计数据: {}", message);
            String json = AadiUtil.mappingJson(message);
            logger.info("海流计解析后数据: {}", json);
        } catch (Exception e) {
            logger.error("解析海流计数据异常: {}", msg, e);
        }
    }


    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        SocketAddress address = ctx.channel().remoteAddress();
        logger.info("海流计连接开启: {}", address.toString());
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        SocketAddress address = ctx.channel().remoteAddress();
        logger.info("海流计连接关闭: {}", address.toString());
        super.channelInactive(ctx);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        SocketAddress address = ctx.channel().remoteAddress();
        logger.error("海流计连接异常: {}", address.toString(), cause);
        super.exceptionCaught(ctx, cause);
    }

}
