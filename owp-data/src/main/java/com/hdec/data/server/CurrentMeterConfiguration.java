package com.hdec.data.server;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.DelimiterBasedFrameDecoder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties({CurrentMeterProperties.class})
@ConditionalOnProperty(value = "server.current-meter.enable", havingValue = "true", matchIfMissing = false)
public class CurrentMeterConfiguration {
    @Bean("currentMeterAcceptorEventLoopGroup")
    public NioEventLoopGroup acceptorEventLoopGroup(CurrentMeterProperties properties) {
        return new NioEventLoopGroup(properties.getAcceptor());
    }


    @Bean("currentMeterWorkerEventLoopGroup")
    public NioEventLoopGroup workerEventLoopGroup(CurrentMeterProperties properties) {
        return new NioEventLoopGroup(properties.getWorker());
    }

    @Bean
    public CurrentMeterChannelHandler currentMeterChannelHandler() {
        return new CurrentMeterChannelHandler();
    }

    @Bean("currentMeterServerBootstrap")
    public ServerBootstrap serverBootstrap(CurrentMeterProperties properties,
                                           @Qualifier("currentMeterAcceptorEventLoopGroup") NioEventLoopGroup acceptorEventLoopGroup,
                                           @Qualifier("currentMeterWorkerEventLoopGroup") NioEventLoopGroup workerEventLoopGroup,
                                           CurrentMeterChannelHandler channelHandler) {
        ServerBootstrap serverBootstrap = new ServerBootstrap();
        serverBootstrap
                /*  指定使用的线程组  */
                .group(acceptorEventLoopGroup, workerEventLoopGroup)
                /*  指定使用的通道  */
                .channel(NioServerSocketChannel.class)
                /*  指定连接超时时间  */
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, properties.getTimeout())
                .childHandler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    protected void initChannel(SocketChannel channel) throws Exception {
                        ChannelPipeline pipeline = channel.pipeline();
                        pipeline.addLast(new DelimiterBasedFrameDecoder(10000, true,
                                Unpooled.wrappedBuffer(new byte[]{0x0D, 0x0A})));
                        pipeline.addLast(channelHandler);
                    }
                }); // 指定worker处理器
        return serverBootstrap;
    }

}
