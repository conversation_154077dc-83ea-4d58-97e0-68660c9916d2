package com.hdec.data.server;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.nio.NioEventLoopGroup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;

@Component
@ConditionalOnProperty(value = "server.current-meter.enable", havingValue = "true", matchIfMissing = false)
public class CurrentMeterServer {
    private static final Logger logger = LoggerFactory.getLogger(CurrentMeterServer.class);

    @Resource
    private CurrentMeterProperties properties;

    @Resource(name = "currentMeterServerBootstrap")
    private ServerBootstrap serverBootstrap;

    @Resource(name = "currentMeterAcceptorEventLoopGroup")
    private NioEventLoopGroup acceptorEventLoopGroup;

    @Resource(name = "currentMeterWorkerEventLoopGroup")
    private NioEventLoopGroup workerEventLoopGroup;


    /**
     * 开机启动
     *
     * @throws InterruptedException 中断异常
     */
    @PostConstruct
    public void start() throws InterruptedException {
        // 绑定端口启动
        serverBootstrap.bind(properties.getPort()).sync();
        logger.info("Current Meter server started on port(s): {} (socket)", properties.getPort());
    }

    /**
     * 关闭线程池
     */
    @PreDestroy
    public void close() {
        logger.info("Current Meter server shutdown on port(s): {} (socket)", properties.getPort());
        acceptorEventLoopGroup.shutdownGracefully();
        workerEventLoopGroup.shutdownGracefully();
    }
}
