package com.hdec.data.runner;

import com.alibaba.fastjson.JSON;
import com.hdec.common.domain.AttrCommon;
import com.hdec.common.domain.PointCommon;
import com.hdec.common.domain.ProResourceCommon;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.cache.Cache;
import com.hdec.data.domain.AttrIdVal;
import com.hdec.data.domain.InsertRecord;
import com.hdec.data.domain.Redo;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.feign.WindService;
import com.hdec.data.mapper.DataStatMapper;
import com.hdec.data.mapper.TaskMapper;
import com.hdec.data.netty.*;
import com.hdec.data.service.*;
import com.hdec.data.socket.WebSocketServer;
import com.hdec.data.vo.ProcessVo;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.websocket.Session;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class AppRunner implements ApplicationRunner {

    @Autowired
    private WebSocketServer webSocketServer;

    @Autowired
    private TaskService taskService;

    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private HighTaskService highTaskService;

    @Autowired
    private NettyServer nettyServer;

    @Autowired
    private NettyServerForStatic nettyServerForStatic;

    @Autowired
    private NettyServerForRadar nettyServerForRadar;

    @Autowired
    private NettyServerForDynamicV2Data v2Data;

    @Autowired
    private NettyServerForDynamicV2Ctrl v2Ctrl;

    @Autowired
    private NettyServerForDynamicV2DataHis v2DataHis;

    @Override
    public void run(ApplicationArguments args) {
        /* 消息推送 */
        new Thread(() -> pushProcess()).start();

        /* 统计任务（缺测和测值统计的） */
//        statLoop();

        /* 自动化设备数据接收服务、统计自动化任务 */
        dataServer();
    }

    /**
     * 自动化设备数据接收服务
     */
    private void dataServer() {
        /* 动态设备 */
        new Thread(() -> {
            try {
                nettyServer.startServer();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }).start();
        /* 静态设备 */
        new Thread(() -> {
            try {
                nettyServerForStatic.startServer();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }).start();
        /* 波浪雷达 */
        new Thread(() -> {
            try {
                nettyServerForRadar.startServer();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }).start();
        /* 动态V2 */
        new Thread(() -> {
            try {
                v2Ctrl.startServer();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }).start();
        new Thread(() -> {
            try {
                v2Data.startServer();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }).start();
        new Thread(() -> {
            try {
                v2DataHis.startServer();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }).start();
    }

    /**
     * 开启统计任务
     */
    private void statLoop() {
        /* 将任务状态重置后分别开启快慢线程处理任务 */
        taskMapper.resetTaskStatus();
        log.info("统计任务开始");

        int threadCount = 4;
        for (int i = 0; i < threadCount; i++) {
            new Thread(() -> taskService.fastLoop()).start();
//            new Thread(() -> taskService.slowLoop()).start();
        }
        int slowThreadCount = 8;
        for (int i = 0; i < slowThreadCount; i++) {
            new Thread(() -> taskService.slowLoop()).start();
        }
    }

    /**
     * 开启统计任务
     */
    private void statAutoLoop() {
        /* 将任务状态重置后分别开启快慢线程处理任务 */
        highTaskService.resetTaskStatus();

        new Thread(() -> highTaskService.fastLoop()).start();
        new Thread(() -> highTaskService.slowLoop()).start();
    }

    public static float randomFloat(int min, int max) {
        return (float) (Math.random() * (max - min) + min);
    }

    /**
     * 给前端推送进度
     */
    private void pushProcess() {
        while (true) {
            /* 没有推送任务，睡眠半秒 */
            if (Cache.map.size() == 0 && !Cache.hasAlarmFlag) {
                try {
                    TimeUnit.MILLISECONDS.sleep(500);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }

            /* 推送导入进度 */
            if (Cache.map.size() > 0) {
                for (Iterator<Map.Entry<String, Double>> it = Cache.map.entrySet().iterator(); it.hasNext(); ) {
                    Map.Entry<String, Double> en = it.next();
                    String toPushUserId = en.getKey().split("-")[0];
                    Session session = WebSocketServer.webSocketMap.get(toPushUserId);
                    if (session != null) {
                        Double process = en.getValue();
                        List<ProcessVo> vos = new ArrayList<>(1);
                        ProcessVo vo = new ProcessVo(Integer.parseInt(en.getKey().split("-")[1]), Math.floor(process) + "", 1);
                        vos.add(vo);
                        webSocketServer.sendMessage(session, JSON.toJSONString(vos));
                        if (process >= 100) {
                            it.remove();
                        }
                    }
                }
            }

            /* 推送告警 */
            if (Cache.hasAlarmFlag) {
                List<ProcessVo> vos = new ArrayList<>(1);
                ProcessVo vo = new ProcessVo(2);
                vos.add(vo);
                WebSocketServer.webSocketMap.forEach((userId, session) -> webSocketServer.sendMessage(session, JSON.toJSONString(vos)));
            }
            // 所有session都推送完成了再将flag重置
            Cache.hasAlarmFlag = false;

            // 这里有必要睡眠的
            try {
                Thread.sleep(300);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }
}
