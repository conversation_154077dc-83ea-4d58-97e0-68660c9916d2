package com.hdec.data.runner;

import com.hdec.common.domain.AttrCommon;
import com.hdec.common.domain.InstCommon;
import com.hdec.common.domain.PointCommon;
import com.hdec.common.domain.ProResourceCommon;
import com.hdec.common.util.DataServiceUtil;
import com.hdec.common.util.TimeUtil;
import com.hdec.common.vo.InstDirectAttr;
import com.hdec.data.domain.RedoParam;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.feign.WindService;
import com.hdec.data.mapper.DataStatMapper;
import com.hdec.data.service.TaskService;
import com.hdec.data.service.TdBaseService;
import com.hdec.data.service.TdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RunnerService {

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private WindService windService;

    @Autowired
    private TdService tdService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private DataStatMapper statMapper;

    @Autowired
    private TdBaseService tdBaseService;

    /**
     * 创建高频超级表
     */
    public void createHighSTable() {
        List<ProResourceCommon> resources = windService.allFiled();
        for (ProResourceCommon resource : resources) {
            /* 获取该风场下所有仪器和分量 */
            List<InstCommon> fieldInsts = monitorService.allInstByField(resource.getFieldNum());
            List<AttrCommon> fieldAttrs = monitorService.allAttrByField(resource.getFieldNum());
            if (ObjectUtils.isEmpty(fieldInsts) || ObjectUtils.isEmpty(fieldAttrs)) {
                continue;
            }

            for (InstCommon inst : fieldInsts) {
                // 获取该仪器下的高频分量
                List<AttrCommon> highAttrs = filterHighAttr(inst.getId(), fieldAttrs);
                if (!ObjectUtils.isEmpty(highAttrs)) {
                    tdService.createOrModifySTable(resource.getFieldNum(), inst.getId(), inst.getDirect(), highAttrs);
                }
            }
            System.out.println(resource);
        }
    }

    /**
     * 修正超级表（删除以前的d1和d2字段然后新建）
     */
    public void modifySTables() {
        List<ProResourceCommon> resources = windService.allFiled();
//        List<ProResourceCommon> resources = new ArrayList<>();
//        resources.add(new ProResourceCommon("owp00041", ""));

        for (ProResourceCommon resource : resources) {
            /* 获取该风场下所有仪器和分量 */
            List<InstCommon> fieldInsts = monitorService.allInstByField(resource.getFieldNum());
            List<AttrCommon> fieldAttrs = monitorService.allAttrByField(resource.getFieldNum());
            if (ObjectUtils.isEmpty(fieldInsts) || ObjectUtils.isEmpty(fieldAttrs)) {
                continue;
            }

            for (InstCommon inst : fieldInsts) {
                // 获取该仪器下的非高频分量
                List<AttrCommon> attrs = filterNonHighAttr(inst.getId(), fieldAttrs);
                if (!ObjectUtils.isEmpty(attrs)) {
                    tdService.modifySTables(inst.getId(), inst.getDirect(), attrs);
                }
            }
        }
    }

    /**
     * 筛选高频分量
     */
    private List<AttrCommon> filterHighAttr(Integer instId, List<AttrCommon> fieldAttrs) {
        return fieldAttrs.stream().filter(e -> "高频".equals(e.getRate()) && instId.equals(e.getInstId())).collect(Collectors.toList());
    }

    /**
     * 筛选非高频分量
     */
    private List<AttrCommon> filterNonHighAttr(Integer instId, List<AttrCommon> fieldAttrs) {
        return fieldAttrs.stream().filter(e -> !"高频".equals(e.getRate()) && instId.equals(e.getInstId())).collect(Collectors.toList());
    }

    /**
     * 数据统计
     */
    public void dataStat(List<ProResourceCommon> fields) {
        for (ProResourceCommon field : fields) {
            String fieldNum = field.getFieldNum();
            log.info("开始处理风场：{}", fieldNum);
            long s = System.currentTimeMillis();

            /* 删除该风场下所有统计表 */
            List<String> fieldStatTableNames = statMapper.getStatTableNamesByField(fieldNum);
            for (String statTableName : fieldStatTableNames) {
                statMapper.dropTableByName(statTableName);
                log.info("删除表：{}", statTableName);
            }

            /* 获取该风场下所有测点 */
            List<PointCommon> points = monitorService.getPointsByFieldNum(fieldNum);
            if (ObjectUtils.isEmpty(points)) {
                continue;
            }

            int j = 0;
            for (PointCommon point : points) {
                InstDirectAttr info = monitorService.getInstAttrs(point.getId());
                if (info.getInstId() == null || ObjectUtils.isEmpty(info.getRateAttrs())) {
                    continue;
                }

                info.getRateAttrs().forEach((rate, attrs) -> {
                    /* 查询该测点存在数据的天 */
                    Set<String> days = getDataExistDays(info.getInstId(), rate, point.getId());

                    // 将天按月分组
                    Map<String, List<String>> monthMap = days.stream().collect(Collectors.groupingBy(e -> e.substring(0, 7)));
                    monthMap.forEach((month, monthDays) -> {
                        List<RedoParam> monthParams = new ArrayList<>(monthDays.size());
                        for (String monthDay : monthDays) {
                            monthParams.add(new RedoParam(point.getId(), TimeUtil.parse2Day(monthDay)));
                        }
                        taskService.doTaskByDays(fieldNum, rate, monthParams, "");
                    });
                });
                log.info("风场：{}的测点进度：{}/{}", fieldNum, ++j, points.size());
            }
            long e = System.currentTimeMillis();
            log.info("风场{}处理完成，耗时：{}", fieldNum, TimeUtil.second2Human((int) ((e - s) / 1000)));
        }
    }

    /**
     * 返回该测点存在数据的天（去重）
     */
    private Set<String> getDataExistDays(Integer instId, String rate, Integer pointId) {
        String tableName = DataServiceUtil.buildTableName(instId, rate, pointId);
        String sql = "select ts from " + tableName + " where del = false";
        List<Map<String, Object>> records = tdBaseService.selectMulti(sql, rate);
        if (ObjectUtils.isEmpty(records)) {
            return Collections.emptySet();
        }

        Set<String> set = new HashSet<>(records.size());
        for (Map<String, Object> record : records) {
            set.add(TimeUtil.format2Day((Date) record.get("ts")));
        }
        return set;
    }

    /**
     * 返回该测点存在数据的天（去重）
     */
    private Set<String> getDataExistMonths(Integer instId, String rate, Integer pointId) {
        String tableName = DataServiceUtil.buildTableName(instId, rate, pointId);
        String sql = "select ts from " + tableName + " where del = false";
        List<Map<String, Object>> records = tdBaseService.selectMulti(sql, rate);
        if (ObjectUtils.isEmpty(records)) {
            return Collections.emptySet();
        }

        Set<String> set = new HashSet<>(records.size());
        for (Map<String, Object> record : records) {
            set.add(format2Month((Date) record.get("ts")));
        }
        return set;
    }

    /**
     * 格式化到天
     */
    public static String format2Month(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-01");
        return sdf.format(date);
    }

}
